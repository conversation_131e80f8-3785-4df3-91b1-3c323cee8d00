# Code Architecture Guidelines

## 📏 硬性指标（Must-Follow）

### ✅ 文件行数限制
- **动态语言**（如 Python、JavaScript、TypeScript）：
  - 每个代码文件 **不超过 200 行**
- **静态语言**（如 Java、Go、Rust）：
  - 每个代码文件 **不超过 250 行**

> 📌 *目的：提高可读性、可维护性，降低认知负担*

### ✅ 文件夹结构限制
- 每个文件夹中 **文件数量不超过 8 个**
- 若超过，需进行 **多层子文件夹拆分**

> 📌 *目的：提升结构清晰度，便于快速定位与扩展*

---

## 🧠 架构设计关注点（持续警惕）

以下“坏味道”会严重侵蚀代码质量，**必须时刻警惕并避免**：

### ❌ 1. 僵化（Rigidity）
> 系统难以变更，微小改动引发连锁反应

- **问题**：变更成本高，开发效率低
- **建议**：引入接口抽象、策略模式、依赖倒置原则等

### ❌ 2. 冗余（Redundancy）
> 相同逻辑重复出现，维护困难

- **问题**：代码膨胀，一致性差
- **建议**：提取公共函数或类，使用组合代替继承

### ❌ 3. 循环依赖（Circular Dependency）
> 模块相互依赖，形成“死结”

- **问题**：难以测试、复用与维护
- **建议**：使用接口解耦、事件机制、依赖注入等手段

### ❌ 4. 脆弱性（Fragility）
> 修改一处，导致看似无关部分出错

- **问题**：系统不稳定，回归问题频发
- **建议**：遵循单一职责原则、提高模块内聚性

### ❌ 5. 晦涩性（Obscurity）
> 代码结构混乱，意图不明

- **问题**：新人难以上手，协作困难
- **建议**：命名清晰、注释得当、结构简洁、文档完善

### ❌ 6. 数据泥团（Data Clump）
> 多个参数总是一起出现，暗示应封装为对象

- **问题**：函数参数臃肿，语义不清
- **建议**：封装为数据结构或值对象

### ❌ 7. 不必要的复杂性（Needless Complexity）
> 过度设计，小问题用大方案

- **问题**：理解成本高，维护难度大
- **建议**：遵循 YAGNI 原则，KISS 原则，按需设计

---

## 🚨 重要提醒

> **【非常重要】无论你是编写、阅读还是审核代码，都必须严格遵守上述硬性指标，并时刻关注架构设计质量。**

> **【非常重要】一旦发现“坏味道”，应立即提醒用户是否需要优化，并提供合理的优化建议。**