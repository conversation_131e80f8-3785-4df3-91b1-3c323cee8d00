import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Space, Statistic, Table, Tag } from 'tdesign-react';
import { FolderIcon, FileIcon, AddIcon, ViewListIcon } from 'tdesign-icons-react';
import { ProjectService, Project } from '../../../services/projects';
import { StoryboardService, Storyboard } from '../../../services/storyboards';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

const CMSDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    totalProjects: 0,
    totalStoryboards: 0,
    activeProjects: 0,
    recentProjects: [] as Project[],
    recentStoryboards: [] as Storyboard[],
  });

  // 获取统计数据
  const fetchStats = async () => {
    setLoading(true);
    try {
      // 获取项目统计
      const projectsResponse = await ProjectService.getList({ limit: 100 });
      const projects = projectsResponse.list;
      
      // 获取故事板统计
      const storyboardsResponse = await StoryboardService.getList({ limit: 100 });
      const storyboards = storyboardsResponse.list;
      
      // 获取最近的项目和故事板
      const recentProjectsResponse = await ProjectService.getList({ limit: 5 });
      const recentStoryboardsResponse = await StoryboardService.getList({ limit: 5 });
      
      setStats({
        totalProjects: projects.length,
        totalStoryboards: storyboards.length,
        activeProjects: projects.filter(p => p.status === 1).length,
        recentProjects: recentProjectsResponse.list,
        recentStoryboards: recentStoryboardsResponse.list,
      });
    } catch (error) {
      console.error('获取统计数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  // 项目表格列配置
  const projectColumns = [
    {
      title: '项目名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Project }) => {
        const statusMap = {
          0: { label: '禁用', color: 'default' },
          1: { label: '启用', color: 'success' },
          2: { label: '草稿', color: 'warning' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Project }) => dayjs(row.created_at).format('MM-DD HH:mm'),
    },
  ];

  // 故事板表格列配置
  const storyboardColumns = [
    {
      title: '故事板名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '所属项目',
      colKey: 'project_name',
      width: 150,
      cell: ({ row }: { row: Storyboard }) => row.project_name || '-',
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Storyboard }) => {
        const statusMap = {
          0: { label: '禁用', color: 'default' },
          1: { label: '启用', color: 'success' },
          2: { label: '草稿', color: 'warning' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Storyboard }) => dayjs(row.created_at).format('MM-DD HH:mm'),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>CMS 管理系统</h1>
        <p style={{ margin: '8px 0 0 0', color: '#666' }}>
          欢迎使用预演工具内容管理系统，管理您的项目和故事板
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card bordered={false}>
            <Statistic
              title="总项目数"
              value={stats.totalProjects}
              prefix={<FolderIcon />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card bordered={false}>
            <Statistic
              title="总故事板数"
              value={stats.totalStoryboards}
              prefix={<FileIcon />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card bordered={false}>
            <Statistic
              title="活跃项目"
              value={stats.activeProjects}
              prefix={<FolderIcon />}
              loading={loading}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card bordered={false}>
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Space direction="vertical" size="small">
                <Button
                  theme="primary"
                  icon={<AddIcon />}
                  onClick={() => navigate('/cms/projects')}
                >
                  管理项目
                </Button>
                <Button
                  variant="outline"
                  icon={<ViewListIcon />}
                  onClick={() => navigate('/cms/storyboards')}
                >
                  管理故事板
                </Button>
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 最近项目和故事板 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="最近项目" bordered={false}>
            <Table
              data={stats.recentProjects}
              columns={projectColumns}
              loading={loading}
              rowKey="id"
              size="small"
            />
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Button
                variant="text"
                onClick={() => navigate('/cms/projects')}
              >
                查看全部项目
              </Button>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近故事板" bordered={false}>
            <Table
              data={stats.recentStoryboards}
              columns={storyboardColumns}
              loading={loading}
              rowKey="id"
              size="small"
            />
            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Button
                variant="text"
                onClick={() => navigate('/cms/storyboards')}
              >
                查看全部故事板
              </Button>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" bordered={false} style={{ marginTop: '24px' }}>
        <Space size="large">
          <Button
            theme="primary"
            icon={<AddIcon />}
            onClick={() => navigate('/cms/projects')}
          >
            创建新项目
          </Button>
          <Button
            theme="default"
            icon={<AddIcon />}
            onClick={() => navigate('/cms/storyboards')}
          >
            创建故事板
          </Button>
          <Button
            variant="outline"
            icon={<ViewListIcon />}
            onClick={() => navigate('/cms/projects')}
          >
            浏览项目
          </Button>
          <Button
            variant="outline"
            icon={<ViewListIcon />}
            onClick={() => navigate('/cms/storyboards')}
          >
            浏览故事板
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default CMSDashboard;
