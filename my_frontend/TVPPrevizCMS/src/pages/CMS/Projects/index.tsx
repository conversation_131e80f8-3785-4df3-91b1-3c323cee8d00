import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  InputNumber,
  Pagination,
  Image,
  MessagePlugin,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import MultiAssetSelector from '../../../components/MultiAssetSelector';
import SourceFileManager from '../../../components/SourceFileManager';
import { ProjectService, Project, ProjectCreateData, ProjectUpdateData } from '../../../services/projects';
import { Asset } from '../../../services/assets';
import { buildThumbnailUrl } from '../../../utils/url';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const ProjectsPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);
  const currentUser = (userState.userInfo as any)?.name || 'admin';
  const currentUserId = `user_${currentUser}`;

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [form] = Form.useForm();

  // 监听缩略图字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);

  // 角色资产选择相关状态
  const [assetSelectorVisible, setAssetSelectorVisible] = useState(false);
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);

  // 查看项目相关状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingProject, setViewingProject] = useState<Project | null>(null);

  // 状态选项
  const statusOptions = [
    { label: '禁用', value: 0 },
    { label: '启用', value: 1 },
    { label: '草稿', value: 2 },
  ];

  // 获取项目列表
  const fetchProjects = async () => {
    setLoading(true);
    try {
      const response = await ProjectService.getList({
        page: currentPage,
        limit: pageSize,
        search: searchValue,
      });
      setProjects(response.list);
      setTotal(response.pagination.total);
    } catch (error) {
      message.error('获取项目列表失败');
      console.error('获取项目列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [currentPage, pageSize, searchValue]);

  // 当编辑项目改变时，更新表单值
  useEffect(() => {
    if (editingProject && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        name: editingProject.name,
        description: editingProject.description,
        status: editingProject.status,
        thumbnail_path: editingProject.thumbnail_path,
        episode: editingProject.episode,
        dialogue_group: editingProject.dialogue_group,
      });
    }
  }, [editingProject, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingProject(null);
    setSelectedAssets([]); // 清空选择的资产
    form.reset();
    // 使用 setTimeout 确保表单重置后再设置默认值
    setTimeout(() => {
      form.setFieldsValue({
        status: 1,
        creator_id: currentUserId, // 使用当前登录用户ID
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = async (project: Project) => {
    setModalType('edit');
    setEditingProject(project);

    // 获取项目详情（包含关联的资产）
    try {
      const projectDetail = await ProjectService.getById(project.id);
      setSelectedAssets(projectDetail.assets || []);
    } catch (error) {
      console.error('获取项目资产失败:', error);
      setSelectedAssets([]);
    }

    // 先重置表单，然后设置值
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        name: project.name,
        description: project.description,
        status: project.status,
        thumbnail_path: project.thumbnail_path,
        episode: project.episode,
        dialogue_group: project.dialogue_group,
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开角色资产选择器
  const handleOpenAssetSelector = () => {
    setAssetSelectorVisible(true);
  };

  // 确认选择角色资产
  const handleConfirmAssets = (assets: Asset[]) => {
    setSelectedAssets(assets);
  };

  // 移除角色资产
  const handleRemoveAsset = (assetId: string) => {
    setSelectedAssets(prev => prev.filter(asset => asset.id !== assetId));
  };

  // 打开查看弹窗
  const handleView = async (project: Project) => {
    try {
      // 获取项目详情（包含关联的资产）
      const projectDetail = await ProjectService.getById(project.id);
      setViewingProject(projectDetail);
      setViewModalVisible(true);
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败');
    }
  };

  // 移除了简单的下载函数，使用SourceFileManager组件替代

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        if (modalType === 'create') {
          const createData: ProjectCreateData = {
            name: values.name,
            description: values.description,
            creator_id: values.creator_id,
            status: values.status,
            thumbnail_path: values.thumbnail_path,
            episode: values.episode,
            dialogue_group: values.dialogue_group,
            assetIds: selectedAssets.map(asset => asset.id),
          };
          await ProjectService.create(createData);
          message.success('创建项目成功');
        } else {
          const updateData: ProjectUpdateData = {
            name: values.name,
            description: values.description,
            status: values.status,
            thumbnail_path: values.thumbnail_path,
            episode: values.episode,
            dialogue_group: values.dialogue_group,
            assetIds: selectedAssets.map(asset => asset.id),
          };
          // 移除了调试代码
          await ProjectService.update(editingProject!.id, updateData);
          message.success('更新项目成功');
        }

        setModalVisible(false);
        fetchProjects();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建项目失败' : '更新项目失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除项目
  const handleDelete = async (id: string) => {
    try {
      await ProjectService.delete(id);
      message.success('删除项目成功');
      fetchProjects();
    } catch (error) {
      message.error('删除项目失败');
      console.error('删除项目失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的项目');
      return;
    }
    
    try {
      await ProjectService.batchDelete(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchProjects();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Project }) => {
        const imageUrl = row.thumbnail_path
          ? buildThumbnailUrl(row.thumbnail_path)
          : '/images/default-thumbnail.svg';
        return (
          <Image
            src={imageUrl}
            style={{ width: '60px', height: '45px', borderRadius: '4px' }}
            fit="cover"
            error={
              <div style={{
                width: '60px',
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                无图片
              </div>
            }
          />
        );
      },
    },
    {
      title: '项目名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '描述',
      colKey: 'description',
      width: 300,
      cell: ({ row }: { row: Project }) => row.description || '-',
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Project }) => {
        const statusMap = {
          0: { label: '禁用', color: 'default' },
          1: { label: '启用', color: 'success' },
          2: { label: '草稿', color: 'warning' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '集数',
      colKey: 'episode',
      width: 80,
      cell: ({ row }: { row: Project }) => row.episode || '-',
    },
    {
      title: '故事板数量',
      colKey: 'storyboard_count',
      width: 120,
      cell: ({ row }: { row: Project }) => row.storyboard_count || 0,
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Project }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 200,
      cell: ({ row }: { row: Project }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="项目管理" bordered={false}>
        {/* 搜索栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索项目名称或描述"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '300px' }}
              suffixIcon={<SearchIcon />}
            />
            <Button onClick={() => handleSearch(searchValue)}>搜索</Button>
            <Button variant="outline" onClick={handleReset}>重置</Button>
          </Space>
        </div>

        {/* 操作栏 */}
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" icon={<AddIcon />} onClick={handleCreate}>
              新建项目
            </Button>
            <Popconfirm
              content="确定要批量删除选中的项目吗？"
              onConfirm={handleBatchDelete}
            >
              <Button
                theme="danger"
                variant="outline"
                disabled={selectedRowKeys.length === 0}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={projects}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建项目' : '编辑项目'}
        visible={modalVisible}
        onCancel={() => {
          console.log('Dialog onCancel triggered');
          setModalVisible(false);
        }}
        onClose={() => {
          console.log('Dialog onClose triggered');
          setModalVisible(false);
        }}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="项目名称"
            name="name"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </FormItem>
          
          <FormItem label="项目描述" name="description">
            <Textarea placeholder="请输入项目描述" rows={3} />
          </FormItem>
          
          {modalType === 'create' && (
            <FormItem
              label="创建者ID"
              name="creator_id"
              rules={[{ required: true, message: '请输入创建者ID' }]}
            >
              <Input placeholder="自动填充当前用户" disabled />
            </FormItem>
          )}
          
          <FormItem label="状态" name="status">
            <Select placeholder="请选择状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>
          
          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value) => form.setFieldsValue({ thumbnail_path: value })}
            />
          </FormItem>

          <FormItem label="角色资产">
            <div style={{
              border: '1px solid #e7e7e7',
              borderRadius: '6px',
              padding: '16px',
              backgroundColor: '#fff'
            }}>
              {selectedAssets.length > 0 ? (
                <div>
                  <div style={{
                    marginBottom: '12px',
                    fontSize: '14px',
                    fontWeight: 500
                  }}>
                    已选择 {selectedAssets.length} 个角色资产：
                  </div>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px',
                    marginBottom: '12px'
                  }}>
                    {selectedAssets.map(asset => (
                      <Tag
                        key={asset.id}
                        closable
                        onClose={() => handleRemoveAsset(asset.id)}
                      >
                        {asset.name}
                      </Tag>
                    ))}
                  </div>
                  <Button size="small" onClick={handleOpenAssetSelector}>
                    添加更多资产
                  </Button>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <div style={{
                    color: '#999',
                    marginBottom: '12px',
                    fontSize: '14px'
                  }}>
                    请选择角色资产
                  </div>
                  <Button onClick={handleOpenAssetSelector}>
                    选择角色资产
                  </Button>
                </div>
              )}
            </div>
          </FormItem>

          <FormItem label="集数" name="episode">
            <InputNumber placeholder="请输入集数" min={1} />
          </FormItem>
          
          <FormItem label="对话组" name="dialogue_group">
            <Input placeholder="请输入对话组" />
          </FormItem>
        </Form>
      </Dialog>

      {/* 角色资产选择弹窗 */}
      <MultiAssetSelector
        visible={assetSelectorVisible}
        onClose={() => setAssetSelectorVisible(false)}
        onConfirm={handleConfirmAssets}
        selectedAssets={selectedAssets}
        assetTypeId={1} // 角色模型类型
      />

      {/* 查看项目详情弹窗 */}
      <Dialog
        header="项目详情"
        visible={viewModalVisible}
        onClose={() => setViewModalVisible(false)}
        width={800}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
          </Space>
        }
      >
        {viewingProject && (
          <div style={{ padding: '16px 0' }}>
            {/* 项目基本信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 600 }}>
                基本信息
              </h3>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>项目名称：</strong>
                    <span>{viewingProject.name}</span>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>状态：</strong>
                    <Tag
                      theme={viewingProject.status === 1 ? 'success' : viewingProject.status === 2 ? 'warning' : 'default'}
                      style={{ marginLeft: '8px' }}
                    >
                      {statusOptions.find(opt => opt.value === viewingProject.status)?.label || '未知'}
                    </Tag>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>集数：</strong>
                    <span>{viewingProject.episode || '未设置'}</span>
                  </div>
                </div>

                <div>
                  <div style={{ marginBottom: '12px' }}>
                    <strong>创建时间：</strong>
                    <span>{dayjs(viewingProject.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>更新时间：</strong>
                    <span>{dayjs(viewingProject.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <strong>故事板数量：</strong>
                    <span>{viewingProject.storyboard_count || 0}</span>
                  </div>
                </div>
              </div>

              {viewingProject.description && (
                <div style={{ marginTop: '16px' }}>
                  <strong>项目描述：</strong>
                  <div style={{
                    marginTop: '8px',
                    padding: '12px',
                    backgroundColor: '#f8f8f8',
                    borderRadius: '4px',
                    lineHeight: '1.6'
                  }}>
                    {viewingProject.description}
                  </div>
                </div>
              )}

              {viewingProject.thumbnail_path && (
                <div style={{ marginTop: '16px' }}>
                  <strong>缩略图：</strong>
                  <div style={{ marginTop: '8px' }}>
                    <Image
                      src={buildThumbnailUrl(viewingProject.thumbnail_path)}
                      style={{
                        maxWidth: '200px',
                        maxHeight: '150px',
                        borderRadius: '4px'
                      }}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* 关联的角色资产 */}
            <div>
              <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 600 }}>
                关联的角色资产 ({viewingProject.assets?.length || 0})
              </h3>

              {viewingProject.assets && viewingProject.assets.length > 0 ? (
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
                  gap: '16px'
                }}>
                  {viewingProject.assets.map((asset: any) => (
                    <div
                      key={asset.id}
                      style={{
                        border: '1px solid #e7e7e7',
                        borderRadius: '6px',
                        padding: '16px',
                        backgroundColor: '#fff'
                      }}
                    >
                      {/* 资产缩略图 */}
                      <div style={{
                        textAlign: 'center',
                        marginBottom: '12px'
                      }}>
                        {asset.thumbnail_path ? (
                          <Image
                            src={buildThumbnailUrl(asset.thumbnail_path)}
                            style={{
                              width: '100%',
                              height: '120px',
                              objectFit: 'cover',
                              borderRadius: '4px'
                            }}
                          />
                        ) : (
                          <div style={{
                            width: '100%',
                            height: '120px',
                            backgroundColor: '#f5f5f5',
                            borderRadius: '4px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: '#999'
                          }}>
                            暂无预览图
                          </div>
                        )}
                      </div>

                      {/* 资产信息 */}
                      <div style={{ fontSize: '14px' }}>
                        <div style={{
                          fontWeight: 500,
                          marginBottom: '8px',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {asset.name}
                        </div>

                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          marginBottom: '8px'
                        }}>
                          <Tag size="small" variant="light">
                            角色模型
                          </Tag>
                          <span style={{ fontSize: '12px', color: '#666' }}>
                            {asset.source_file_size ?
                              `${(asset.source_file_size / 1024 / 1024).toFixed(2)} MB` :
                              '未知大小'
                            }
                          </span>
                        </div>

                        {/* 文件管理组件 */}
                        <div style={{ marginTop: '8px' }}>
                          <SourceFileManager
                            value={{
                              url: asset.source_file_url,
                              size: asset.source_file_size
                            }}
                            module="assets"
                            readOnly={true}
                            onChange={() => {}}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                  backgroundColor: '#f8f8f8',
                  borderRadius: '6px'
                }}>
                  暂无关联的角色资产
                </div>
              )}
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default ProjectsPage;
