import React, { useState, useEffect } from 'react';
import { buildThumbnailUrl } from '../../../utils/url';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
  Image,
} from 'tdesign-react';
import { SearchIcon, EditIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import { SequenceService, Sequence, SequenceUpdateData } from '../../../services/sequences';
import { SceneService, Scene } from '../../../services/scenes';
import { ProjectService, Project } from '../../../services/projects';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const SequencesPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [sequences, setSequences] = useState<Sequence[]>([]);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sceneFilter, setSceneFilter] = useState<string>('');
  const [projectFilter, setProjectFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<number | undefined>();

  // 编辑弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSequence, setEditingSequence] = useState<Sequence | null>(null);
  const [form] = Form.useForm();

  // 监听缩略图字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);

  // 查看弹窗状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingSequence, setViewingSequence] = useState<Sequence | null>(null);

  // 状态选项
  const statusOptions = [
    { value: 1, label: '规划中' },
    { value: 2, label: '拍摄中' },
    { value: 3, label: '已完成' },
    { value: 4, label: '需重拍' },
    { value: 5, label: '已废弃' },
  ];

  // 时间设定选项
  const timeOfDayOptions = [
    { value: '晨', label: '晨' },
    { value: '日', label: '日' },
    { value: '午', label: '午' },
    { value: '夜', label: '夜' },
  ];

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await ProjectService.getList({ limit: 100 });
      setProjects(response.list || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 获取场景列表
  const fetchScenes = async () => {
    try {
      const response = await SceneService.list({ limit: 100 });
      setScenes(response.data || []);
    } catch (error) {
      console.error('获取场景列表失败:', error);
    }
  };

  // 获取场次列表
  const fetchSequences = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
      };

      if (searchValue) {
        params.name = searchValue;
      }
      if (sceneFilter) {
        params.scene_id = sceneFilter;
      }
      if (projectFilter) {
        params.project_id = projectFilter;
      }
      if (statusFilter !== undefined) {
        params.status = statusFilter;
      }

      const response = await SequenceService.list(params);
      setSequences(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取场次列表失败');
      console.error('获取场次列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
    fetchScenes();
  }, []);

  useEffect(() => {
    fetchSequences();
  }, [currentPage, pageSize, searchValue, sceneFilter, projectFilter, statusFilter]);

  // 当编辑场次改变时，更新表单值
  useEffect(() => {
    if (editingSequence && modalVisible) {
      form.setFieldsValue({
        name: editingSequence.name,
        scene_id: editingSequence.scene_id,
        project_id: editingSequence.project_id,
        description: editingSequence.description,
        time_of_day: editingSequence.time_of_day,
        dialogue_group: editingSequence.dialogue_group,
        project_role: editingSequence.project_role,
        status: editingSequence.status,
        thumbnail_path: editingSequence.thumbnail_path,
      });
    }
  }, [editingSequence, modalVisible, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setSceneFilter('');
    setProjectFilter('');
    setStatusFilter(undefined);
    setCurrentPage(1);
  };

  // 打开查看弹窗
  const handleView = (sequence: Sequence) => {
    setViewingSequence(sequence);
    setViewModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (sequence: Sequence) => {
    setEditingSequence(sequence);
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        name: sequence.name,
        scene_id: sequence.scene_id,
        project_id: sequence.project_id,
        description: sequence.description,
        time_of_day: sequence.time_of_day,
        dialogue_group: sequence.dialogue_group,
        project_role: sequence.project_role,
        status: sequence.status,
        thumbnail_path: sequence.thumbnail_path,
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交编辑表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        // 处理元数据
        let metadata = null;
        if (values.metadata && values.metadata.trim()) {
          try {
            metadata = JSON.parse(values.metadata);
          } catch (e) {
            message.error('元数据格式不正确，请输入有效的JSON格式');
            return;
          }
        }

        const updateData: SequenceUpdateData = {
          name: values.name,
          description: values.description,
          time_of_day: values.time_of_day,
          dialogue_group: values.dialogue_group,
          project_role: values.project_role,
          status: values.status,
          project_id: values.project_id,
          thumbnail_path: values.thumbnail_path,
          metadata: metadata,
        };
        await SequenceService.update(editingSequence!.id, updateData);
        message.success('更新场次成功');

        setModalVisible(false);
        fetchSequences();
      }
    } catch (error) {
      message.error('更新场次失败');
      console.error('提交表单失败:', error);
    }
  };

  // 获取场景名称
  const getSceneName = (sceneId: string) => {
    const scene = scenes.find(s => s.id === sceneId);
    return scene?.name || sceneId;
  };

  // 获取项目名称
  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project?.name || projectId;
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Sequence }) => {
        const imageUrl = row.thumbnail_path
          ? (buildThumbnailUrl(row.thumbnail_path))
          : '/images/default-thumbnail.svg';
        return (
          <Image
            src={imageUrl}
            style={{ width: '60px', height: '45px', borderRadius: '4px' }}
            fit="cover"
            error={
              <div style={{
                width: '60px',
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                无图片
              </div>
            }
          />
        );
      },
    },
    {
      title: '场次名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '所属场景',
      colKey: 'scene_id',
      width: 150,
      cell: ({ row }: { row: Sequence }) => getSceneName(row.scene_id),
    },
    {
      title: '所属项目',
      colKey: 'project_id',
      width: 150,
      cell: ({ row }: { row: Sequence }) => getProjectName(row.project_id),
    },
    {
      title: '时间设定',
      colKey: 'time_of_day',
      width: 100,
      cell: ({ row }: { row: Sequence }) => row.time_of_day || '-',
    },
    {
      title: '对话组',
      colKey: 'dialogue_group',
      width: 120,
      cell: ({ row }: { row: Sequence }) => row.dialogue_group || '-',
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Sequence }) => {
        const statusMap = {
          1: { label: '规划中', color: 'warning' },
          2: { label: '拍摄中', color: 'primary' },
          3: { label: '已完成', color: 'success' },
          4: { label: '需重拍', color: 'danger' },
          5: { label: '已废弃', color: 'default' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Sequence }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 150,
      cell: ({ row }: { row: Sequence }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索场次名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="选择项目"
              value={projectFilter}
              onChange={(value) => setProjectFilter(value as string)}
              clearable
              style={{ width: '150px' }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="选择场景"
              value={sceneFilter}
              onChange={(value) => setSceneFilter(value as string)}
              clearable
              style={{ width: '150px' }}
            >
              {scenes.map(scene => (
                <Option key={scene.id} value={scene.id}>
                  {scene.name}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="状态"
              value={statusFilter}
              onChange={(value) => setStatusFilter(value as number)}
              clearable
              style={{ width: '100px' }}
            >
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        {/* 移除了新建和批量删除按钮 */}

        {/* 表格 */}
        <Table
          data={sequences}
          columns={columns}
          loading={loading}
          rowKey="id"
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 编辑弹窗 */}
      <Dialog
        header="编辑场次"
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onClose={() => setModalVisible(false)}
        width={700}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              保存
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="场次名称"
            name="name"
            rules={[{ required: true, message: '请输入场次名称' }]}
          >
            <Input placeholder="请输入场次名称" />
          </FormItem>

          <div style={{ display: 'flex', gap: '16px' }}>
            <FormItem
              label="所属项目"
              name="project_id"
              rules={[{ required: true, message: '请选择所属项目' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="请选择所属项目">
                {projects.map(project => (
                  <Option key={project.id} value={project.id}>
                    {project.name}
                  </Option>
                ))}
              </Select>
            </FormItem>

            <FormItem
              label="所属场景"
              name="scene_id"
              rules={[{ required: true, message: '请选择所属场景' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="请选择所属场景">
                {scenes.map(scene => (
                  <Option key={scene.id} value={scene.id}>
                    {scene.name}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </div>

          <FormItem label="场次描述" name="description">
            <Textarea placeholder="请输入场次描述" rows={3} />
          </FormItem>

          <div style={{ display: 'flex', gap: '16px' }}>
            <FormItem label="时间设定" name="time_of_day" style={{ flex: 1 }}>
              <Select placeholder="请选择时间设定">
                {timeOfDayOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </FormItem>

            <FormItem label="状态" name="status" style={{ flex: 1 }}>
              <Select placeholder="请选择状态">
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </div>

          <FormItem label="对话组" name="dialogue_group">
            <Input placeholder="请输入对话组" />
          </FormItem>

          <FormItem label="项目角色" name="project_role">
            <Input placeholder="请输入项目角色" />
          </FormItem>

          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value) => form.setFieldsValue({ thumbnail_path: value })}
            />
          </FormItem>

          <FormItem label="元数据" name="metadata">
            <Textarea
              placeholder="请输入JSON格式的元数据，例如：{&quot;key&quot;: &quot;value&quot;}"
              rows={4}
              onBlur={(value) => {
                // 验证JSON格式
                if (value && value.trim()) {
                  try {
                    JSON.parse(value);
                  } catch (e) {
                    message.error('元数据格式不正确，请输入有效的JSON格式');
                  }
                }
              }}
            />
          </FormItem>
        </Form>
      </Dialog>

      {/* 查看详情弹窗 */}
      <Dialog
        header="场次详情"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        onClose={() => setViewModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
        }
      >
        {viewingSequence && (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <strong>场次名称：</strong>
              <span>{viewingSequence.name}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>所属场景：</strong>
              <span>{getSceneName(viewingSequence.scene_id)}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>场次描述：</strong>
              <span>{viewingSequence.description || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>时间设定：</strong>
              <span>{viewingSequence.time_of_day || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>对话组：</strong>
              <span>{viewingSequence.dialogue_group || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>项目角色：</strong>
              <span>{viewingSequence.project_role || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>缩略图：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingSequence.thumbnail_path ? (
                  <Image
                    src={buildThumbnailUrl(viewingSequence.thumbnail_path)}
                    style={{ width: '120px', height: '90px', borderRadius: '4px' }}
                    fit="cover"
                    error={
                      <div style={{
                        width: '120px',
                        height: '90px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                        fontSize: '12px',
                        color: '#999'
                      }}>
                        图片加载失败
                      </div>
                    }
                  />
                ) : (
                  <div style={{
                    width: '120px',
                    height: '90px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#999'
                  }}>
                    无缩略图
                  </div>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>元数据：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingSequence.metadata ? (
                  <pre style={{
                    backgroundColor: '#f5f5f5',
                    padding: '12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    maxHeight: '200px',
                    overflow: 'auto',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all'
                  }}>
                    {typeof viewingSequence.metadata === 'string'
                      ? JSON.stringify(JSON.parse(viewingSequence.metadata), null, 2)
                      : JSON.stringify(viewingSequence.metadata, null, 2)
                    }
                  </pre>
                ) : (
                  <span style={{ color: '#999' }}>暂无元数据</span>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>状态：</strong>
              <span>
                {(() => {
                  const statusMap = {
                    1: '规划中',
                    2: '制作中',
                    3: '已完成',
                    4: '已废弃',
                  };
                  return statusMap[viewingSequence.status as keyof typeof statusMap] || '未知';
                })()}
              </span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>创建时间：</strong>
              <span>{dayjs(viewingSequence.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
            <div>
              <strong>更新时间：</strong>
              <span>{dayjs(viewingSequence.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default SequencesPage;
