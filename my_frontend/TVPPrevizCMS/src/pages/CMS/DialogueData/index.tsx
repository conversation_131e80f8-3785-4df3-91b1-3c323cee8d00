import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
  InputNumber,
  Switch,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon, SoundIcon } from 'tdesign-icons-react';
import { DialogueDataService, DialogueData, DialogueDataCreateData, DialogueDataUpdateData } from '../../../services/dialogueGroups';
import { DialogueGroupService, DialogueGroup } from '../../../services/dialogueGroups';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const DialogueDataPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);
  const currentUser = (userState.userInfo as any)?.name || 'admin';

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [dialogueData, setDialogueData] = useState<DialogueData[]>([]);
  const [dialogueGroups, setDialogueGroups] = useState<DialogueGroup[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [dialogueGroupFilter, setDialogueGroupFilter] = useState<string>('');
  const [characterFilter, setCharacterFilter] = useState<string>('');
  const [onlyUnusedFilter, setOnlyUnusedFilter] = useState<boolean>(false);

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingDialogueData, setEditingDialogueData] = useState<DialogueData | null>(null);
  const [form] = Form.useForm();

  // 性别选项
  const genderOptions = [
    { value: '男', label: '男' },
    { value: '女', label: '女' },
    { value: '其他', label: '其他' },
  ];

  // 情感选项
  const emotionOptions = [
    { value: '平静', label: '平静' },
    { value: '高兴', label: '高兴' },
    { value: '愤怒', label: '愤怒' },
    { value: '悲伤', label: '悲伤' },
    { value: '惊讶', label: '惊讶' },
    { value: '恐惧', label: '恐惧' },
    { value: '厌恶', label: '厌恶' },
  ];

  // 音调选项
  const intonationOptions = [
    { value: '正常', label: '正常' },
    { value: '上升', label: '上升' },
    { value: '下降', label: '下降' },
    { value: '疑问', label: '疑问' },
    { value: '感叹', label: '感叹' },
  ];

  // 获取对话组列表
  const fetchDialogueGroups = async () => {
    try {
      const response = await DialogueGroupService.list({ limit: 100 });
      setDialogueGroups(response.data || []);
    } catch (error) {
      console.error('获取对话组列表失败:', error);
    }
  };

  // 获取对话数据列表
  const fetchDialogueData = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
      };

      if (searchValue) {
        params.dialogue_text = searchValue;
      }
      if (dialogueGroupFilter) {
        params.dialogue_group_id = dialogueGroupFilter;
      }
      if (characterFilter) {
        params.character_name = characterFilter;
      }
      if (onlyUnusedFilter) {
        params.only_unused = true;
      }

      const response = await DialogueDataService.list(params);
      setDialogueData(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取对话数据列表失败');
      console.error('获取对话数据列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDialogueGroups();
  }, []);

  useEffect(() => {
    fetchDialogueData();
  }, [currentPage, pageSize, searchValue, dialogueGroupFilter, characterFilter, onlyUnusedFilter]);

  // 当编辑对话数据改变时，更新表单值
  useEffect(() => {
    if (editingDialogueData && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        dialogue_group_id: editingDialogueData.dialogue_group_id,
        character_name: editingDialogueData.character_name,
        gender: editingDialogueData.gender,
        dialogue_text: editingDialogueData.dialogue_text,
        emotion: editingDialogueData.emotion,
        intonation: editingDialogueData.intonation,
        volume: editingDialogueData.volume,
        speed: editingDialogueData.speed,
        generated_audio_file: editingDialogueData.generated_audio_file,
        duration: editingDialogueData.duration,
        used: editingDialogueData.used,
        needs_audio_regeneration: editingDialogueData.needs_audio_regeneration,
      });
    }
  }, [editingDialogueData, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setDialogueGroupFilter('');
    setCharacterFilter('');
    setOnlyUnusedFilter(false);
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingDialogueData(null);
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        volume: 1.0,
        speed: 1.0,
        emotion: '平静',
        intonation: '正常',
        gender: '男',
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (dialogueData: DialogueData) => {
    setModalType('edit');
    setEditingDialogueData(dialogueData);
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        dialogue_group_id: dialogueData.dialogue_group_id,
        character_name: dialogueData.character_name,
        gender: dialogueData.gender,
        dialogue_text: dialogueData.dialogue_text,
        emotion: dialogueData.emotion,
        intonation: dialogueData.intonation,
        volume: dialogueData.volume,
        speed: dialogueData.speed,
        generated_audio_file: dialogueData.generated_audio_file,
        duration: dialogueData.duration,
        used: dialogueData.used,
        needs_audio_regeneration: dialogueData.needs_audio_regeneration,
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        if (modalType === 'create') {
          const createData: DialogueDataCreateData = {
            dialogue_group_id: values.dialogue_group_id,
            character_name: values.character_name,
            gender: values.gender,
            dialogue_text: values.dialogue_text,
            emotion: values.emotion || '平静',
            intonation: values.intonation || '正常',
            volume: values.volume || 1.0,
            speed: values.speed || 1.0,
          };
          await DialogueDataService.create(createData);
          message.success('创建对话数据成功');
        } else {
          const updateData: DialogueDataUpdateData = {
            character_name: values.character_name,
            gender: values.gender,
            dialogue_text: values.dialogue_text,
            emotion: values.emotion,
            intonation: values.intonation,
            volume: values.volume,
            speed: values.speed,
            generated_audio_file: values.generated_audio_file,
            duration: values.duration,
            used: values.used,
            needs_audio_regeneration: values.needs_audio_regeneration,
          };
          await DialogueDataService.update(editingDialogueData!.id, updateData);
          message.success('更新对话数据成功');
        }

        setModalVisible(false);
        fetchDialogueData();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建对话数据失败' : '更新对话数据失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除对话数据
  const handleDelete = async (id: string) => {
    try {
      await DialogueDataService.delete(id);
      message.success('删除对话数据成功');
      fetchDialogueData();
    } catch (error) {
      message.error('删除对话数据失败');
      console.error('删除对话数据失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的对话数据');
      return;
    }

    try {
      await DialogueDataService.batchDelete(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchDialogueData();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 获取对话组名称
  const getDialogueGroupName = (dialogueGroupId: string) => {
    const dialogueGroup = dialogueGroups.find(dg => dg.id === dialogueGroupId);
    return dialogueGroup?.name || dialogueGroupId;
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '角色名称',
      colKey: 'character_name',
      width: 120,
    },
    {
      title: '对话文本',
      colKey: 'dialogue_text',
      width: 300,
      cell: ({ row }: { row: DialogueData }) => (
        <span title={row.dialogue_text} style={{ 
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {row.dialogue_text}
        </span>
      ),
    },
    {
      title: '所属对话组',
      colKey: 'dialogue_group_id',
      width: 150,
      cell: ({ row }: { row: DialogueData }) => getDialogueGroupName(row.dialogue_group_id),
    },
    {
      title: '性别',
      colKey: 'gender',
      width: 80,
    },
    {
      title: '情感',
      colKey: 'emotion',
      width: 80,
      cell: ({ row }: { row: DialogueData }) => row.emotion || '-',
    },
    {
      title: '时长(秒)',
      colKey: 'duration',
      width: 100,
      cell: ({ row }: { row: DialogueData }) => row.duration ? row.duration.toFixed(2) : '-',
    },
    {
      title: '状态',
      colKey: 'used',
      width: 100,
      cell: ({ row }: { row: DialogueData }) => (
        <Space>
          <Tag color={row.used ? 'success' : 'default'}>
            {row.used ? '已使用' : '未使用'}
          </Tag>
          {row.needs_audio_regeneration && (
            <Tag color="warning">需重新生成</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: DialogueData }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 250,
      cell: ({ row }: { row: DialogueData }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<SoundIcon />}
            onClick={() => {/* TODO: 播放音频 */}}
            disabled={!row.generated_audio_file}
          >
            播放
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => {/* TODO: 查看详情 */}}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这条对话数据吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索对话文本"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="选择对话组"
              value={dialogueGroupFilter}
              onChange={(value) => setDialogueGroupFilter(value as string)}
              clearable
              style={{ width: '150px' }}
            >
              {dialogueGroups.map(dialogueGroup => (
                <Option key={dialogueGroup.id} value={dialogueGroup.id}>
                  {dialogueGroup.name}
                </Option>
              ))}
            </Select>
            <Input
              placeholder="角色名称"
              value={characterFilter}
              onChange={setCharacterFilter}
              style={{ width: '120px' }}
            />
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>仅未使用:</span>
              <Switch
                value={onlyUnusedFilter}
                onChange={setOnlyUnusedFilter}
              />
            </div>
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" onClick={handleCreate} icon={<AddIcon />}>
              新建对话数据
            </Button>
            {selectedRowKeys.length === 0 ? (
              <Button
                theme="danger"
                disabled={true}
                icon={<DeleteIcon />}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            ) : (
              <Popconfirm
                content={`确定要删除选中的 ${selectedRowKeys.length} 条对话数据吗？`}
                onConfirm={handleBatchDelete}
              >
                <Button
                  theme="danger"
                  icon={<DeleteIcon />}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={dialogueData}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建对话数据' : '编辑对话数据'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onClose={() => setModalVisible(false)}
        width={700}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="所属对话组"
            name="dialogue_group_id"
            rules={[{ required: true, message: '请选择所属对话组' }]}
          >
            <Select placeholder="请选择所属对话组">
              {dialogueGroups.map(dialogueGroup => (
                <Option key={dialogueGroup.id} value={dialogueGroup.id}>
                  {dialogueGroup.name}
                </Option>
              ))}
            </Select>
          </FormItem>
          
          <div style={{ display: 'flex', gap: '16px' }}>
            <FormItem
              label="角色名称"
              name="character_name"
              rules={[{ required: true, message: '请输入角色名称' }]}
              style={{ flex: 1 }}
            >
              <Input placeholder="请输入角色名称" />
            </FormItem>
            
            <FormItem
              label="性别"
              name="gender"
              rules={[{ required: true, message: '请选择性别' }]}
              style={{ flex: 1 }}
            >
              <Select placeholder="请选择性别">
                {genderOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </div>
          
          <FormItem
            label="对话文本"
            name="dialogue_text"
            rules={[{ required: true, message: '请输入对话文本' }]}
          >
            <Textarea placeholder="请输入对话文本" rows={3} />
          </FormItem>
          
          <div style={{ display: 'flex', gap: '16px' }}>
            <FormItem label="情感" name="emotion" style={{ flex: 1 }}>
              <Select placeholder="请选择情感">
                {emotionOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </FormItem>
            
            <FormItem label="音调" name="intonation" style={{ flex: 1 }}>
              <Select placeholder="请选择音调">
                {intonationOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </FormItem>
          </div>
          
          <div style={{ display: 'flex', gap: '16px' }}>
            <FormItem label="音量" name="volume" style={{ flex: 1 }}>
              <InputNumber placeholder="音量 (0.1-2.0)" min={0.1} max={2.0} step={0.1} />
            </FormItem>
            
            <FormItem label="语速" name="speed" style={{ flex: 1 }}>
              <InputNumber placeholder="语速 (0.5-2.0)" min={0.5} max={2.0} step={0.1} />
            </FormItem>
          </div>
          
          {modalType === 'edit' && (
            <>
              <FormItem label="音频文件路径" name="generated_audio_file">
                <Input placeholder="生成的音频文件路径" />
              </FormItem>
              
              <FormItem label="音频时长(秒)" name="duration">
                <InputNumber placeholder="音频时长" min={0} step={0.1} />
              </FormItem>
              
              <div style={{ display: 'flex', gap: '16px' }}>
                <FormItem label="是否已使用" name="used" style={{ flex: 1 }}>
                  <Switch />
                </FormItem>
                
                <FormItem label="需要重新生成音频" name="needs_audio_regeneration" style={{ flex: 1 }}>
                  <Switch />
                </FormItem>
              </div>
            </>
          )}
        </Form>
      </Dialog>
    </div>
  );
};

export default DialogueDataPage;
