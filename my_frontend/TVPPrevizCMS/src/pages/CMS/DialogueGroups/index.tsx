import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import { DialogueGroupService, DialogueGroup, DialogueGroupCreateData, DialogueGroupUpdateData } from '../../../services/dialogueGroups';
import { ProjectService, Project } from '../../../services/projects';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const DialogueGroupsPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);
  const currentUser = (userState.userInfo as any)?.name || 'admin';

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [dialogueGroups, setDialogueGroups] = useState<DialogueGroup[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [projectFilter, setProjectFilter] = useState<string>('');

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingDialogueGroup, setEditingDialogueGroup] = useState<DialogueGroup | null>(null);
  const [form] = Form.useForm();

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      const response = await ProjectService.getList({ limit: 100 });
      setProjects(response.list || []);
    } catch (error) {
      console.error('获取项目列表失败:', error);
    }
  };

  // 获取对话组列表
  const fetchDialogueGroups = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
      };

      if (searchValue) {
        params.name = searchValue;
      }
      if (projectFilter) {
        params.project_id = projectFilter;
      }

      const response = await DialogueGroupService.list(params);
      setDialogueGroups(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取对话组列表失败');
      console.error('获取对话组列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  useEffect(() => {
    fetchDialogueGroups();
  }, [currentPage, pageSize, searchValue, projectFilter]);

  // 当编辑对话组改变时，更新表单值
  useEffect(() => {
    if (editingDialogueGroup && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        name: editingDialogueGroup.name,
        project_id: editingDialogueGroup.project_id,
        description: editingDialogueGroup.description,
      });
    }
  }, [editingDialogueGroup, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setProjectFilter('');
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingDialogueGroup(null);
    form.reset();
    setModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (dialogueGroup: DialogueGroup) => {
    setModalType('edit');
    setEditingDialogueGroup(dialogueGroup);
    form.reset();
    setTimeout(() => {
      form.setFieldsValue({
        name: dialogueGroup.name,
        project_id: dialogueGroup.project_id,
        description: dialogueGroup.description,
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        if (modalType === 'create') {
          const createData: DialogueGroupCreateData = {
            project_id: values.project_id,
            name: values.name,
            description: values.description || '',
          };
          await DialogueGroupService.create(createData);
          message.success('创建对话组成功');
        } else {
          const updateData: DialogueGroupUpdateData = {
            name: values.name,
            description: values.description,
          };
          await DialogueGroupService.update(editingDialogueGroup!.id, updateData);
          message.success('更新对话组成功');
        }

        setModalVisible(false);
        fetchDialogueGroups();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建对话组失败' : '更新对话组失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除对话组
  const handleDelete = async (id: string) => {
    try {
      await DialogueGroupService.delete(id);
      message.success('删除对话组成功');
      fetchDialogueGroups();
    } catch (error) {
      message.error('删除对话组失败');
      console.error('删除对话组失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的对话组');
      return;
    }

    try {
      await DialogueGroupService.batchDelete(selectedRowKeys);
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchDialogueGroups();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 获取项目名称
  const getProjectName = (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    return project?.name || projectId;
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '对话组名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '所属项目',
      colKey: 'project_id',
      width: 200,
      cell: ({ row }: { row: DialogueGroup }) => getProjectName(row.project_id),
    },
    {
      title: '描述',
      colKey: 'description',
      width: 300,
      cell: ({ row }: { row: DialogueGroup }) => row.description || '-',
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: DialogueGroup }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '更新时间',
      colKey: 'updated_at',
      width: 180,
      cell: ({ row }: { row: DialogueGroup }) => dayjs(row.updated_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 250,
      cell: ({ row }: { row: DialogueGroup }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => {/* TODO: 查看对话组详情 */}}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            onClick={() => {/* TODO: 管理对话数据 */}}
          >
            对话数据
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个对话组吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索对话组名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="选择项目"
              value={projectFilter}
              onChange={(value) => setProjectFilter(value as string)}
              clearable
              style={{ width: '200px' }}
            >
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" onClick={handleCreate} icon={<AddIcon />}>
              新建对话组
            </Button>
            {selectedRowKeys.length === 0 ? (
              <Button
                theme="danger"
                disabled={true}
                icon={<DeleteIcon />}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            ) : (
              <Popconfirm
                content={`确定要删除选中的 ${selectedRowKeys.length} 个对话组吗？`}
                onConfirm={handleBatchDelete}
              >
                <Button
                  theme="danger"
                  icon={<DeleteIcon />}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={dialogueGroups}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建对话组' : '编辑对话组'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        onClose={() => setModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="对话组名称"
            name="name"
            rules={[{ required: true, message: '请输入对话组名称' }]}
          >
            <Input placeholder="请输入对话组名称" />
          </FormItem>
          
          <FormItem
            label="所属项目"
            name="project_id"
            rules={[{ required: true, message: '请选择所属项目' }]}
          >
            <Select placeholder="请选择所属项目">
              {projects.map(project => (
                <Option key={project.id} value={project.id}>
                  {project.name}
                </Option>
              ))}
            </Select>
          </FormItem>
          
          <FormItem label="对话组描述" name="description">
            <Textarea 
              placeholder="请输入对话组描述，如对话的场景、用途等" 
              rows={4} 
            />
          </FormItem>
        </Form>
      </Dialog>
    </div>
  );
};

export default DialogueGroupsPage;
