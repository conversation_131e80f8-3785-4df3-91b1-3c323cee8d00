import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tag,
  Popconfirm,
  message,
  Dialog,
  Form,
  Select,
  Textarea,
  Pagination,
  Image,
} from 'tdesign-react';
import { SearchIcon, AddIcon, EditIcon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import ThumbnailUpload from '../../../components/ThumbnailUpload';
import SourceFileManager from '../../../components/SourceFileManager';
import { AssetService, Asset, AssetCreateData, AssetUpdateData } from '../../../services/assets';
import { buildThumbnailUrl } from '../../../utils/url';
import { FileUploadService } from '../../../services/fileUpload';
import { useAppSelector, useAppDispatch } from '../../../modules/store';
import { getUserInfo } from '../../../modules/user';
import dayjs from 'dayjs';

const { FormItem } = Form;
const { Option } = Select;

const AssetsPage: React.FC = () => {
  // 获取用户信息
  const dispatch = useAppDispatch();
  const userState = useAppSelector((state) => state.user);
  const currentUser = (userState.userInfo as any)?.name || 'admin';
  const currentUserId = `user_${currentUser}`;

  // 确保用户信息已加载
  useEffect(() => {
    if (!userState.userInfo || Object.keys(userState.userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, userState.userInfo]);

  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [typeFilter, setTypeFilter] = useState<number | undefined>();
  const [statusFilter, setStatusFilter] = useState<number | undefined>();

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'create' | 'edit'>('create');
  const [editingAsset, setEditingAsset] = useState<Asset | null>(null);
  const [form] = Form.useForm();
  const [uploadTaskId, setUploadTaskId] = useState<string | null>(null);

  // 监听字段值变化
  const thumbnailPath = Form.useWatch('thumbnail_path', form);
  const sourceFileUrl = Form.useWatch('source_file_url', form);
  const sourceFileSize = Form.useWatch('source_file_size', form);

  // 查看弹窗状态
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingAsset, setViewingAsset] = useState<Asset | null>(null);

  // 资产类型选项
  const assetTypeOptions = [
    { value: 1, label: '角色模型' },
    { value: 2, label: '场景模型' },
    { value: 3, label: '道具模型' },
    { value: 4, label: '音频文件' },
    { value: 5, label: '图片文件' },
    { value: 6, label: '其他' },
  ];

  // 状态选项
  const statusOptions = [
    { value: 1, label: '可用' },
    { value: 2, label: '废弃' },
  ];

  // 获取资产列表
  const fetchAssets = async () => {
    try {
      setLoading(true);
      const offset = (currentPage - 1) * pageSize;
      const params: any = {
        offset,
        limit: pageSize,
      };

      if (searchValue) {
        params.name = searchValue;
      }
      if (typeFilter !== undefined) {
        params.type_id = typeFilter;
      }
      if (statusFilter !== undefined) {
        params.status = statusFilter;
      }

      const response = await AssetService.list(params);
      setAssets(response.data || []);
      setTotal(response.total || 0);
    } catch (error) {
      message.error('获取资产列表失败');
      console.error('获取资产列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAssets();
  }, [currentPage, pageSize, searchValue, typeFilter, statusFilter]);

  // 当编辑资产改变时，更新表单值
  useEffect(() => {
    if (editingAsset && modalVisible && modalType === 'edit') {
      form.setFieldsValue({
        name: editingAsset.name,
        type_id: editingAsset.type_id,
        path: editingAsset.path,
        metadata: editingAsset.metadata,
        status: editingAsset.status,
        thumbnail_path: editingAsset.thumbnail_path,
        source_file_url: editingAsset.source_file_url,
        source_file_size: editingAsset.source_file_size,
      });
    }
  }, [editingAsset, modalVisible, modalType, form]);

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
  };

  // 重置搜索
  const handleReset = () => {
    setSearchValue('');
    setTypeFilter(undefined);
    setStatusFilter(undefined);
    setCurrentPage(1);
  };

  // 打开创建弹窗
  const handleCreate = () => {
    setModalType('create');
    setEditingAsset(null);
    form.reset();
    // 使用 setTimeout 确保表单重置后再设置默认值
    setTimeout(() => {
      form.setFieldsValue({
        status: 1,
        creator_id: currentUserId,
        type_id: 1,
      });
    }, 0);
    setModalVisible(true);
  };

  // 打开查看弹窗
  const handleView = (asset: Asset) => {
    setViewingAsset(asset);
    setViewModalVisible(true);
  };

  // 打开编辑弹窗
  const handleEdit = (asset: any) => {
    setModalType('edit');
    setEditingAsset(asset);
    // 先重置表单，然后设置值
    form.reset();

    setTimeout(() => {
      form.setFieldsValue({
        name: asset.name,
        type_id: asset.type_id,
        metadata: asset.metadata,
        status: asset.status,
        thumbnail_path: asset.thumbnail_path,
        source_file_url: asset.source_file_url,
        source_file_size: asset.source_file_size,
      });
    }, 0);
    setModalVisible(true);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const validateResult = await form.validate();
      if (validateResult === true) {
        const values = form.getFieldsValue(true);

        let recordId: string;

        if (modalType === 'create') {
          const createData: AssetCreateData = {
            name: values.name,
            type_id: values.type_id,
            creator_id: values.creator_id,
            metadata: values.metadata || '',
            thumbnail_path: values.thumbnail_path || '',
            source_file_url: values.source_file_url,
            source_file_size: values.source_file_size,
          };
          const createdAsset = await AssetService.create(createData);
          recordId = createdAsset.id;
          message.success('创建资产成功');
        } else {
          const updateData: AssetUpdateData = {
            name: values.name,
            type_id: values.type_id,
            metadata: values.metadata,
            status: values.status,
            thumbnail_path: values.thumbnail_path,
            source_file_url: values.source_file_url,
            source_file_size: values.source_file_size,
          };
          await AssetService.update(editingAsset!.id, updateData);
          recordId = editingAsset!.id;
          message.success('更新资产成功');
        }

        // 如果有上传任务，更新业务表文件字段
        if (uploadTaskId) {
          try {
            await FileUploadService.updateBusinessFile(uploadTaskId, recordId);
          } catch (error) {
            console.error('更新文件字段失败:', error);
            // 不阻断主流程，只记录错误
          }
        }

        setModalVisible(false);
        fetchAssets();
      }
    } catch (error) {
      message.error(modalType === 'create' ? '创建资产失败' : '更新资产失败');
      console.error('提交表单失败:', error);
    }
  };

  // 删除资产
  const handleDelete = async (id: string) => {
    try {
      await AssetService.delete(id);
      message.success('删除资产成功');
      fetchAssets();
    } catch (error) {
      message.error('删除资产失败');
      console.error('删除资产失败:', error);
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的资产');
      return;
    }

    try {
      await Promise.all(selectedRowKeys.map(id => AssetService.delete(id)));
      message.success('批量删除成功');
      setSelectedRowKeys([]);
      fetchAssets();
    } catch (error) {
      message.error('批量删除失败');
      console.error('批量删除失败:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      colKey: 'row-select',
      type: 'multiple' as const,
      width: 50,
    },
    {
      title: '缩略图',
      colKey: 'thumbnail_path',
      width: 80,
      cell: ({ row }: { row: Asset }) => {
        const imageUrl = row.thumbnail_path
          ? buildThumbnailUrl(row.thumbnail_path)
          : '/images/default-thumbnail.svg';
        return (
          <Image
            src={imageUrl}
            style={{ width: '60px', height: '45px', borderRadius: '4px' }}
            fit="cover"
            error={
              <div style={{
                width: '60px',
                height: '45px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#999'
              }}>
                无图片
              </div>
            }
          />
        );
      },
    },
    {
      title: '资产名称',
      colKey: 'name',
      width: 200,
    },
    {
      title: '资产类型',
      colKey: 'type_name',
      width: 120,
      cell: ({ row }: { row: Asset }) => {
        const typeOption = assetTypeOptions.find(opt => opt.value === row.type_id);
        return typeOption?.label || row.type_name || '-';
      },
    },
    {
      title: '文件路径',
      colKey: 'source_file_url',
      width: 300,
      cell: ({ row }: { row: Asset }) => (
        <span title={row.source_file_url} style={{
          display: 'block',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }}>
          {row.source_file_url}
        </span>
      ),
    },
    {
      title: '状态',
      colKey: 'status',
      width: 100,
      cell: ({ row }: { row: Asset }) => {
        const statusMap = {
          1: { label: '可用', color: 'success' },
          2: { label: '废弃', color: 'default' },
        };
        const statusInfo = statusMap[row.status as keyof typeof statusMap] || { label: '未知', color: 'default' };
        return <Tag color={statusInfo.color}>{statusInfo.label}</Tag>;
      },
    },

    {
      title: '创建者',
      colKey: 'creator_id',
      width: 120,
      cell: ({ row }: { row: Asset }) => row.creator_id || '-',
    },
    {
      title: '创建时间',
      colKey: 'created_at',
      width: 180,
      cell: ({ row }: { row: Asset }) => dayjs(row.created_at).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      colKey: 'action',
      width: 200,
      cell: ({ row }: { row: Asset }) => (
        <Space>
          <Button
            size="small"
            variant="text"
            icon={<BrowseIcon />}
            onClick={() => handleView(row)}
          >
            查看
          </Button>
          <Button
            size="small"
            variant="text"
            icon={<EditIcon />}
            onClick={() => handleEdit(row)}
          >
            编辑
          </Button>
          <Popconfirm
            content="确定要删除这个资产吗？"
            onConfirm={() => handleDelete(row.id)}
          >
            <Button
              size="small"
              variant="text"
              theme="danger"
              icon={<DeleteIcon />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Input
              placeholder="搜索资产名称"
              value={searchValue}
              onChange={(value) => setSearchValue(value)}
              onEnter={() => handleSearch(searchValue)}
              style={{ width: '200px' }}
              suffixIcon={<SearchIcon />}
            />
            <Select
              placeholder="资产类型"
              value={typeFilter}
              onChange={(value) => setTypeFilter(value as number)}
              clearable
              style={{ width: '120px' }}
            >
              {assetTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Select
              placeholder="状态"
              value={statusFilter}
              onChange={(value) => setStatusFilter(value as number)}
              clearable
              style={{ width: '100px' }}
            >
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <Button onClick={() => handleSearch(searchValue)} icon={<SearchIcon />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Space>
            <Button theme="primary" onClick={handleCreate} icon={<AddIcon />}>
              新建资产
            </Button>
            <Popconfirm
              content={`确定要删除选中的 ${selectedRowKeys.length} 个资产吗？`}
              onConfirm={handleBatchDelete}
            >
              <Button
                theme="danger"
                disabled={selectedRowKeys.length === 0}
                icon={<DeleteIcon />}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </Space>
        </div>

        {/* 表格 */}
        <Table
          data={assets}
          columns={columns}
          loading={loading}
          rowKey="id"
          selectedRowKeys={selectedRowKeys}
          onSelectChange={(selectedRowKeys: (string | number)[]) => {
            setSelectedRowKeys(selectedRowKeys as string[]);
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            onChange={(pageInfo: { current: number; pageSize: number }) => {
              setCurrentPage(pageInfo.current);
              setPageSize(pageInfo.pageSize);
            }}
          />
          <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
            共 {total} 条记录
          </div>
        </div>
      </Card>

      {/* 创建/编辑弹窗 */}
      <Dialog
        header={modalType === 'create' ? '新建资产' : '编辑资产'}
        visible={modalVisible}
        onCancel={() => {
          console.log('Asset Dialog onCancel triggered');
          setModalVisible(false);
        }}
        onClose={() => {
          console.log('Asset Dialog onClose triggered');
          setModalVisible(false);
        }}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Space>
            <Button onClick={() => setModalVisible(false)}>取消</Button>
            <Button theme="primary" onClick={handleSubmit}>
              {modalType === 'create' ? '创建' : '保存'}
            </Button>
          </Space>
        }
      >
        <Form form={form} layout="vertical">
          <FormItem
            label="资产名称"
            name="name"
            rules={[{ required: true, message: '请输入资产名称' }]}
          >
            <Input placeholder="请输入资产名称" />
          </FormItem>
          
          <FormItem
            label="资产类型"
            name="type_id"
            rules={[{ required: true, message: '请选择资产类型' }]}
          >
            <Select placeholder="请选择资产类型">
              {assetTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>



          {modalType === 'create' && (
            <FormItem
              label="创建者ID"
              name="creator_id"
              rules={[{ required: true, message: '请输入创建者ID' }]}
            >
              <Input placeholder="自动填充当前用户" disabled />
            </FormItem>
          )}
          
          <FormItem
            label="文件路径"
            name="source_file_url"
          >
            <Input placeholder="文件路径将在上传后自动填充" disabled />
          </FormItem>

          <FormItem
            label="源文件管理"
            name="source_file"
            rules={modalType === 'create' ? [{ required: true, message: '请上传源文件' }] : []}
          >
            <SourceFileManager
              value={{
                url: editingAsset?.source_file_url || sourceFileUrl as string,
                size: editingAsset?.source_file_size || sourceFileSize as number
              }}
              module="assets"
              accept="*/*"
              placeholder="上传资产源文件（支持最大10GB）"
              onChange={(fileUrl, _fileKey, taskId) => {
                form.setFieldsValue({
                  source_file: fileUrl,
                  source_file_url: fileUrl
                });
                if (taskId) {
                  setUploadTaskId(taskId);
                }
              }}
            />
          </FormItem>
          
          <FormItem label="缩略图" name="thumbnail_path">
            <ThumbnailUpload
              value={thumbnailPath as string}
              onChange={(value) => form.setFieldsValue({ thumbnail_path: value })}
            />
          </FormItem>
          
          <FormItem label="状态" name="status">
            <Select placeholder="请选择状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </FormItem>
          
          <FormItem label="元数据" name="metadata">
            <Textarea 
              placeholder="请输入JSON格式的元数据" 
              rows={4} 
            />
          </FormItem>
        </Form>
      </Dialog>

      {/* 查看详情弹窗 */}
      <Dialog
        header="资产详情"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        onClose={() => setViewModalVisible(false)}
        width={600}
        closeBtn={true}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        footer={
          <Button onClick={() => setViewModalVisible(false)}>关闭</Button>
        }
      >
        {viewingAsset && (
          <div style={{ padding: '16px 0' }}>
            <div style={{ marginBottom: '16px' }}>
              <strong>资产名称：</strong>
              <span>{viewingAsset.name}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>资产类型：</strong>
              <span>
                {(() => {
                  const typeMap = {
                    1: '角色模型',
                    2: '场景模型',
                    3: '道具模型',
                    4: '材质贴图',
                    5: '动画文件',
                    6: '音频文件',
                    7: '视频文件',
                    8: '其他',
                  };
                  return typeMap[viewingAsset.type_id as keyof typeof typeMap] || '未知';
                })()}
              </span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>文件路径：</strong>
              <span>{viewingAsset.path || '-'}</span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>缩略图：</strong>
              <div style={{ marginTop: '8px' }}>
                {viewingAsset.thumbnail_path ? (
                  <Image
                    src={buildThumbnailUrl(viewingAsset.thumbnail_path)}
                    style={{ width: '120px', height: '90px', borderRadius: '4px' }}
                    fit="cover"
                    error={
                      <div style={{
                        width: '120px',
                        height: '90px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                        fontSize: '12px',
                        color: '#999'
                      }}>
                        图片加载失败
                      </div>
                    }
                  />
                ) : (
                  <div style={{
                    width: '120px',
                    height: '90px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: '#999'
                  }}>
                    暂无缩略图
                  </div>
                )}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>状态：</strong>
              <span>
                {(() => {
                  const statusMap = {
                    1: '可用',
                    2: '维护中',
                    3: '已废弃',
                  };
                  return statusMap[viewingAsset.status as keyof typeof statusMap] || '未知';
                })()}
              </span>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>源文件：</strong>
              {viewingAsset.source_file_url ? (
                <SourceFileManager
                  value={{
                    url: viewingAsset.source_file_url,
                    size: viewingAsset.source_file_size
                  }}
                  module="assets"
                  readOnly={true}
                  onChange={() => {}}
                />
              ) : (
                <span style={{ color: '#999' }}>未上传源文件</span>
              )}
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>元数据：</strong>
              <pre style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                fontSize: '12px',
                lineHeight: '1.4',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {typeof viewingAsset.metadata === 'object'
                  ? JSON.stringify(viewingAsset.metadata, null, 2)
                  : viewingAsset.metadata || '无'}
              </pre>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <strong>创建时间：</strong>
              <span>{dayjs(viewingAsset.created_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
            <div>
              <strong>更新时间：</strong>
              <span>{dayjs(viewingAsset.updated_at).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  );
};

export default AssetsPage;
