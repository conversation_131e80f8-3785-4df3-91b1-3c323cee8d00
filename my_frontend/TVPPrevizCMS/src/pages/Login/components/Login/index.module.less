.itemContainer {
  width: 400px;
  margin-top: 48px;
}

.checkContainer {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--td-text-color-secondary);

  &.rememberPwd {
    margin-bottom: 16px;
    justify-content: space-between;
  }

  :global {
    .t-checkbox__label {
      color: var(--td-text-color-secondary);
    }

    span {
      color: var(--td-brand-color);

      &:hover {
        cursor: pointer;
      }
    }
  }
}

.tipContainer {
  width: 192px;
  margin-bottom: 16px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;

  :global {
    .tip {
      color: var(--td-text-color-primary);
    }

    .refresh {
      display: flex;
      align-items: center;
      color: var(--td-brand-color);

      .t-icon {
        font-size: 14px;
        margin-left: 4px;
      }

      &:hover {
        cursor: pointer;
      }
    }
  }
}

.checkContainerTip {
  float: right;
  font-size: 14px;
  color: var(--td-brand-color);
}

.verificationBtn {
  flex-shrink: 0;
  width: 102px;
  height: 40px;
  margin-left: 11px;
}

.btnContainer {
  margin-top: 48px;
}

.switchContainer {
  margin-top: 24px;

  :global {
    .tip {
      font-size: 14px;
      color: var(--td-brand-color);
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      margin-right: 14px;

      &:not(:last-child) {
        &::after {
          content: '';
          display: block;
          width: 1px;
          height: 12px;
          background: var(--td-gray-color-3);
          margin-left: 14px;
        }
      }
    }
  }
}
