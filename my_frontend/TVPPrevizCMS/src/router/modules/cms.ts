import { lazy } from 'react';
import {
  SettingIcon,
  FolderIcon,
  DashboardIcon,
  FileIcon,
  ImageIcon,
  ViewModuleIcon,
  VideoIcon,
  SoundIcon,
  ChatIcon
} from 'tdesign-icons-react';
import { IRouter } from '../index';

const cmsRoutes: IRouter[] = [
  {
    path: '/cms',
    meta: {
      title: '项目资产管理',
      Icon: SettingIcon,
    },
    children: [
      {
        path: 'dashboard',
        Component: lazy(() => import('../../pages/CMS/Dashboard')),
        meta: {
          title: 'CMS首页',
          Icon: DashboardIcon,
        },
      },
      {
        path: 'projects',
        Component: lazy(() => import('../../pages/CMS/Projects')),
        meta: {
          title: '项目管理',
          Icon: FolderIcon,
        },
      },
      {
        path: 'storyboards',
        Component: lazy(() => import('../../pages/CMS/Storyboards')),
        meta: {
          title: '故事板管理',
          Icon: FileIcon,
        },
      },
      {
        path: 'assets',
        Component: lazy(() => import('../../pages/CMS/Assets')),
        meta: {
          title: '资产管理',
          Icon: ImageIcon,
        },
      },
      {
        path: 'scenes',
        Component: lazy(() => import('../../pages/CMS/Scenes')),
        meta: {
          title: '场景管理',
          Icon: ViewModuleIcon,
        },
      },
      {
        path: 'sequences',
        Component: lazy(() => import('../../pages/CMS/Sequences')),
        meta: {
          title: '场次管理',
          Icon: VideoIcon,
        },
      },
      {
        path: 'dialogue-groups',
        Component: lazy(() => import('../../pages/CMS/DialogueGroups')),
        meta: {
          title: '对话组管理',
          Icon: ChatIcon,
        },
      },
      {
        path: 'dialogue-data',
        Component: lazy(() => import('../../pages/CMS/DialogueData')),
        meta: {
          title: '对话数据',
          Icon: SoundIcon,
        },
      },
    ],
  },
];

export default cmsRoutes;
