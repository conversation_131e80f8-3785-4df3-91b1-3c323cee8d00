/**
 * URL工具函数
 * 根据环境自动检测并返回正确的基础URL
 */

// 检测当前运行环境
const detectEnvironment = () => {
  // Docker环境检测（通过端口判断）
  if (window.location.port === '8082') {
    return 'docker';
  }
  
  // 开发环境检测
  if (window.location.hostname === 'localhost' && window.location.port === '3001') {
    return 'development';
  }
  
  // 生产环境
  return 'production';
};

// 获取基础URL
const getBaseUrl = () => {
  const env = detectEnvironment();
  
  switch (env) {
    case 'docker':
      // Docker环境使用当前域名（通过Nginx代理）
      return window.location.origin;
    case 'development':
      // 开发环境直接连接后端
      return 'http://localhost:3002';
    default:
      // 生产环境使用当前域名
      return window.location.origin;
  }
};

// 获取API基础URL
export const getApiBaseUrl = () => {
  const env = detectEnvironment();
  
  switch (env) {
    case 'docker':
      return '/api';
    case 'development':
      return 'http://localhost:3002/api';
    default:
      return '/api';
  }
};

// 构建完整的文件URL
export const buildFileUrl = (path: string): string => {
  if (!path) return '';
  
  // 如果已经是完整的HTTP URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }
  
  const baseUrl = getBaseUrl();
  
  // 如果是相对路径（以/开头），拼接基础URL
  if (path.startsWith('/')) {
    return `${baseUrl}${path}`;
  }
  
  // 如果是云存储路径（不以/开头），添加https协议
  return `https://${path}`;
};

// 构建缩略图URL
export const buildThumbnailUrl = (thumbnailPath: string): string => {
  return buildFileUrl(thumbnailPath);
};

// 构建上传API URL
export const buildUploadUrl = (endpoint: string): string => {
  const apiBaseUrl = getApiBaseUrl();
  
  // 如果是相对路径的API端点
  if (endpoint.startsWith('/')) {
    return `${apiBaseUrl}${endpoint}`;
  }
  
  return `${apiBaseUrl}/${endpoint}`;
};

// 获取当前环境信息（用于调试）
export const getEnvironmentInfo = () => {
  const env = detectEnvironment();
  const baseUrl = getBaseUrl();
  const apiBaseUrl = getApiBaseUrl();
  
  return {
    environment: env,
    baseUrl,
    apiBaseUrl,
    location: {
      hostname: window.location.hostname,
      port: window.location.port,
      origin: window.location.origin
    }
  };
}; 