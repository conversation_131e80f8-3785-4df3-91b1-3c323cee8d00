import { instance } from '../utils/request';

// 资产类型定义
export interface Asset {
  id: string;
  name: string;
  type_id: number;
  type_name: string;
  creator_id: string;
  path: string;
  metadata: string;
  status: number;
  created_at: string;
  updated_at: string;
  thumbnail_path: string;
  source_file_url?: string;
  source_file_size?: number;
}

// 资产版本类型定义
export interface AssetVersion {
  id: string;
  asset_id: string;
  version_number: string;
  path: string;
  metadata: string;
  is_latest: boolean;
  created_at: string;
  updated_at: string;
  thumbnail_path: string;
}

// 创建资产请求类型
export interface AssetCreateData {
  name: string;
  type_id: number;
  creator_id: string;
  metadata?: string;
  thumbnail_path?: string;
  source_file_url: string;
  source_file_size?: number;
}

// 更新资产请求类型
export interface AssetUpdateData {
  name?: string;
  type_id?: number;
  metadata?: string;
  status?: number;
  thumbnail_path?: string;
  source_file_url?: string;
  source_file_size?: number;
}

// 创建资产版本请求类型
export interface AssetVersionCreateData {
  asset_id: string;
  version_number: string;
  path: string;
  metadata?: string;
  thumbnail_path?: string;
}

// 资产服务类
export class AssetService {
  // 获取资产列表
  static async list(params: {
    offset?: number;
    limit?: number;
    type_id?: number;
    creator_id?: string;
    status?: number;
    name?: string;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取资产详情
  static async getById(id: string) {
    const response = await instance.get(`/assets/${id}`);
    return response.data;
  }

  // 创建资产
  static async create(data: AssetCreateData) {
    const response = await instance.post('/assets', data);
    return response.data;
  }

  // 更新资产
  static async update(id: string, data: AssetUpdateData) {
    const response = await instance.put(`/assets/${id}`, data);
    return response.data;
  }

  // 删除资产
  static async delete(id: string) {
    const response = await instance.delete(`/assets/${id}`);
    return response.data;
  }

  // 获取资产版本列表
  static async getVersions(assetId: string, params: {
    offset?: number;
    limit?: number;
  } = {}) {
    const { offset = 0, limit = 10 } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
    });

    const response = await instance.get(`/assets/${assetId}/versions?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 创建资产版本
  static async createVersion(data: AssetVersionCreateData) {
    const response = await instance.post('/asset-versions', data);
    return response.data;
  }

  // 获取资产版本详情
  static async getVersionById(versionId: string) {
    const response = await instance.get(`/asset-versions/${versionId}`);
    return response.data;
  }

  // 设置为最新版本
  static async setLatestVersion(versionId: string) {
    const response = await instance.put(`/asset-versions/${versionId}/set-latest`);
    return response.data;
  }

  // 删除资产版本
  static async deleteVersion(versionId: string) {
    const response = await instance.delete(`/asset-versions/${versionId}`);
    return response.data;
  }

  // 获取资产类型列表
  static async getAssetTypes() {
    const response = await instance.get('/asset-types');
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 场景资产管理
  static async addToScene(sceneId: string, assetId: string, order: number = 0) {
    const response = await instance.post('/scene-assets', {
      scene_id: sceneId,
      asset_id: assetId,
      asset_order: order,
    });
    return response.data;
  }

  static async removeFromScene(sceneId: string, assetId: string) {
    const response = await instance.delete(`/scene-assets/${sceneId}/${assetId}`);
    return response.data;
  }

  static async getSceneAssets(sceneId: string, params: {
    offset?: number;
    limit?: number;
    asset_type_id?: number;
  } = {}) {
    const { offset = 0, limit = 10, asset_type_id } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(asset_type_id && { asset_type_id: asset_type_id.toString() }),
    });

    const response = await instance.get(`/scenes/${sceneId}/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 场次资产管理
  static async addToSequence(sequenceId: string, assetId: string, order: number = 0) {
    const response = await instance.post('/sequence-assets', {
      sequence_id: sequenceId,
      asset_id: assetId,
      asset_order: order,
    });
    return response.data;
  }

  static async removeFromSequence(sequenceId: string, assetId: string) {
    const response = await instance.delete(`/sequence-assets/${sequenceId}/${assetId}`);
    return response.data;
  }

  static async getSequenceAssets(sequenceId: string, params: {
    offset?: number;
    limit?: number;
    asset_type_id?: number;
  } = {}) {
    const { offset = 0, limit = 10, asset_type_id } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(asset_type_id && { asset_type_id: asset_type_id.toString() }),
    });

    const response = await instance.get(`/sequences/${sequenceId}/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 镜头资产管理
  static async addToShot(shotId: string, assetId: string, order: number = 0) {
    const response = await instance.post('/shot-assets', {
      shot_id: shotId,
      asset_id: assetId,
      asset_order: order,
    });
    return response.data;
  }

  static async removeFromShot(shotId: string, assetId: string) {
    const response = await instance.delete(`/shot-assets/${shotId}/${assetId}`);
    return response.data;
  }

  static async getShotAssets(shotId: string, params: {
    offset?: number;
    limit?: number;
    asset_type_id?: number;
  } = {}) {
    const { offset = 0, limit = 10, asset_type_id } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(asset_type_id && { asset_type_id: asset_type_id.toString() }),
    });

    const response = await instance.get(`/shots/${shotId}/assets?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }
}
