import { instance } from '../utils/request';

export interface Storyboard {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  content?: any;
  thumbnail_path?: string;
  metadata?: any;
  status: number;
  created_at: string;
  updated_at: string;
  project_name?: string;
}

export interface StoryboardCreateData {
  project_id: string;
  name: string;
  description?: string;
  content?: any;
  thumbnail_path?: string;
  metadata?: any;
  status?: number;
}

export interface StoryboardUpdateData {
  name?: string;
  description?: string;
  content?: any;
  thumbnail_path?: string;
  metadata?: any;
  status?: number;
}

export interface StoryboardListParams {
  page?: number;
  limit?: number;
  search?: string;
  projectId?: string;
}

export interface StoryboardListResponse {
  list: Storyboard[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 故事板API服务
 */
export class StoryboardService {
  /**
   * 获取故事板列表
   */
  static async getList(params: StoryboardListParams = {}): Promise<StoryboardListResponse> {
    const { page = 1, limit = 10, ...filters } = params;
    const offset = (page - 1) * limit;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/storyboards?${queryParams}`);
    return {
      list: response.data?.list || [],
      pagination: response.data?.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * 获取故事板详情
   */
  static async getById(id: string): Promise<Storyboard> {
    const response = await instance.get(`/storyboards/${id}`);
    return response.data;
  }

  /**
   * 创建故事板
   */
  static async create(data: StoryboardCreateData): Promise<Storyboard> {
    const response = await instance.post('/storyboards', data);
    return response.data;
  }

  /**
   * 更新故事板
   */
  static async update(id: string, data: StoryboardUpdateData): Promise<Storyboard> {
    const response = await instance.put(`/storyboards/${id}`, data);
    return response.data;
  }

  /**
   * 删除故事板
   */
  static async delete(id: string): Promise<void> {
    await instance.delete(`/storyboards/${id}`);
  }

  /**
   * 批量删除故事板
   */
  static async batchDelete(ids: string[]): Promise<any> {
    const response = await instance.post('/storyboards/batch-delete', { ids });
    return response.data;
  }
}

export default StoryboardService;
