import { instance } from '../utils/request';
import SparkMD5 from 'spark-md5';

// 上传任务状态
export type UploadStatus = 'uploading' | 'completed' | 'failed' | 'cancelled';

// 上传任务信息
export interface UploadTask {
  id: string;
  upload_id: string;
  file_key: string;
  file_name: string;
  file_size: number;
  file_type: string;
  file_url?: string;
  module: string;
  status: UploadStatus;
  progress: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

// 初始化上传响应
export interface InitUploadResponse {
  taskId: string;
  uploadId: string;
  fileKey: string;
  bucket: string;
  region: string;
  isInstantUpload?: boolean;
  fileUrl?: string;
}

// 上传配置
export interface UploadConfig {
  maxFileSize: number;
  chunkSize: number;
  maxConcurrency: number;
  bucket: string;
  region: string;
}

// 分块信息
export interface ChunkInfo {
  PartNumber: number;
  ETag: string;
}

// 文件检查响应
export interface FileCheckResponse {
  exists: boolean;
  taskId?: string;
  fileUrl?: string;
  fileKey?: string;
  isInstantUpload?: boolean;
}

export class FileUploadService {
  /**
   * 计算文件MD5
   */
  static async calculateFileMD5(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const spark = new SparkMD5.ArrayBuffer();
      const fileReader = new FileReader();
      const chunkSize = 2097152; // 2MB chunks
      let currentChunk = 0;
      const chunks = Math.ceil(file.size / chunkSize);

      fileReader.onload = (e) => {
        if (e.target?.result) {
          spark.append(e.target.result as ArrayBuffer);
          currentChunk++;

          if (currentChunk < chunks) {
            loadNext();
          } else {
            resolve(spark.end());
          }
        }
      };

      fileReader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      const loadNext = () => {
        const start = currentChunk * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        fileReader.readAsArrayBuffer(file.slice(start, end));
      };

      loadNext();
    });
  }

  /**
   * 检查文件是否存在（秒传）
   */
  static async checkFileExists(
    fileMd5: string,
    fileSize: number,
    fileName: string,
    module: string
  ): Promise<FileCheckResponse> {
    const response = await instance.post('/file-upload/check', {
      fileMd5,
      fileSize,
      fileName,
      module
    });
    return response.data;
  }

  /**
   * 获取上传配置
   */
  static async getUploadConfig(): Promise<UploadConfig> {
    const response = await instance.get('/file-upload/config');
    return response.data;
  }

  /**
   * 初始化分块上传
   */
  static async initMultipartUpload(
    fileName: string,
    fileSize: number,
    fileType: string,
    module: string,
    fileMd5?: string
  ): Promise<InitUploadResponse> {
    const response = await instance.post('/file-upload/init', {
      fileName,
      fileSize,
      fileType,
      module,
      fileMd5
    });
    return response.data;
  }

  /**
   * 获取分块上传签名
   */
  static async getUploadPartSignature(
    uploadId: string,
    fileKey: string,
    partNumber: number
  ): Promise<string> {
    const response = await instance.post('/file-upload/signature', {
      uploadId,
      fileKey,
      partNumber
    });
    return response.data.signedUrl;
  }

  /**
   * 上传分块
   */
  static async uploadPart(
    signedUrl: string,
    chunk: Blob
  ): Promise<string> {
    const response = await fetch(signedUrl, {
      method: 'PUT',
      body: chunk,
      headers: {
        'Content-Type': 'application/octet-stream'
      },
      mode: 'cors'
    });

    if (!response.ok) {
      throw new Error(`Upload part failed: ${response.status} ${response.statusText}`);
    }

    const etag = response.headers.get('ETag');
    if (!etag) {
      throw new Error('No ETag returned from upload');
    }

    return etag.replace(/"/g, ''); // 移除引号
  }

  /**
   * 完成分块上传
   */
  static async completeMultipartUpload(
    taskId: string,
    uploadId: string,
    fileKey: string,
    parts: ChunkInfo[]
  ): Promise<{ fileUrl: string; fileKey: string }> {
    const response = await instance.post('/file-upload/complete', {
      taskId,
      uploadId,
      fileKey,
      parts
    });
    return response.data;
  }

  /**
   * 取消分块上传
   */
  static async abortMultipartUpload(
    taskId: string,
    uploadId: string,
    fileKey: string
  ): Promise<void> {
    await instance.post('/file-upload/abort', {
      taskId,
      uploadId,
      fileKey
    });
  }

  /**
   * 获取上传任务状态
   */
  static async getUploadTaskStatus(taskId: string): Promise<UploadTask> {
    const response = await instance.get(`/file-upload/task/${taskId}`);
    return response.data;
  }

  /**
   * 分块上传文件
   */
  static async uploadFile(
    file: File,
    module: string,
    onProgress?: (progress: number) => void,
    onError?: (error: Error) => void
  ): Promise<{ fileUrl: string; fileKey: string; taskId: string }> {
    try {
      // 获取上传配置
      const config = await this.getUploadConfig();

      // 检查文件大小
      if (file.size > config.maxFileSize) {
        throw new Error(`文件大小不能超过 ${Math.round(config.maxFileSize / 1024 / 1024 / 1024)}GB`);
      }

      // 计算文件MD5
      onProgress?.(5); // 开始计算MD5
      const fileMd5 = await this.calculateFileMD5(file);
      onProgress?.(10); // MD5计算完成

      // 检查文件是否已存在（秒传）
      const checkResult = await this.checkFileExists(fileMd5, file.size, file.name, module);

      if (checkResult.exists && checkResult.taskId && checkResult.fileUrl && checkResult.fileKey) {
        // 文件已存在，直接返回秒传结果
        onProgress?.(100);
        return {
          fileUrl: checkResult.fileUrl,
          fileKey: checkResult.fileKey,
          taskId: checkResult.taskId
        };
      }

      // 初始化分块上传
      const initResult = await this.initMultipartUpload(
        file.name,
        file.size,
        file.type,
        module,
        fileMd5
      );

      // 如果初始化时就发现是秒传
      if (initResult.isInstantUpload && initResult.fileUrl && initResult.fileKey) {
        onProgress?.(100);
        return {
          fileUrl: initResult.fileUrl,
          fileKey: initResult.fileKey,
          taskId: initResult.taskId
        };
      }

      const { taskId, uploadId, fileKey } = initResult;
      const chunkSize = config.chunkSize;
      const totalChunks = Math.ceil(file.size / chunkSize);
      const parts: ChunkInfo[] = [];

      // 分块上传
      const uploadPromises: Promise<void>[] = [];
      let completedChunks = 0;

      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);
        const partNumber = i + 1;

        const uploadPromise = (async () => {
          try {
            // 获取签名URL
            const signedUrl = await this.getUploadPartSignature(
              uploadId,
              fileKey,
              partNumber
            );

            // 上传分块
            const etag = await this.uploadPart(signedUrl, chunk);
            
            parts[i] = { PartNumber: partNumber, ETag: etag };
            completedChunks++;

            // 更新进度（10%用于MD5计算，90%用于上传）
            const progress = Math.round(10 + (completedChunks / totalChunks) * 90);
            onProgress?.(progress);
          } catch (error) {
            console.error(`Upload part ${partNumber} failed:`, error);
            throw error;
          }
        })();

        uploadPromises.push(uploadPromise);

        // 控制并发数
        if (uploadPromises.length >= config.maxConcurrency) {
          await Promise.all(uploadPromises.splice(0, config.maxConcurrency));
        }
      }

      // 等待所有分块上传完成
      await Promise.all(uploadPromises);

      // 完成分块上传
      const result = await this.completeMultipartUpload(
        taskId,
        uploadId,
        fileKey,
        parts.sort((a, b) => a.PartNumber - b.PartNumber)
      );

      return {
        ...result,
        taskId
      };
    } catch (error) {
      onError?.(error as Error);
      throw error;
    }
  }

  /**
   * 更新业务表文件字段
   */
  static async updateBusinessFile(taskId: string, recordId: string): Promise<void> {
    await instance.post('/file-upload/update-business', {
      taskId,
      recordId
    });
  }
}
