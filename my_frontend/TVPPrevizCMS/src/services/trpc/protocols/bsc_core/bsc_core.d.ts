import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace trpc. */
export namespace trpc {

    /** Namespace video_media. */
    namespace video_media {

        /** Namespace bsc_core. */
        namespace bsc_core {

            /** Properties of a CommonRsp. */
            interface ICommonRsp {

                /** CommonRsp code */
                code?: (number|null);

                /** CommonRsp msg */
                msg?: (string|null);
            }

            /** Represents a CommonRsp. */
            class CommonRsp implements ICommonRsp {

                /**
                 * Constructs a new CommonRsp.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICommonRsp);

                /** CommonRsp code. */
                public code: number;

                /** CommonRsp msg. */
                public msg: string;

                /**
                 * Encodes the specified CommonRsp message. Does not implicitly {@link trpc.video_media.bsc_core.CommonRsp.verify|verify} messages.
                 * @param message CommonRsp message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICommonR<PERSON>, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CommonRsp message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CommonRsp
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CommonRsp;

                /**
                 * Verifies a CommonRsp message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CommonRsp message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CommonRsp
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CommonRsp;

                /**
                 * Creates a plain object from a CommonRsp message. Also converts values to other types if specified.
                 * @param message CommonRsp
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CommonRsp, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CommonRsp to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CommonRsp
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a Pagination. */
            interface IPagination {

                /** Pagination page */
                page?: (number|null);

                /** Pagination pageSize */
                pageSize?: (number|null);
            }

            /** Represents a Pagination. */
            class Pagination implements IPagination {

                /**
                 * Constructs a new Pagination.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IPagination);

                /** Pagination page. */
                public page: number;

                /** Pagination pageSize. */
                public pageSize: number;

                /**
                 * Encodes the specified Pagination message. Does not implicitly {@link trpc.video_media.bsc_core.Pagination.verify|verify} messages.
                 * @param message Pagination message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IPagination, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Pagination message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Pagination
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.Pagination;

                /**
                 * Verifies a Pagination message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Pagination message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Pagination
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.Pagination;

                /**
                 * Creates a plain object from a Pagination message. Also converts values to other types if specified.
                 * @param message Pagination
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.Pagination, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Pagination to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Pagination
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Sort enum. */
            enum Sort {
                DESC = 0,
                ASC = 1
            }

            /** FilterOperation enum. */
            enum FilterOperation {
                EQUAL = 0,
                IN = 1,
                BETWEEN = 2,
                LESS = 3,
                MORE = 4
            }

            /** Properties of a Filter. */
            interface IFilter {

                /** Filter field */
                field?: (string|null);

                /** Filter operation */
                operation?: (trpc.video_media.bsc_core.FilterOperation|null);

                /** Filter value */
                value?: (string|null);
            }

            /** Represents a Filter. */
            class Filter implements IFilter {

                /**
                 * Constructs a new Filter.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilter);

                /** Filter field. */
                public field: string;

                /** Filter operation. */
                public operation: trpc.video_media.bsc_core.FilterOperation;

                /** Filter value. */
                public value: string;

                /**
                 * Encodes the specified Filter message. Does not implicitly {@link trpc.video_media.bsc_core.Filter.verify|verify} messages.
                 * @param message Filter message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilter, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Filter message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Filter
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.Filter;

                /**
                 * Verifies a Filter message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Filter message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Filter
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.Filter;

                /**
                 * Creates a plain object from a Filter message. Also converts values to other types if specified.
                 * @param message Filter
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.Filter, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Filter to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Filter
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a PageInfo. */
            interface IPageInfo {

                /** PageInfo total */
                total?: (number|null);

                /** PageInfo page */
                page?: (number|null);

                /** PageInfo pageSize */
                pageSize?: (number|null);
            }

            /** Represents a PageInfo. */
            class PageInfo implements IPageInfo {

                /**
                 * Constructs a new PageInfo.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IPageInfo);

                /** PageInfo total. */
                public total: number;

                /** PageInfo page. */
                public page: number;

                /** PageInfo pageSize. */
                public pageSize: number;

                /**
                 * Encodes the specified PageInfo message. Does not implicitly {@link trpc.video_media.bsc_core.PageInfo.verify|verify} messages.
                 * @param message PageInfo message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IPageInfo, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a PageInfo message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns PageInfo
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.PageInfo;

                /**
                 * Verifies a PageInfo message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a PageInfo message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns PageInfo
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.PageInfo;

                /**
                 * Creates a plain object from a PageInfo message. Also converts values to other types if specified.
                 * @param message PageInfo
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.PageInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this PageInfo to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for PageInfo
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a Project. */
            interface IProject {

                /** Project id */
                id?: (string|null);

                /** Project name */
                name?: (string|null);

                /** Project description */
                description?: (string|null);
            }

            /** Represents a Project. */
            class Project implements IProject {

                /**
                 * Constructs a new Project.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IProject);

                /** Project id. */
                public id: string;

                /** Project name. */
                public name: string;

                /** Project description. */
                public description: string;

                /**
                 * Encodes the specified Project message. Does not implicitly {@link trpc.video_media.bsc_core.Project.verify|verify} messages.
                 * @param message Project message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IProject, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Project message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Project
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.Project;

                /**
                 * Verifies a Project message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Project message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Project
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.Project;

                /**
                 * Creates a plain object from a Project message. Also converts values to other types if specified.
                 * @param message Project
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.Project, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Project to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Project
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetProjectListRequest. */
            interface IGetProjectListRequest {

                /** GetProjectListRequest projectList */
                projectList?: (string[]|null);
            }

            /** Represents a GetProjectListRequest. */
            class GetProjectListRequest implements IGetProjectListRequest {

                /**
                 * Constructs a new GetProjectListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetProjectListRequest);

                /** GetProjectListRequest projectList. */
                public projectList: string[];

                /**
                 * Encodes the specified GetProjectListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetProjectListRequest.verify|verify} messages.
                 * @param message GetProjectListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetProjectListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetProjectListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetProjectListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetProjectListRequest;

                /**
                 * Verifies a GetProjectListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetProjectListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetProjectListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetProjectListRequest;

                /**
                 * Creates a plain object from a GetProjectListRequest message. Also converts values to other types if specified.
                 * @param message GetProjectListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetProjectListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetProjectListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetProjectListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetProjectListResponse. */
            interface IGetProjectListResponse {

                /** GetProjectListResponse projects */
                projects?: (trpc.video_media.bsc_core.IProject[]|null);

                /** GetProjectListResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a GetProjectListResponse. */
            class GetProjectListResponse implements IGetProjectListResponse {

                /**
                 * Constructs a new GetProjectListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetProjectListResponse);

                /** GetProjectListResponse projects. */
                public projects: trpc.video_media.bsc_core.IProject[];

                /** GetProjectListResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified GetProjectListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetProjectListResponse.verify|verify} messages.
                 * @param message GetProjectListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetProjectListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetProjectListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetProjectListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetProjectListResponse;

                /**
                 * Verifies a GetProjectListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetProjectListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetProjectListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetProjectListResponse;

                /**
                 * Creates a plain object from a GetProjectListResponse message. Also converts values to other types if specified.
                 * @param message GetProjectListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetProjectListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetProjectListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetProjectListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetProjectRequest. */
            interface IGetProjectRequest {

                /** GetProjectRequest project */
                project?: (string|null);
            }

            /** Represents a GetProjectRequest. */
            class GetProjectRequest implements IGetProjectRequest {

                /**
                 * Constructs a new GetProjectRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetProjectRequest);

                /** GetProjectRequest project. */
                public project: string;

                /**
                 * Encodes the specified GetProjectRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetProjectRequest.verify|verify} messages.
                 * @param message GetProjectRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetProjectRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetProjectRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetProjectRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetProjectRequest;

                /**
                 * Verifies a GetProjectRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetProjectRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetProjectRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetProjectRequest;

                /**
                 * Creates a plain object from a GetProjectRequest message. Also converts values to other types if specified.
                 * @param message GetProjectRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetProjectRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetProjectRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetProjectRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetProjectResponse. */
            interface IGetProjectResponse {

                /** GetProjectResponse project */
                project?: (trpc.video_media.bsc_core.IProject|null);

                /** GetProjectResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a GetProjectResponse. */
            class GetProjectResponse implements IGetProjectResponse {

                /**
                 * Constructs a new GetProjectResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetProjectResponse);

                /** GetProjectResponse project. */
                public project?: (trpc.video_media.bsc_core.IProject|null);

                /** GetProjectResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified GetProjectResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetProjectResponse.verify|verify} messages.
                 * @param message GetProjectResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetProjectResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetProjectResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetProjectResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetProjectResponse;

                /**
                 * Verifies a GetProjectResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetProjectResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetProjectResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetProjectResponse;

                /**
                 * Creates a plain object from a GetProjectResponse message. Also converts values to other types if specified.
                 * @param message GetProjectResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetProjectResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetProjectResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetProjectResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchProjectListRequest. */
            interface ISearchProjectListRequest {

                /** SearchProjectListRequest filters */
                filters?: (trpc.video_media.bsc_core.IFilter[]|null);

                /** SearchProjectListRequest pagination */
                pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchProjectListRequest sort */
                sort?: (trpc.video_media.bsc_core.Sort|null);
            }

            /** Represents a SearchProjectListRequest. */
            class SearchProjectListRequest implements ISearchProjectListRequest {

                /**
                 * Constructs a new SearchProjectListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchProjectListRequest);

                /** SearchProjectListRequest filters. */
                public filters: trpc.video_media.bsc_core.IFilter[];

                /** SearchProjectListRequest pagination. */
                public pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchProjectListRequest sort. */
                public sort: trpc.video_media.bsc_core.Sort;

                /**
                 * Encodes the specified SearchProjectListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.SearchProjectListRequest.verify|verify} messages.
                 * @param message SearchProjectListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchProjectListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchProjectListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchProjectListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchProjectListRequest;

                /**
                 * Verifies a SearchProjectListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchProjectListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchProjectListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchProjectListRequest;

                /**
                 * Creates a plain object from a SearchProjectListRequest message. Also converts values to other types if specified.
                 * @param message SearchProjectListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchProjectListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchProjectListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchProjectListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchProjectListResponse. */
            interface ISearchProjectListResponse {

                /** SearchProjectListResponse project */
                project?: (trpc.video_media.bsc_core.IProject|null);

                /** SearchProjectListResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a SearchProjectListResponse. */
            class SearchProjectListResponse implements ISearchProjectListResponse {

                /**
                 * Constructs a new SearchProjectListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchProjectListResponse);

                /** SearchProjectListResponse project. */
                public project?: (trpc.video_media.bsc_core.IProject|null);

                /** SearchProjectListResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified SearchProjectListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.SearchProjectListResponse.verify|verify} messages.
                 * @param message SearchProjectListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchProjectListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchProjectListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchProjectListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchProjectListResponse;

                /**
                 * Verifies a SearchProjectListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchProjectListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchProjectListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchProjectListResponse;

                /**
                 * Creates a plain object from a SearchProjectListResponse message. Also converts values to other types if specified.
                 * @param message SearchProjectListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchProjectListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchProjectListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchProjectListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateProjectRequest. */
            interface ICreateProjectRequest {

                /** CreateProjectRequest name */
                name?: (string|null);

                /** CreateProjectRequest desc */
                desc?: (string|null);
            }

            /** Represents a CreateProjectRequest. */
            class CreateProjectRequest implements ICreateProjectRequest {

                /**
                 * Constructs a new CreateProjectRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateProjectRequest);

                /** CreateProjectRequest name. */
                public name: string;

                /** CreateProjectRequest desc. */
                public desc: string;

                /**
                 * Encodes the specified CreateProjectRequest message. Does not implicitly {@link trpc.video_media.bsc_core.CreateProjectRequest.verify|verify} messages.
                 * @param message CreateProjectRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateProjectRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateProjectRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateProjectRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateProjectRequest;

                /**
                 * Verifies a CreateProjectRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateProjectRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateProjectRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateProjectRequest;

                /**
                 * Creates a plain object from a CreateProjectRequest message. Also converts values to other types if specified.
                 * @param message CreateProjectRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateProjectRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateProjectRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateProjectRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateProjectResponse. */
            interface ICreateProjectResponse {

                /** CreateProjectResponse projectId */
                projectId?: (string|null);

                /** CreateProjectResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a CreateProjectResponse. */
            class CreateProjectResponse implements ICreateProjectResponse {

                /**
                 * Constructs a new CreateProjectResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateProjectResponse);

                /** CreateProjectResponse projectId. */
                public projectId: string;

                /** CreateProjectResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified CreateProjectResponse message. Does not implicitly {@link trpc.video_media.bsc_core.CreateProjectResponse.verify|verify} messages.
                 * @param message CreateProjectResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateProjectResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateProjectResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateProjectResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateProjectResponse;

                /**
                 * Verifies a CreateProjectResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateProjectResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateProjectResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateProjectResponse;

                /**
                 * Creates a plain object from a CreateProjectResponse message. Also converts values to other types if specified.
                 * @param message CreateProjectResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateProjectResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateProjectResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateProjectResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateProjectRequest. */
            interface IUpdateProjectRequest {

                /** UpdateProjectRequest projectId */
                projectId?: (string|null);

                /** UpdateProjectRequest name */
                name?: (string|null);

                /** UpdateProjectRequest desc */
                desc?: (string|null);
            }

            /** Represents an UpdateProjectRequest. */
            class UpdateProjectRequest implements IUpdateProjectRequest {

                /**
                 * Constructs a new UpdateProjectRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IUpdateProjectRequest);

                /** UpdateProjectRequest projectId. */
                public projectId: string;

                /** UpdateProjectRequest name. */
                public name: string;

                /** UpdateProjectRequest desc. */
                public desc: string;

                /**
                 * Encodes the specified UpdateProjectRequest message. Does not implicitly {@link trpc.video_media.bsc_core.UpdateProjectRequest.verify|verify} messages.
                 * @param message UpdateProjectRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IUpdateProjectRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateProjectRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateProjectRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.UpdateProjectRequest;

                /**
                 * Verifies an UpdateProjectRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateProjectRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateProjectRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.UpdateProjectRequest;

                /**
                 * Creates a plain object from an UpdateProjectRequest message. Also converts values to other types if specified.
                 * @param message UpdateProjectRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.UpdateProjectRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateProjectRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateProjectRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateProjectResponse. */
            interface IUpdateProjectResponse {

                /** UpdateProjectResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents an UpdateProjectResponse. */
            class UpdateProjectResponse implements IUpdateProjectResponse {

                /**
                 * Constructs a new UpdateProjectResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IUpdateProjectResponse);

                /** UpdateProjectResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified UpdateProjectResponse message. Does not implicitly {@link trpc.video_media.bsc_core.UpdateProjectResponse.verify|verify} messages.
                 * @param message UpdateProjectResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IUpdateProjectResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateProjectResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateProjectResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.UpdateProjectResponse;

                /**
                 * Verifies an UpdateProjectResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateProjectResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateProjectResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.UpdateProjectResponse;

                /**
                 * Creates a plain object from an UpdateProjectResponse message. Also converts values to other types if specified.
                 * @param message UpdateProjectResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.UpdateProjectResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateProjectResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateProjectResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteProjectRequest. */
            interface IDeleteProjectRequest {

                /** DeleteProjectRequest projectId */
                projectId?: (string|null);
            }

            /** Represents a DeleteProjectRequest. */
            class DeleteProjectRequest implements IDeleteProjectRequest {

                /**
                 * Constructs a new DeleteProjectRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IDeleteProjectRequest);

                /** DeleteProjectRequest projectId. */
                public projectId: string;

                /**
                 * Encodes the specified DeleteProjectRequest message. Does not implicitly {@link trpc.video_media.bsc_core.DeleteProjectRequest.verify|verify} messages.
                 * @param message DeleteProjectRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IDeleteProjectRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteProjectRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteProjectRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.DeleteProjectRequest;

                /**
                 * Verifies a DeleteProjectRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteProjectRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteProjectRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.DeleteProjectRequest;

                /**
                 * Creates a plain object from a DeleteProjectRequest message. Also converts values to other types if specified.
                 * @param message DeleteProjectRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.DeleteProjectRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteProjectRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteProjectRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteProjectResponse. */
            interface IDeleteProjectResponse {

                /** DeleteProjectResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a DeleteProjectResponse. */
            class DeleteProjectResponse implements IDeleteProjectResponse {

                /**
                 * Constructs a new DeleteProjectResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IDeleteProjectResponse);

                /** DeleteProjectResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified DeleteProjectResponse message. Does not implicitly {@link trpc.video_media.bsc_core.DeleteProjectResponse.verify|verify} messages.
                 * @param message DeleteProjectResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IDeleteProjectResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteProjectResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteProjectResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.DeleteProjectResponse;

                /**
                 * Verifies a DeleteProjectResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteProjectResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteProjectResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.DeleteProjectResponse;

                /**
                 * Creates a plain object from a DeleteProjectResponse message. Also converts values to other types if specified.
                 * @param message DeleteProjectResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.DeleteProjectResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteProjectResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteProjectResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a Scene. */
            interface IScene {

                /** Scene id */
                id?: (string|null);

                /** Scene name */
                name?: (string|null);

                /** Scene sessions */
                sessions?: (trpc.video_media.bsc_core.IFilmSession[]|null);
            }

            /** Represents a Scene. */
            class Scene implements IScene {

                /**
                 * Constructs a new Scene.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IScene);

                /** Scene id. */
                public id: string;

                /** Scene name. */
                public name: string;

                /** Scene sessions. */
                public sessions: trpc.video_media.bsc_core.IFilmSession[];

                /**
                 * Encodes the specified Scene message. Does not implicitly {@link trpc.video_media.bsc_core.Scene.verify|verify} messages.
                 * @param message Scene message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IScene, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Scene message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Scene
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.Scene;

                /**
                 * Verifies a Scene message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Scene message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Scene
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.Scene;

                /**
                 * Creates a plain object from a Scene message. Also converts values to other types if specified.
                 * @param message Scene
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.Scene, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Scene to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Scene
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetSceneRequest. */
            interface IGetSceneRequest {

                /** GetSceneRequest sceneId */
                sceneId?: (string|null);
            }

            /** Represents a GetSceneRequest. */
            class GetSceneRequest implements IGetSceneRequest {

                /**
                 * Constructs a new GetSceneRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetSceneRequest);

                /** GetSceneRequest sceneId. */
                public sceneId: string;

                /**
                 * Encodes the specified GetSceneRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetSceneRequest.verify|verify} messages.
                 * @param message GetSceneRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetSceneRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetSceneRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetSceneRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetSceneRequest;

                /**
                 * Verifies a GetSceneRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetSceneRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetSceneRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetSceneRequest;

                /**
                 * Creates a plain object from a GetSceneRequest message. Also converts values to other types if specified.
                 * @param message GetSceneRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetSceneRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetSceneRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetSceneRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetSceneResponse. */
            interface IGetSceneResponse {

                /** GetSceneResponse scene */
                scene?: (trpc.video_media.bsc_core.IScene|null);

                /** GetSceneResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a GetSceneResponse. */
            class GetSceneResponse implements IGetSceneResponse {

                /**
                 * Constructs a new GetSceneResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetSceneResponse);

                /** GetSceneResponse scene. */
                public scene?: (trpc.video_media.bsc_core.IScene|null);

                /** GetSceneResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified GetSceneResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetSceneResponse.verify|verify} messages.
                 * @param message GetSceneResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetSceneResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetSceneResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetSceneResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetSceneResponse;

                /**
                 * Verifies a GetSceneResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetSceneResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetSceneResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetSceneResponse;

                /**
                 * Creates a plain object from a GetSceneResponse message. Also converts values to other types if specified.
                 * @param message GetSceneResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetSceneResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetSceneResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetSceneResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetSceneListRequest. */
            interface IGetSceneListRequest {

                /** GetSceneListRequest sceneIdList */
                sceneIdList?: (string[]|null);
            }

            /** Represents a GetSceneListRequest. */
            class GetSceneListRequest implements IGetSceneListRequest {

                /**
                 * Constructs a new GetSceneListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetSceneListRequest);

                /** GetSceneListRequest sceneIdList. */
                public sceneIdList: string[];

                /**
                 * Encodes the specified GetSceneListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetSceneListRequest.verify|verify} messages.
                 * @param message GetSceneListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetSceneListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetSceneListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetSceneListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetSceneListRequest;

                /**
                 * Verifies a GetSceneListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetSceneListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetSceneListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetSceneListRequest;

                /**
                 * Creates a plain object from a GetSceneListRequest message. Also converts values to other types if specified.
                 * @param message GetSceneListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetSceneListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetSceneListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetSceneListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetSceneListResponse. */
            interface IGetSceneListResponse {

                /** GetSceneListResponse scenes */
                scenes?: (trpc.video_media.bsc_core.IScene[]|null);

                /** GetSceneListResponse pageInfo */
                pageInfo?: (trpc.video_media.bsc_core.IPageInfo|null);
            }

            /** Represents a GetSceneListResponse. */
            class GetSceneListResponse implements IGetSceneListResponse {

                /**
                 * Constructs a new GetSceneListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetSceneListResponse);

                /** GetSceneListResponse scenes. */
                public scenes: trpc.video_media.bsc_core.IScene[];

                /** GetSceneListResponse pageInfo. */
                public pageInfo?: (trpc.video_media.bsc_core.IPageInfo|null);

                /**
                 * Encodes the specified GetSceneListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetSceneListResponse.verify|verify} messages.
                 * @param message GetSceneListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetSceneListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetSceneListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetSceneListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetSceneListResponse;

                /**
                 * Verifies a GetSceneListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetSceneListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetSceneListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetSceneListResponse;

                /**
                 * Creates a plain object from a GetSceneListResponse message. Also converts values to other types if specified.
                 * @param message GetSceneListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetSceneListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetSceneListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetSceneListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchSceneListRequest. */
            interface ISearchSceneListRequest {

                /** SearchSceneListRequest pagination */
                pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchSceneListRequest filters */
                filters?: (trpc.video_media.bsc_core.IFilter[]|null);

                /** SearchSceneListRequest sort */
                sort?: (trpc.video_media.bsc_core.Sort|null);
            }

            /** Represents a SearchSceneListRequest. */
            class SearchSceneListRequest implements ISearchSceneListRequest {

                /**
                 * Constructs a new SearchSceneListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchSceneListRequest);

                /** SearchSceneListRequest pagination. */
                public pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchSceneListRequest filters. */
                public filters: trpc.video_media.bsc_core.IFilter[];

                /** SearchSceneListRequest sort. */
                public sort: trpc.video_media.bsc_core.Sort;

                /**
                 * Encodes the specified SearchSceneListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.SearchSceneListRequest.verify|verify} messages.
                 * @param message SearchSceneListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchSceneListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchSceneListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchSceneListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchSceneListRequest;

                /**
                 * Verifies a SearchSceneListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchSceneListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchSceneListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchSceneListRequest;

                /**
                 * Creates a plain object from a SearchSceneListRequest message. Also converts values to other types if specified.
                 * @param message SearchSceneListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchSceneListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchSceneListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchSceneListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchSceneListResponse. */
            interface ISearchSceneListResponse {

                /** SearchSceneListResponse scenes */
                scenes?: (trpc.video_media.bsc_core.IScene[]|null);

                /** SearchSceneListResponse pageInfo */
                pageInfo?: (trpc.video_media.bsc_core.IPageInfo|null);
            }

            /** Represents a SearchSceneListResponse. */
            class SearchSceneListResponse implements ISearchSceneListResponse {

                /**
                 * Constructs a new SearchSceneListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchSceneListResponse);

                /** SearchSceneListResponse scenes. */
                public scenes: trpc.video_media.bsc_core.IScene[];

                /** SearchSceneListResponse pageInfo. */
                public pageInfo?: (trpc.video_media.bsc_core.IPageInfo|null);

                /**
                 * Encodes the specified SearchSceneListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.SearchSceneListResponse.verify|verify} messages.
                 * @param message SearchSceneListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchSceneListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchSceneListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchSceneListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchSceneListResponse;

                /**
                 * Verifies a SearchSceneListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchSceneListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchSceneListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchSceneListResponse;

                /**
                 * Creates a plain object from a SearchSceneListResponse message. Also converts values to other types if specified.
                 * @param message SearchSceneListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchSceneListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchSceneListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchSceneListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateSceneRequest. */
            interface ICreateSceneRequest {

                /** CreateSceneRequest name */
                name?: (string|null);

                /** CreateSceneRequest desc */
                desc?: (string|null);

                /** CreateSceneRequest assetList */
                assetList?: (string[]|null);
            }

            /** Represents a CreateSceneRequest. */
            class CreateSceneRequest implements ICreateSceneRequest {

                /**
                 * Constructs a new CreateSceneRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateSceneRequest);

                /** CreateSceneRequest name. */
                public name: string;

                /** CreateSceneRequest desc. */
                public desc: string;

                /** CreateSceneRequest assetList. */
                public assetList: string[];

                /**
                 * Encodes the specified CreateSceneRequest message. Does not implicitly {@link trpc.video_media.bsc_core.CreateSceneRequest.verify|verify} messages.
                 * @param message CreateSceneRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateSceneRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateSceneRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateSceneRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateSceneRequest;

                /**
                 * Verifies a CreateSceneRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateSceneRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateSceneRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateSceneRequest;

                /**
                 * Creates a plain object from a CreateSceneRequest message. Also converts values to other types if specified.
                 * @param message CreateSceneRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateSceneRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateSceneRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateSceneRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateSceneResponse. */
            interface ICreateSceneResponse {

                /** CreateSceneResponse sceneId */
                sceneId?: (string|null);

                /** CreateSceneResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a CreateSceneResponse. */
            class CreateSceneResponse implements ICreateSceneResponse {

                /**
                 * Constructs a new CreateSceneResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateSceneResponse);

                /** CreateSceneResponse sceneId. */
                public sceneId: string;

                /** CreateSceneResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified CreateSceneResponse message. Does not implicitly {@link trpc.video_media.bsc_core.CreateSceneResponse.verify|verify} messages.
                 * @param message CreateSceneResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateSceneResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateSceneResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateSceneResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateSceneResponse;

                /**
                 * Verifies a CreateSceneResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateSceneResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateSceneResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateSceneResponse;

                /**
                 * Creates a plain object from a CreateSceneResponse message. Also converts values to other types if specified.
                 * @param message CreateSceneResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateSceneResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateSceneResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateSceneResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateSceneRequest. */
            interface IUpdateSceneRequest {

                /** UpdateSceneRequest sceneId */
                sceneId?: (string|null);

                /** UpdateSceneRequest name */
                name?: (string|null);

                /** UpdateSceneRequest desc */
                desc?: (string|null);

                /** UpdateSceneRequest assetList */
                assetList?: (string[]|null);
            }

            /** Represents an UpdateSceneRequest. */
            class UpdateSceneRequest implements IUpdateSceneRequest {

                /**
                 * Constructs a new UpdateSceneRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IUpdateSceneRequest);

                /** UpdateSceneRequest sceneId. */
                public sceneId: string;

                /** UpdateSceneRequest name. */
                public name: string;

                /** UpdateSceneRequest desc. */
                public desc: string;

                /** UpdateSceneRequest assetList. */
                public assetList: string[];

                /**
                 * Encodes the specified UpdateSceneRequest message. Does not implicitly {@link trpc.video_media.bsc_core.UpdateSceneRequest.verify|verify} messages.
                 * @param message UpdateSceneRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IUpdateSceneRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateSceneRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateSceneRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.UpdateSceneRequest;

                /**
                 * Verifies an UpdateSceneRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateSceneRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateSceneRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.UpdateSceneRequest;

                /**
                 * Creates a plain object from an UpdateSceneRequest message. Also converts values to other types if specified.
                 * @param message UpdateSceneRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.UpdateSceneRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateSceneRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateSceneRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an UpdateSceneResponse. */
            interface IUpdateSceneResponse {

                /** UpdateSceneResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents an UpdateSceneResponse. */
            class UpdateSceneResponse implements IUpdateSceneResponse {

                /**
                 * Constructs a new UpdateSceneResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IUpdateSceneResponse);

                /** UpdateSceneResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified UpdateSceneResponse message. Does not implicitly {@link trpc.video_media.bsc_core.UpdateSceneResponse.verify|verify} messages.
                 * @param message UpdateSceneResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IUpdateSceneResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an UpdateSceneResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns UpdateSceneResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.UpdateSceneResponse;

                /**
                 * Verifies an UpdateSceneResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an UpdateSceneResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns UpdateSceneResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.UpdateSceneResponse;

                /**
                 * Creates a plain object from an UpdateSceneResponse message. Also converts values to other types if specified.
                 * @param message UpdateSceneResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.UpdateSceneResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this UpdateSceneResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for UpdateSceneResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteSceneRequest. */
            interface IDeleteSceneRequest {

                /** DeleteSceneRequest sceneId */
                sceneId?: (string|null);
            }

            /** Represents a DeleteSceneRequest. */
            class DeleteSceneRequest implements IDeleteSceneRequest {

                /**
                 * Constructs a new DeleteSceneRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IDeleteSceneRequest);

                /** DeleteSceneRequest sceneId. */
                public sceneId: string;

                /**
                 * Encodes the specified DeleteSceneRequest message. Does not implicitly {@link trpc.video_media.bsc_core.DeleteSceneRequest.verify|verify} messages.
                 * @param message DeleteSceneRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IDeleteSceneRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteSceneRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteSceneRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.DeleteSceneRequest;

                /**
                 * Verifies a DeleteSceneRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteSceneRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteSceneRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.DeleteSceneRequest;

                /**
                 * Creates a plain object from a DeleteSceneRequest message. Also converts values to other types if specified.
                 * @param message DeleteSceneRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.DeleteSceneRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteSceneRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteSceneRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a DeleteSceneResponse. */
            interface IDeleteSceneResponse {

                /** DeleteSceneResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a DeleteSceneResponse. */
            class DeleteSceneResponse implements IDeleteSceneResponse {

                /**
                 * Constructs a new DeleteSceneResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IDeleteSceneResponse);

                /** DeleteSceneResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified DeleteSceneResponse message. Does not implicitly {@link trpc.video_media.bsc_core.DeleteSceneResponse.verify|verify} messages.
                 * @param message DeleteSceneResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IDeleteSceneResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a DeleteSceneResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns DeleteSceneResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.DeleteSceneResponse;

                /**
                 * Verifies a DeleteSceneResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a DeleteSceneResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns DeleteSceneResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.DeleteSceneResponse;

                /**
                 * Creates a plain object from a DeleteSceneResponse message. Also converts values to other types if specified.
                 * @param message DeleteSceneResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.DeleteSceneResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this DeleteSceneResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for DeleteSceneResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSession. */
            interface IFilmSession {

                /** FilmSession id */
                id?: (string|null);

                /** FilmSession sceneId */
                sceneId?: (string|null);

                /** FilmSession name */
                name?: (string|null);

                /** FilmSession data */
                data?: (string|null);

                /** FilmSession version */
                version?: (number|null);
            }

            /** Represents a FilmSession. */
            class FilmSession implements IFilmSession {

                /**
                 * Constructs a new FilmSession.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSession);

                /** FilmSession id. */
                public id: string;

                /** FilmSession sceneId. */
                public sceneId: string;

                /** FilmSession name. */
                public name: string;

                /** FilmSession data. */
                public data: string;

                /** FilmSession version. */
                public version: number;

                /**
                 * Encodes the specified FilmSession message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSession.verify|verify} messages.
                 * @param message FilmSession message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSession, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSession message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSession
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSession;

                /**
                 * Verifies a FilmSession message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSession message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSession
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSession;

                /**
                 * Creates a plain object from a FilmSession message. Also converts values to other types if specified.
                 * @param message FilmSession
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSession, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSession to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSession
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionRequest. */
            interface IGetFilmSessionRequest {

                /** GetFilmSessionRequest sessionId */
                sessionId?: (string|null);
            }

            /** Represents a GetFilmSessionRequest. */
            class GetFilmSessionRequest implements IGetFilmSessionRequest {

                /**
                 * Constructs a new GetFilmSessionRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionRequest);

                /** GetFilmSessionRequest sessionId. */
                public sessionId: string;

                /**
                 * Encodes the specified GetFilmSessionRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionRequest.verify|verify} messages.
                 * @param message GetFilmSessionRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionRequest;

                /**
                 * Verifies a GetFilmSessionRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionRequest;

                /**
                 * Creates a plain object from a GetFilmSessionRequest message. Also converts values to other types if specified.
                 * @param message GetFilmSessionRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionResponse. */
            interface IGetFilmSessionResponse {

                /** GetFilmSessionResponse session */
                session?: (trpc.video_media.bsc_core.IFilmSession|null);
            }

            /** Represents a GetFilmSessionResponse. */
            class GetFilmSessionResponse implements IGetFilmSessionResponse {

                /**
                 * Constructs a new GetFilmSessionResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionResponse);

                /** GetFilmSessionResponse session. */
                public session?: (trpc.video_media.bsc_core.IFilmSession|null);

                /**
                 * Encodes the specified GetFilmSessionResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionResponse.verify|verify} messages.
                 * @param message GetFilmSessionResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionResponse;

                /**
                 * Verifies a GetFilmSessionResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionResponse;

                /**
                 * Creates a plain object from a GetFilmSessionResponse message. Also converts values to other types if specified.
                 * @param message GetFilmSessionResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchFilmSessionListRequest. */
            interface ISearchFilmSessionListRequest {

                /** SearchFilmSessionListRequest pagination */
                pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchFilmSessionListRequest filters */
                filters?: (trpc.video_media.bsc_core.IFilter[]|null);

                /** SearchFilmSessionListRequest sort */
                sort?: (trpc.video_media.bsc_core.Sort|null);
            }

            /** Represents a SearchFilmSessionListRequest. */
            class SearchFilmSessionListRequest implements ISearchFilmSessionListRequest {

                /**
                 * Constructs a new SearchFilmSessionListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchFilmSessionListRequest);

                /** SearchFilmSessionListRequest pagination. */
                public pagination?: (trpc.video_media.bsc_core.IPagination|null);

                /** SearchFilmSessionListRequest filters. */
                public filters: trpc.video_media.bsc_core.IFilter[];

                /** SearchFilmSessionListRequest sort. */
                public sort: trpc.video_media.bsc_core.Sort;

                /**
                 * Encodes the specified SearchFilmSessionListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.SearchFilmSessionListRequest.verify|verify} messages.
                 * @param message SearchFilmSessionListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchFilmSessionListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchFilmSessionListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchFilmSessionListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchFilmSessionListRequest;

                /**
                 * Verifies a SearchFilmSessionListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchFilmSessionListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchFilmSessionListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchFilmSessionListRequest;

                /**
                 * Creates a plain object from a SearchFilmSessionListRequest message. Also converts values to other types if specified.
                 * @param message SearchFilmSessionListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchFilmSessionListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchFilmSessionListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchFilmSessionListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SearchFilmSessionListResponse. */
            interface ISearchFilmSessionListResponse {

                /** SearchFilmSessionListResponse filmSesionList */
                filmSesionList?: (trpc.video_media.bsc_core.IFilmSession[]|null);

                /** SearchFilmSessionListResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a SearchFilmSessionListResponse. */
            class SearchFilmSessionListResponse implements ISearchFilmSessionListResponse {

                /**
                 * Constructs a new SearchFilmSessionListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISearchFilmSessionListResponse);

                /** SearchFilmSessionListResponse filmSesionList. */
                public filmSesionList: trpc.video_media.bsc_core.IFilmSession[];

                /** SearchFilmSessionListResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified SearchFilmSessionListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.SearchFilmSessionListResponse.verify|verify} messages.
                 * @param message SearchFilmSessionListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISearchFilmSessionListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SearchFilmSessionListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SearchFilmSessionListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SearchFilmSessionListResponse;

                /**
                 * Verifies a SearchFilmSessionListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SearchFilmSessionListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SearchFilmSessionListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SearchFilmSessionListResponse;

                /**
                 * Creates a plain object from a SearchFilmSessionListResponse message. Also converts values to other types if specified.
                 * @param message SearchFilmSessionListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SearchFilmSessionListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SearchFilmSessionListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SearchFilmSessionListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionListRequest. */
            interface IGetFilmSessionListRequest {

                /** GetFilmSessionListRequest fileSessionList */
                fileSessionList?: (string[]|null);
            }

            /** Represents a GetFilmSessionListRequest. */
            class GetFilmSessionListRequest implements IGetFilmSessionListRequest {

                /**
                 * Constructs a new GetFilmSessionListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionListRequest);

                /** GetFilmSessionListRequest fileSessionList. */
                public fileSessionList: string[];

                /**
                 * Encodes the specified GetFilmSessionListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionListRequest.verify|verify} messages.
                 * @param message GetFilmSessionListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionListRequest;

                /**
                 * Verifies a GetFilmSessionListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionListRequest;

                /**
                 * Creates a plain object from a GetFilmSessionListRequest message. Also converts values to other types if specified.
                 * @param message GetFilmSessionListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionListResponse. */
            interface IGetFilmSessionListResponse {

                /** GetFilmSessionListResponse filmSesionList */
                filmSesionList?: (trpc.video_media.bsc_core.IFilmSession[]|null);

                /** GetFilmSessionListResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a GetFilmSessionListResponse. */
            class GetFilmSessionListResponse implements IGetFilmSessionListResponse {

                /**
                 * Constructs a new GetFilmSessionListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionListResponse);

                /** GetFilmSessionListResponse filmSesionList. */
                public filmSesionList: trpc.video_media.bsc_core.IFilmSession[];

                /** GetFilmSessionListResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified GetFilmSessionListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionListResponse.verify|verify} messages.
                 * @param message GetFilmSessionListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionListResponse;

                /**
                 * Verifies a GetFilmSessionListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionListResponse;

                /**
                 * Creates a plain object from a GetFilmSessionListResponse message. Also converts values to other types if specified.
                 * @param message GetFilmSessionListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateFilmSessionRequest. */
            interface ICreateFilmSessionRequest {

                /** CreateFilmSessionRequest sceneId */
                sceneId?: (string|null);

                /** CreateFilmSessionRequest name */
                name?: (string|null);

                /** CreateFilmSessionRequest data */
                data?: (string|null);
            }

            /** Represents a CreateFilmSessionRequest. */
            class CreateFilmSessionRequest implements ICreateFilmSessionRequest {

                /**
                 * Constructs a new CreateFilmSessionRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateFilmSessionRequest);

                /** CreateFilmSessionRequest sceneId. */
                public sceneId: string;

                /** CreateFilmSessionRequest name. */
                public name: string;

                /** CreateFilmSessionRequest data. */
                public data: string;

                /**
                 * Encodes the specified CreateFilmSessionRequest message. Does not implicitly {@link trpc.video_media.bsc_core.CreateFilmSessionRequest.verify|verify} messages.
                 * @param message CreateFilmSessionRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateFilmSessionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateFilmSessionRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateFilmSessionRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateFilmSessionRequest;

                /**
                 * Verifies a CreateFilmSessionRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateFilmSessionRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateFilmSessionRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateFilmSessionRequest;

                /**
                 * Creates a plain object from a CreateFilmSessionRequest message. Also converts values to other types if specified.
                 * @param message CreateFilmSessionRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateFilmSessionRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateFilmSessionRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateFilmSessionRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a CreateFilmSessionResponse. */
            interface ICreateFilmSessionResponse {

                /** CreateFilmSessionResponse sessionId */
                sessionId?: (string|null);

                /** CreateFilmSessionResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a CreateFilmSessionResponse. */
            class CreateFilmSessionResponse implements ICreateFilmSessionResponse {

                /**
                 * Constructs a new CreateFilmSessionResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ICreateFilmSessionResponse);

                /** CreateFilmSessionResponse sessionId. */
                public sessionId: string;

                /** CreateFilmSessionResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified CreateFilmSessionResponse message. Does not implicitly {@link trpc.video_media.bsc_core.CreateFilmSessionResponse.verify|verify} messages.
                 * @param message CreateFilmSessionResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ICreateFilmSessionResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a CreateFilmSessionResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns CreateFilmSessionResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.CreateFilmSessionResponse;

                /**
                 * Verifies a CreateFilmSessionResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a CreateFilmSessionResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns CreateFilmSessionResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.CreateFilmSessionResponse;

                /**
                 * Creates a plain object from a CreateFilmSessionResponse message. Also converts values to other types if specified.
                 * @param message CreateFilmSessionResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.CreateFilmSessionResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this CreateFilmSessionResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for CreateFilmSessionResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SaveFilmSessionRequest. */
            interface ISaveFilmSessionRequest {

                /** SaveFilmSessionRequest sessionId */
                sessionId?: (string|null);

                /** SaveFilmSessionRequest data */
                data?: (string|null);

                /** SaveFilmSessionRequest version */
                version?: (number|null);
            }

            /** Represents a SaveFilmSessionRequest. */
            class SaveFilmSessionRequest implements ISaveFilmSessionRequest {

                /**
                 * Constructs a new SaveFilmSessionRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISaveFilmSessionRequest);

                /** SaveFilmSessionRequest sessionId. */
                public sessionId: string;

                /** SaveFilmSessionRequest data. */
                public data: string;

                /** SaveFilmSessionRequest version. */
                public version: number;

                /**
                 * Encodes the specified SaveFilmSessionRequest message. Does not implicitly {@link trpc.video_media.bsc_core.SaveFilmSessionRequest.verify|verify} messages.
                 * @param message SaveFilmSessionRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISaveFilmSessionRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SaveFilmSessionRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SaveFilmSessionRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SaveFilmSessionRequest;

                /**
                 * Verifies a SaveFilmSessionRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SaveFilmSessionRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SaveFilmSessionRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SaveFilmSessionRequest;

                /**
                 * Creates a plain object from a SaveFilmSessionRequest message. Also converts values to other types if specified.
                 * @param message SaveFilmSessionRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SaveFilmSessionRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SaveFilmSessionRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SaveFilmSessionRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SaveFilmSessionResponse. */
            interface ISaveFilmSessionResponse {

                /** SaveFilmSessionResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a SaveFilmSessionResponse. */
            class SaveFilmSessionResponse implements ISaveFilmSessionResponse {

                /**
                 * Constructs a new SaveFilmSessionResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISaveFilmSessionResponse);

                /** SaveFilmSessionResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified SaveFilmSessionResponse message. Does not implicitly {@link trpc.video_media.bsc_core.SaveFilmSessionResponse.verify|verify} messages.
                 * @param message SaveFilmSessionResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISaveFilmSessionResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SaveFilmSessionResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SaveFilmSessionResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SaveFilmSessionResponse;

                /**
                 * Verifies a SaveFilmSessionResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SaveFilmSessionResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SaveFilmSessionResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SaveFilmSessionResponse;

                /**
                 * Creates a plain object from a SaveFilmSessionResponse message. Also converts values to other types if specified.
                 * @param message SaveFilmSessionResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SaveFilmSessionResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SaveFilmSessionResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SaveFilmSessionResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSessionShareRequest. */
            interface IFilmSessionShareRequest {
            }

            /** Represents a FilmSessionShareRequest. */
            class FilmSessionShareRequest implements IFilmSessionShareRequest {

                /**
                 * Constructs a new FilmSessionShareRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSessionShareRequest);

                /**
                 * Encodes the specified FilmSessionShareRequest message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSessionShareRequest.verify|verify} messages.
                 * @param message FilmSessionShareRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSessionShareRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSessionShareRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSessionShareRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSessionShareRequest;

                /**
                 * Verifies a FilmSessionShareRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSessionShareRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSessionShareRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSessionShareRequest;

                /**
                 * Creates a plain object from a FilmSessionShareRequest message. Also converts values to other types if specified.
                 * @param message FilmSessionShareRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSessionShareRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSessionShareRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSessionShareRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSessionShareResponse. */
            interface IFilmSessionShareResponse {
            }

            /** Represents a FilmSessionShareResponse. */
            class FilmSessionShareResponse implements IFilmSessionShareResponse {

                /**
                 * Constructs a new FilmSessionShareResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSessionShareResponse);

                /**
                 * Encodes the specified FilmSessionShareResponse message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSessionShareResponse.verify|verify} messages.
                 * @param message FilmSessionShareResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSessionShareResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSessionShareResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSessionShareResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSessionShareResponse;

                /**
                 * Verifies a FilmSessionShareResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSessionShareResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSessionShareResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSessionShareResponse;

                /**
                 * Creates a plain object from a FilmSessionShareResponse message. Also converts values to other types if specified.
                 * @param message FilmSessionShareResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSessionShareResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSessionShareResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSessionShareResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSessionCopyRequest. */
            interface IFilmSessionCopyRequest {

                /** FilmSessionCopyRequest fileSessionList */
                fileSessionList?: (string[]|null);
            }

            /** Represents a FilmSessionCopyRequest. */
            class FilmSessionCopyRequest implements IFilmSessionCopyRequest {

                /**
                 * Constructs a new FilmSessionCopyRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSessionCopyRequest);

                /** FilmSessionCopyRequest fileSessionList. */
                public fileSessionList: string[];

                /**
                 * Encodes the specified FilmSessionCopyRequest message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSessionCopyRequest.verify|verify} messages.
                 * @param message FilmSessionCopyRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSessionCopyRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSessionCopyRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSessionCopyRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSessionCopyRequest;

                /**
                 * Verifies a FilmSessionCopyRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSessionCopyRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSessionCopyRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSessionCopyRequest;

                /**
                 * Creates a plain object from a FilmSessionCopyRequest message. Also converts values to other types if specified.
                 * @param message FilmSessionCopyRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSessionCopyRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSessionCopyRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSessionCopyRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSessionCopyItem. */
            interface IFilmSessionCopyItem {

                /** FilmSessionCopyItem oriFilmSession */
                oriFilmSession?: (string|null);

                /** FilmSessionCopyItem newFilmSession */
                newFilmSession?: (string|null);
            }

            /** Represents a FilmSessionCopyItem. */
            class FilmSessionCopyItem implements IFilmSessionCopyItem {

                /**
                 * Constructs a new FilmSessionCopyItem.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSessionCopyItem);

                /** FilmSessionCopyItem oriFilmSession. */
                public oriFilmSession: string;

                /** FilmSessionCopyItem newFilmSession. */
                public newFilmSession: string;

                /**
                 * Encodes the specified FilmSessionCopyItem message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSessionCopyItem.verify|verify} messages.
                 * @param message FilmSessionCopyItem message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSessionCopyItem, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSessionCopyItem message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSessionCopyItem
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSessionCopyItem;

                /**
                 * Verifies a FilmSessionCopyItem message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSessionCopyItem message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSessionCopyItem
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSessionCopyItem;

                /**
                 * Creates a plain object from a FilmSessionCopyItem message. Also converts values to other types if specified.
                 * @param message FilmSessionCopyItem
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSessionCopyItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSessionCopyItem to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSessionCopyItem
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FilmSessionCopyResponse. */
            interface IFilmSessionCopyResponse {

                /** FilmSessionCopyResponse items */
                items?: (trpc.video_media.bsc_core.IFilmSessionCopyItem[]|null);

                /** FilmSessionCopyResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a FilmSessionCopyResponse. */
            class FilmSessionCopyResponse implements IFilmSessionCopyResponse {

                /**
                 * Constructs a new FilmSessionCopyResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IFilmSessionCopyResponse);

                /** FilmSessionCopyResponse items. */
                public items: trpc.video_media.bsc_core.IFilmSessionCopyItem[];

                /** FilmSessionCopyResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified FilmSessionCopyResponse message. Does not implicitly {@link trpc.video_media.bsc_core.FilmSessionCopyResponse.verify|verify} messages.
                 * @param message FilmSessionCopyResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IFilmSessionCopyResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FilmSessionCopyResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FilmSessionCopyResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.FilmSessionCopyResponse;

                /**
                 * Verifies a FilmSessionCopyResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FilmSessionCopyResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FilmSessionCopyResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.FilmSessionCopyResponse;

                /**
                 * Creates a plain object from a FilmSessionCopyResponse message. Also converts values to other types if specified.
                 * @param message FilmSessionCopyResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.FilmSessionCopyResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FilmSessionCopyResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FilmSessionCopyResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a SessionHistoryItem. */
            interface ISessionHistoryItem {

                /** SessionHistoryItem id */
                id?: (string|null);

                /** SessionHistoryItem createdAt */
                createdAt?: (string|null);

                /** SessionHistoryItem data */
                data?: (string|null);
            }

            /** Represents a SessionHistoryItem. */
            class SessionHistoryItem implements ISessionHistoryItem {

                /**
                 * Constructs a new SessionHistoryItem.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.ISessionHistoryItem);

                /** SessionHistoryItem id. */
                public id: string;

                /** SessionHistoryItem createdAt. */
                public createdAt: string;

                /** SessionHistoryItem data. */
                public data: string;

                /**
                 * Encodes the specified SessionHistoryItem message. Does not implicitly {@link trpc.video_media.bsc_core.SessionHistoryItem.verify|verify} messages.
                 * @param message SessionHistoryItem message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.ISessionHistoryItem, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a SessionHistoryItem message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns SessionHistoryItem
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.SessionHistoryItem;

                /**
                 * Verifies a SessionHistoryItem message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a SessionHistoryItem message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns SessionHistoryItem
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.SessionHistoryItem;

                /**
                 * Creates a plain object from a SessionHistoryItem message. Also converts values to other types if specified.
                 * @param message SessionHistoryItem
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.SessionHistoryItem, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this SessionHistoryItem to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for SessionHistoryItem
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionHistoryRequest. */
            interface IGetFilmSessionHistoryRequest {

                /** GetFilmSessionHistoryRequest sessionId */
                sessionId?: (string|null);
            }

            /** Represents a GetFilmSessionHistoryRequest. */
            class GetFilmSessionHistoryRequest implements IGetFilmSessionHistoryRequest {

                /**
                 * Constructs a new GetFilmSessionHistoryRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionHistoryRequest);

                /** GetFilmSessionHistoryRequest sessionId. */
                public sessionId: string;

                /**
                 * Encodes the specified GetFilmSessionHistoryRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionHistoryRequest.verify|verify} messages.
                 * @param message GetFilmSessionHistoryRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionHistoryRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionHistoryRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionHistoryRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionHistoryRequest;

                /**
                 * Verifies a GetFilmSessionHistoryRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionHistoryRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionHistoryRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionHistoryRequest;

                /**
                 * Creates a plain object from a GetFilmSessionHistoryRequest message. Also converts values to other types if specified.
                 * @param message GetFilmSessionHistoryRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionHistoryRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionHistoryRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionHistoryRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetFilmSessionHistoryResponse. */
            interface IGetFilmSessionHistoryResponse {

                /** GetFilmSessionHistoryResponse history */
                history?: (trpc.video_media.bsc_core.ISessionHistoryItem[]|null);
            }

            /** Represents a GetFilmSessionHistoryResponse. */
            class GetFilmSessionHistoryResponse implements IGetFilmSessionHistoryResponse {

                /**
                 * Constructs a new GetFilmSessionHistoryResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetFilmSessionHistoryResponse);

                /** GetFilmSessionHistoryResponse history. */
                public history: trpc.video_media.bsc_core.ISessionHistoryItem[];

                /**
                 * Encodes the specified GetFilmSessionHistoryResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetFilmSessionHistoryResponse.verify|verify} messages.
                 * @param message GetFilmSessionHistoryResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetFilmSessionHistoryResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetFilmSessionHistoryResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetFilmSessionHistoryResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetFilmSessionHistoryResponse;

                /**
                 * Verifies a GetFilmSessionHistoryResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetFilmSessionHistoryResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetFilmSessionHistoryResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetFilmSessionHistoryResponse;

                /**
                 * Creates a plain object from a GetFilmSessionHistoryResponse message. Also converts values to other types if specified.
                 * @param message GetFilmSessionHistoryResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetFilmSessionHistoryResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetFilmSessionHistoryResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetFilmSessionHistoryResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of an Asset. */
            interface IAsset {

                /** Asset id */
                id?: (string|null);

                /** Asset tags */
                tags?: (string[]|null);

                /** Asset assetType */
                assetType?: (string|null);

                /** Asset data */
                data?: (string|null);

                /** Asset state */
                state?: (number|null);

                /** Asset projectIdList */
                projectIdList?: (string[]|null);

                /** Asset createdAt */
                createdAt?: (number|Long|null);

                /** Asset createdBy */
                createdBy?: (string|null);
            }

            /** Represents an Asset. */
            class Asset implements IAsset {

                /**
                 * Constructs a new Asset.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IAsset);

                /** Asset id. */
                public id: string;

                /** Asset tags. */
                public tags: string[];

                /** Asset assetType. */
                public assetType: string;

                /** Asset data. */
                public data: string;

                /** Asset state. */
                public state: number;

                /** Asset projectIdList. */
                public projectIdList: string[];

                /** Asset createdAt. */
                public createdAt: (number|Long);

                /** Asset createdBy. */
                public createdBy: string;

                /**
                 * Encodes the specified Asset message. Does not implicitly {@link trpc.video_media.bsc_core.Asset.verify|verify} messages.
                 * @param message Asset message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IAsset, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an Asset message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Asset
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.Asset;

                /**
                 * Verifies an Asset message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an Asset message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Asset
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.Asset;

                /**
                 * Creates a plain object from an Asset message. Also converts values to other types if specified.
                 * @param message Asset
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.Asset, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Asset to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Asset
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetAssetsListRequest. */
            interface IGetAssetsListRequest {

                /** GetAssetsListRequest projectId */
                projectId?: (string|null);

                /** GetAssetsListRequest sceneId */
                sceneId?: (string|null);
            }

            /** Represents a GetAssetsListRequest. */
            class GetAssetsListRequest implements IGetAssetsListRequest {

                /**
                 * Constructs a new GetAssetsListRequest.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetAssetsListRequest);

                /** GetAssetsListRequest projectId. */
                public projectId: string;

                /** GetAssetsListRequest sceneId. */
                public sceneId: string;

                /**
                 * Encodes the specified GetAssetsListRequest message. Does not implicitly {@link trpc.video_media.bsc_core.GetAssetsListRequest.verify|verify} messages.
                 * @param message GetAssetsListRequest message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetAssetsListRequest, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetAssetsListRequest message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetAssetsListRequest
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetAssetsListRequest;

                /**
                 * Verifies a GetAssetsListRequest message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetAssetsListRequest message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetAssetsListRequest
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetAssetsListRequest;

                /**
                 * Creates a plain object from a GetAssetsListRequest message. Also converts values to other types if specified.
                 * @param message GetAssetsListRequest
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetAssetsListRequest, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetAssetsListRequest to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetAssetsListRequest
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a GetAssetsListResponse. */
            interface IGetAssetsListResponse {

                /** GetAssetsListResponse assetList */
                assetList?: (trpc.video_media.bsc_core.IAsset[]|null);

                /** GetAssetsListResponse rsp */
                rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);
            }

            /** Represents a GetAssetsListResponse. */
            class GetAssetsListResponse implements IGetAssetsListResponse {

                /**
                 * Constructs a new GetAssetsListResponse.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: trpc.video_media.bsc_core.IGetAssetsListResponse);

                /** GetAssetsListResponse assetList. */
                public assetList: trpc.video_media.bsc_core.IAsset[];

                /** GetAssetsListResponse rsp. */
                public rsp?: (trpc.video_media.bsc_core.ICommonRsp|null);

                /**
                 * Encodes the specified GetAssetsListResponse message. Does not implicitly {@link trpc.video_media.bsc_core.GetAssetsListResponse.verify|verify} messages.
                 * @param message GetAssetsListResponse message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: trpc.video_media.bsc_core.IGetAssetsListResponse, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a GetAssetsListResponse message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns GetAssetsListResponse
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): trpc.video_media.bsc_core.GetAssetsListResponse;

                /**
                 * Verifies a GetAssetsListResponse message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a GetAssetsListResponse message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns GetAssetsListResponse
                 */
                public static fromObject(object: { [k: string]: any }): trpc.video_media.bsc_core.GetAssetsListResponse;

                /**
                 * Creates a plain object from a GetAssetsListResponse message. Also converts values to other types if specified.
                 * @param message GetAssetsListResponse
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: trpc.video_media.bsc_core.GetAssetsListResponse, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this GetAssetsListResponse to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for GetAssetsListResponse
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }
    }
}

/** Namespace validate. */
export namespace validate {

    /** Properties of a FieldRules. */
    interface IFieldRules {

        /** FieldRules message */
        message?: (validate.IMessageRules|null);

        /** FieldRules float */
        float?: (validate.IFloatRules|null);

        /** FieldRules double */
        double?: (validate.IDoubleRules|null);

        /** FieldRules int32 */
        int32?: (validate.IInt32Rules|null);

        /** FieldRules int64 */
        int64?: (validate.IInt64Rules|null);

        /** FieldRules uint32 */
        uint32?: (validate.IUInt32Rules|null);

        /** FieldRules uint64 */
        uint64?: (validate.IUInt64Rules|null);

        /** FieldRules sint32 */
        sint32?: (validate.ISInt32Rules|null);

        /** FieldRules sint64 */
        sint64?: (validate.ISInt64Rules|null);

        /** FieldRules fixed32 */
        fixed32?: (validate.IFixed32Rules|null);

        /** FieldRules fixed64 */
        fixed64?: (validate.IFixed64Rules|null);

        /** FieldRules sfixed32 */
        sfixed32?: (validate.ISFixed32Rules|null);

        /** FieldRules sfixed64 */
        sfixed64?: (validate.ISFixed64Rules|null);

        /** FieldRules bool */
        bool?: (validate.IBoolRules|null);

        /** FieldRules string */
        string?: (validate.IStringRules|null);

        /** FieldRules bytes */
        bytes?: (validate.IBytesRules|null);

        /** FieldRules enum */
        "enum"?: (validate.IEnumRules|null);

        /** FieldRules repeated */
        repeated?: (validate.IRepeatedRules|null);

        /** FieldRules map */
        map?: (validate.IMapRules|null);

        /** FieldRules any */
        any?: (validate.IAnyRules|null);

        /** FieldRules duration */
        duration?: (validate.IDurationRules|null);

        /** FieldRules timestamp */
        timestamp?: (validate.ITimestampRules|null);
    }

    /** Represents a FieldRules. */
    class FieldRules implements IFieldRules {

        /**
         * Constructs a new FieldRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFieldRules);

        /** FieldRules message. */
        public message?: (validate.IMessageRules|null);

        /** FieldRules float. */
        public float?: (validate.IFloatRules|null);

        /** FieldRules double. */
        public double?: (validate.IDoubleRules|null);

        /** FieldRules int32. */
        public int32?: (validate.IInt32Rules|null);

        /** FieldRules int64. */
        public int64?: (validate.IInt64Rules|null);

        /** FieldRules uint32. */
        public uint32?: (validate.IUInt32Rules|null);

        /** FieldRules uint64. */
        public uint64?: (validate.IUInt64Rules|null);

        /** FieldRules sint32. */
        public sint32?: (validate.ISInt32Rules|null);

        /** FieldRules sint64. */
        public sint64?: (validate.ISInt64Rules|null);

        /** FieldRules fixed32. */
        public fixed32?: (validate.IFixed32Rules|null);

        /** FieldRules fixed64. */
        public fixed64?: (validate.IFixed64Rules|null);

        /** FieldRules sfixed32. */
        public sfixed32?: (validate.ISFixed32Rules|null);

        /** FieldRules sfixed64. */
        public sfixed64?: (validate.ISFixed64Rules|null);

        /** FieldRules bool. */
        public bool?: (validate.IBoolRules|null);

        /** FieldRules string. */
        public string?: (validate.IStringRules|null);

        /** FieldRules bytes. */
        public bytes?: (validate.IBytesRules|null);

        /** FieldRules enum. */
        public enum?: (validate.IEnumRules|null);

        /** FieldRules repeated. */
        public repeated?: (validate.IRepeatedRules|null);

        /** FieldRules map. */
        public map?: (validate.IMapRules|null);

        /** FieldRules any. */
        public any?: (validate.IAnyRules|null);

        /** FieldRules duration. */
        public duration?: (validate.IDurationRules|null);

        /** FieldRules timestamp. */
        public timestamp?: (validate.ITimestampRules|null);

        /** FieldRules type. */
        public type?: ("float"|"double"|"int32"|"int64"|"uint32"|"uint64"|"sint32"|"sint64"|"fixed32"|"fixed64"|"sfixed32"|"sfixed64"|"bool"|"string"|"bytes"|"enum"|"repeated"|"map"|"any"|"duration"|"timestamp");

        /**
         * Encodes the specified FieldRules message. Does not implicitly {@link validate.FieldRules.verify|verify} messages.
         * @param message FieldRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFieldRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a FieldRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns FieldRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.FieldRules;

        /**
         * Verifies a FieldRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a FieldRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns FieldRules
         */
        public static fromObject(object: { [k: string]: any }): validate.FieldRules;

        /**
         * Creates a plain object from a FieldRules message. Also converts values to other types if specified.
         * @param message FieldRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.FieldRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this FieldRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for FieldRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a FloatRules. */
    interface IFloatRules {

        /** FloatRules const */
        "const"?: (number|null);

        /** FloatRules lt */
        lt?: (number|null);

        /** FloatRules lte */
        lte?: (number|null);

        /** FloatRules gt */
        gt?: (number|null);

        /** FloatRules gte */
        gte?: (number|null);

        /** FloatRules in */
        "in"?: (number[]|null);

        /** FloatRules notIn */
        notIn?: (number[]|null);

        /** FloatRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a FloatRules. */
    class FloatRules implements IFloatRules {

        /**
         * Constructs a new FloatRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFloatRules);

        /** FloatRules const. */
        public const: number;

        /** FloatRules lt. */
        public lt: number;

        /** FloatRules lte. */
        public lte: number;

        /** FloatRules gt. */
        public gt: number;

        /** FloatRules gte. */
        public gte: number;

        /** FloatRules in. */
        public in: number[];

        /** FloatRules notIn. */
        public notIn: number[];

        /** FloatRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified FloatRules message. Does not implicitly {@link validate.FloatRules.verify|verify} messages.
         * @param message FloatRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFloatRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a FloatRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns FloatRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.FloatRules;

        /**
         * Verifies a FloatRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a FloatRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns FloatRules
         */
        public static fromObject(object: { [k: string]: any }): validate.FloatRules;

        /**
         * Creates a plain object from a FloatRules message. Also converts values to other types if specified.
         * @param message FloatRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.FloatRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this FloatRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for FloatRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a DoubleRules. */
    interface IDoubleRules {

        /** DoubleRules const */
        "const"?: (number|null);

        /** DoubleRules lt */
        lt?: (number|null);

        /** DoubleRules lte */
        lte?: (number|null);

        /** DoubleRules gt */
        gt?: (number|null);

        /** DoubleRules gte */
        gte?: (number|null);

        /** DoubleRules in */
        "in"?: (number[]|null);

        /** DoubleRules notIn */
        notIn?: (number[]|null);

        /** DoubleRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a DoubleRules. */
    class DoubleRules implements IDoubleRules {

        /**
         * Constructs a new DoubleRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IDoubleRules);

        /** DoubleRules const. */
        public const: number;

        /** DoubleRules lt. */
        public lt: number;

        /** DoubleRules lte. */
        public lte: number;

        /** DoubleRules gt. */
        public gt: number;

        /** DoubleRules gte. */
        public gte: number;

        /** DoubleRules in. */
        public in: number[];

        /** DoubleRules notIn. */
        public notIn: number[];

        /** DoubleRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified DoubleRules message. Does not implicitly {@link validate.DoubleRules.verify|verify} messages.
         * @param message DoubleRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IDoubleRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DoubleRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DoubleRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.DoubleRules;

        /**
         * Verifies a DoubleRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DoubleRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DoubleRules
         */
        public static fromObject(object: { [k: string]: any }): validate.DoubleRules;

        /**
         * Creates a plain object from a DoubleRules message. Also converts values to other types if specified.
         * @param message DoubleRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.DoubleRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DoubleRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for DoubleRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an Int32Rules. */
    interface IInt32Rules {

        /** Int32Rules const */
        "const"?: (number|null);

        /** Int32Rules lt */
        lt?: (number|null);

        /** Int32Rules lte */
        lte?: (number|null);

        /** Int32Rules gt */
        gt?: (number|null);

        /** Int32Rules gte */
        gte?: (number|null);

        /** Int32Rules in */
        "in"?: (number[]|null);

        /** Int32Rules notIn */
        notIn?: (number[]|null);

        /** Int32Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents an Int32Rules. */
    class Int32Rules implements IInt32Rules {

        /**
         * Constructs a new Int32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IInt32Rules);

        /** Int32Rules const. */
        public const: number;

        /** Int32Rules lt. */
        public lt: number;

        /** Int32Rules lte. */
        public lte: number;

        /** Int32Rules gt. */
        public gt: number;

        /** Int32Rules gte. */
        public gte: number;

        /** Int32Rules in. */
        public in: number[];

        /** Int32Rules notIn. */
        public notIn: number[];

        /** Int32Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified Int32Rules message. Does not implicitly {@link validate.Int32Rules.verify|verify} messages.
         * @param message Int32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Int32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Int32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Int32Rules;

        /**
         * Verifies an Int32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Int32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Int32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Int32Rules;

        /**
         * Creates a plain object from an Int32Rules message. Also converts values to other types if specified.
         * @param message Int32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Int32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Int32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Int32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an Int64Rules. */
    interface IInt64Rules {

        /** Int64Rules const */
        "const"?: (number|Long|null);

        /** Int64Rules lt */
        lt?: (number|Long|null);

        /** Int64Rules lte */
        lte?: (number|Long|null);

        /** Int64Rules gt */
        gt?: (number|Long|null);

        /** Int64Rules gte */
        gte?: (number|Long|null);

        /** Int64Rules in */
        "in"?: ((number|Long)[]|null);

        /** Int64Rules notIn */
        notIn?: ((number|Long)[]|null);

        /** Int64Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents an Int64Rules. */
    class Int64Rules implements IInt64Rules {

        /**
         * Constructs a new Int64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IInt64Rules);

        /** Int64Rules const. */
        public const: (number|Long);

        /** Int64Rules lt. */
        public lt: (number|Long);

        /** Int64Rules lte. */
        public lte: (number|Long);

        /** Int64Rules gt. */
        public gt: (number|Long);

        /** Int64Rules gte. */
        public gte: (number|Long);

        /** Int64Rules in. */
        public in: (number|Long)[];

        /** Int64Rules notIn. */
        public notIn: (number|Long)[];

        /** Int64Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified Int64Rules message. Does not implicitly {@link validate.Int64Rules.verify|verify} messages.
         * @param message Int64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an Int64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Int64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Int64Rules;

        /**
         * Verifies an Int64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an Int64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Int64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Int64Rules;

        /**
         * Creates a plain object from an Int64Rules message. Also converts values to other types if specified.
         * @param message Int64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Int64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Int64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Int64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UInt32Rules. */
    interface IUInt32Rules {

        /** UInt32Rules const */
        "const"?: (number|null);

        /** UInt32Rules lt */
        lt?: (number|null);

        /** UInt32Rules lte */
        lte?: (number|null);

        /** UInt32Rules gt */
        gt?: (number|null);

        /** UInt32Rules gte */
        gte?: (number|null);

        /** UInt32Rules in */
        "in"?: (number[]|null);

        /** UInt32Rules notIn */
        notIn?: (number[]|null);

        /** UInt32Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a UInt32Rules. */
    class UInt32Rules implements IUInt32Rules {

        /**
         * Constructs a new UInt32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IUInt32Rules);

        /** UInt32Rules const. */
        public const: number;

        /** UInt32Rules lt. */
        public lt: number;

        /** UInt32Rules lte. */
        public lte: number;

        /** UInt32Rules gt. */
        public gt: number;

        /** UInt32Rules gte. */
        public gte: number;

        /** UInt32Rules in. */
        public in: number[];

        /** UInt32Rules notIn. */
        public notIn: number[];

        /** UInt32Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified UInt32Rules message. Does not implicitly {@link validate.UInt32Rules.verify|verify} messages.
         * @param message UInt32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IUInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UInt32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UInt32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.UInt32Rules;

        /**
         * Verifies a UInt32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UInt32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UInt32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.UInt32Rules;

        /**
         * Creates a plain object from a UInt32Rules message. Also converts values to other types if specified.
         * @param message UInt32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.UInt32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UInt32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UInt32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a UInt64Rules. */
    interface IUInt64Rules {

        /** UInt64Rules const */
        "const"?: (number|Long|null);

        /** UInt64Rules lt */
        lt?: (number|Long|null);

        /** UInt64Rules lte */
        lte?: (number|Long|null);

        /** UInt64Rules gt */
        gt?: (number|Long|null);

        /** UInt64Rules gte */
        gte?: (number|Long|null);

        /** UInt64Rules in */
        "in"?: ((number|Long)[]|null);

        /** UInt64Rules notIn */
        notIn?: ((number|Long)[]|null);

        /** UInt64Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a UInt64Rules. */
    class UInt64Rules implements IUInt64Rules {

        /**
         * Constructs a new UInt64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IUInt64Rules);

        /** UInt64Rules const. */
        public const: (number|Long);

        /** UInt64Rules lt. */
        public lt: (number|Long);

        /** UInt64Rules lte. */
        public lte: (number|Long);

        /** UInt64Rules gt. */
        public gt: (number|Long);

        /** UInt64Rules gte. */
        public gte: (number|Long);

        /** UInt64Rules in. */
        public in: (number|Long)[];

        /** UInt64Rules notIn. */
        public notIn: (number|Long)[];

        /** UInt64Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified UInt64Rules message. Does not implicitly {@link validate.UInt64Rules.verify|verify} messages.
         * @param message UInt64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IUInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a UInt64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns UInt64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.UInt64Rules;

        /**
         * Verifies a UInt64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a UInt64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns UInt64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.UInt64Rules;

        /**
         * Creates a plain object from a UInt64Rules message. Also converts values to other types if specified.
         * @param message UInt64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.UInt64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this UInt64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for UInt64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SInt32Rules. */
    interface ISInt32Rules {

        /** SInt32Rules const */
        "const"?: (number|null);

        /** SInt32Rules lt */
        lt?: (number|null);

        /** SInt32Rules lte */
        lte?: (number|null);

        /** SInt32Rules gt */
        gt?: (number|null);

        /** SInt32Rules gte */
        gte?: (number|null);

        /** SInt32Rules in */
        "in"?: (number[]|null);

        /** SInt32Rules notIn */
        notIn?: (number[]|null);

        /** SInt32Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a SInt32Rules. */
    class SInt32Rules implements ISInt32Rules {

        /**
         * Constructs a new SInt32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISInt32Rules);

        /** SInt32Rules const. */
        public const: number;

        /** SInt32Rules lt. */
        public lt: number;

        /** SInt32Rules lte. */
        public lte: number;

        /** SInt32Rules gt. */
        public gt: number;

        /** SInt32Rules gte. */
        public gte: number;

        /** SInt32Rules in. */
        public in: number[];

        /** SInt32Rules notIn. */
        public notIn: number[];

        /** SInt32Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified SInt32Rules message. Does not implicitly {@link validate.SInt32Rules.verify|verify} messages.
         * @param message SInt32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISInt32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SInt32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SInt32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SInt32Rules;

        /**
         * Verifies a SInt32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SInt32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SInt32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SInt32Rules;

        /**
         * Creates a plain object from a SInt32Rules message. Also converts values to other types if specified.
         * @param message SInt32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SInt32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SInt32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SInt32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SInt64Rules. */
    interface ISInt64Rules {

        /** SInt64Rules const */
        "const"?: (number|Long|null);

        /** SInt64Rules lt */
        lt?: (number|Long|null);

        /** SInt64Rules lte */
        lte?: (number|Long|null);

        /** SInt64Rules gt */
        gt?: (number|Long|null);

        /** SInt64Rules gte */
        gte?: (number|Long|null);

        /** SInt64Rules in */
        "in"?: ((number|Long)[]|null);

        /** SInt64Rules notIn */
        notIn?: ((number|Long)[]|null);

        /** SInt64Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a SInt64Rules. */
    class SInt64Rules implements ISInt64Rules {

        /**
         * Constructs a new SInt64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISInt64Rules);

        /** SInt64Rules const. */
        public const: (number|Long);

        /** SInt64Rules lt. */
        public lt: (number|Long);

        /** SInt64Rules lte. */
        public lte: (number|Long);

        /** SInt64Rules gt. */
        public gt: (number|Long);

        /** SInt64Rules gte. */
        public gte: (number|Long);

        /** SInt64Rules in. */
        public in: (number|Long)[];

        /** SInt64Rules notIn. */
        public notIn: (number|Long)[];

        /** SInt64Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified SInt64Rules message. Does not implicitly {@link validate.SInt64Rules.verify|verify} messages.
         * @param message SInt64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISInt64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SInt64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SInt64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SInt64Rules;

        /**
         * Verifies a SInt64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SInt64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SInt64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SInt64Rules;

        /**
         * Creates a plain object from a SInt64Rules message. Also converts values to other types if specified.
         * @param message SInt64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SInt64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SInt64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SInt64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a Fixed32Rules. */
    interface IFixed32Rules {

        /** Fixed32Rules const */
        "const"?: (number|null);

        /** Fixed32Rules lt */
        lt?: (number|null);

        /** Fixed32Rules lte */
        lte?: (number|null);

        /** Fixed32Rules gt */
        gt?: (number|null);

        /** Fixed32Rules gte */
        gte?: (number|null);

        /** Fixed32Rules in */
        "in"?: (number[]|null);

        /** Fixed32Rules notIn */
        notIn?: (number[]|null);

        /** Fixed32Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a Fixed32Rules. */
    class Fixed32Rules implements IFixed32Rules {

        /**
         * Constructs a new Fixed32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFixed32Rules);

        /** Fixed32Rules const. */
        public const: number;

        /** Fixed32Rules lt. */
        public lt: number;

        /** Fixed32Rules lte. */
        public lte: number;

        /** Fixed32Rules gt. */
        public gt: number;

        /** Fixed32Rules gte. */
        public gte: number;

        /** Fixed32Rules in. */
        public in: number[];

        /** Fixed32Rules notIn. */
        public notIn: number[];

        /** Fixed32Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified Fixed32Rules message. Does not implicitly {@link validate.Fixed32Rules.verify|verify} messages.
         * @param message Fixed32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFixed32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Fixed32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Fixed32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Fixed32Rules;

        /**
         * Verifies a Fixed32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Fixed32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Fixed32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Fixed32Rules;

        /**
         * Creates a plain object from a Fixed32Rules message. Also converts values to other types if specified.
         * @param message Fixed32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Fixed32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Fixed32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Fixed32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a Fixed64Rules. */
    interface IFixed64Rules {

        /** Fixed64Rules const */
        "const"?: (number|Long|null);

        /** Fixed64Rules lt */
        lt?: (number|Long|null);

        /** Fixed64Rules lte */
        lte?: (number|Long|null);

        /** Fixed64Rules gt */
        gt?: (number|Long|null);

        /** Fixed64Rules gte */
        gte?: (number|Long|null);

        /** Fixed64Rules in */
        "in"?: ((number|Long)[]|null);

        /** Fixed64Rules notIn */
        notIn?: ((number|Long)[]|null);

        /** Fixed64Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a Fixed64Rules. */
    class Fixed64Rules implements IFixed64Rules {

        /**
         * Constructs a new Fixed64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IFixed64Rules);

        /** Fixed64Rules const. */
        public const: (number|Long);

        /** Fixed64Rules lt. */
        public lt: (number|Long);

        /** Fixed64Rules lte. */
        public lte: (number|Long);

        /** Fixed64Rules gt. */
        public gt: (number|Long);

        /** Fixed64Rules gte. */
        public gte: (number|Long);

        /** Fixed64Rules in. */
        public in: (number|Long)[];

        /** Fixed64Rules notIn. */
        public notIn: (number|Long)[];

        /** Fixed64Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified Fixed64Rules message. Does not implicitly {@link validate.Fixed64Rules.verify|verify} messages.
         * @param message Fixed64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IFixed64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Fixed64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Fixed64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.Fixed64Rules;

        /**
         * Verifies a Fixed64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Fixed64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Fixed64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.Fixed64Rules;

        /**
         * Creates a plain object from a Fixed64Rules message. Also converts values to other types if specified.
         * @param message Fixed64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.Fixed64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Fixed64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Fixed64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SFixed32Rules. */
    interface ISFixed32Rules {

        /** SFixed32Rules const */
        "const"?: (number|null);

        /** SFixed32Rules lt */
        lt?: (number|null);

        /** SFixed32Rules lte */
        lte?: (number|null);

        /** SFixed32Rules gt */
        gt?: (number|null);

        /** SFixed32Rules gte */
        gte?: (number|null);

        /** SFixed32Rules in */
        "in"?: (number[]|null);

        /** SFixed32Rules notIn */
        notIn?: (number[]|null);

        /** SFixed32Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a SFixed32Rules. */
    class SFixed32Rules implements ISFixed32Rules {

        /**
         * Constructs a new SFixed32Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISFixed32Rules);

        /** SFixed32Rules const. */
        public const: number;

        /** SFixed32Rules lt. */
        public lt: number;

        /** SFixed32Rules lte. */
        public lte: number;

        /** SFixed32Rules gt. */
        public gt: number;

        /** SFixed32Rules gte. */
        public gte: number;

        /** SFixed32Rules in. */
        public in: number[];

        /** SFixed32Rules notIn. */
        public notIn: number[];

        /** SFixed32Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified SFixed32Rules message. Does not implicitly {@link validate.SFixed32Rules.verify|verify} messages.
         * @param message SFixed32Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISFixed32Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SFixed32Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SFixed32Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SFixed32Rules;

        /**
         * Verifies a SFixed32Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SFixed32Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SFixed32Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SFixed32Rules;

        /**
         * Creates a plain object from a SFixed32Rules message. Also converts values to other types if specified.
         * @param message SFixed32Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SFixed32Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SFixed32Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SFixed32Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a SFixed64Rules. */
    interface ISFixed64Rules {

        /** SFixed64Rules const */
        "const"?: (number|Long|null);

        /** SFixed64Rules lt */
        lt?: (number|Long|null);

        /** SFixed64Rules lte */
        lte?: (number|Long|null);

        /** SFixed64Rules gt */
        gt?: (number|Long|null);

        /** SFixed64Rules gte */
        gte?: (number|Long|null);

        /** SFixed64Rules in */
        "in"?: ((number|Long)[]|null);

        /** SFixed64Rules notIn */
        notIn?: ((number|Long)[]|null);

        /** SFixed64Rules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a SFixed64Rules. */
    class SFixed64Rules implements ISFixed64Rules {

        /**
         * Constructs a new SFixed64Rules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ISFixed64Rules);

        /** SFixed64Rules const. */
        public const: (number|Long);

        /** SFixed64Rules lt. */
        public lt: (number|Long);

        /** SFixed64Rules lte. */
        public lte: (number|Long);

        /** SFixed64Rules gt. */
        public gt: (number|Long);

        /** SFixed64Rules gte. */
        public gte: (number|Long);

        /** SFixed64Rules in. */
        public in: (number|Long)[];

        /** SFixed64Rules notIn. */
        public notIn: (number|Long)[];

        /** SFixed64Rules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified SFixed64Rules message. Does not implicitly {@link validate.SFixed64Rules.verify|verify} messages.
         * @param message SFixed64Rules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ISFixed64Rules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a SFixed64Rules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns SFixed64Rules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.SFixed64Rules;

        /**
         * Verifies a SFixed64Rules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a SFixed64Rules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns SFixed64Rules
         */
        public static fromObject(object: { [k: string]: any }): validate.SFixed64Rules;

        /**
         * Creates a plain object from a SFixed64Rules message. Also converts values to other types if specified.
         * @param message SFixed64Rules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.SFixed64Rules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this SFixed64Rules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for SFixed64Rules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a BoolRules. */
    interface IBoolRules {

        /** BoolRules const */
        "const"?: (boolean|null);
    }

    /** Represents a BoolRules. */
    class BoolRules implements IBoolRules {

        /**
         * Constructs a new BoolRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IBoolRules);

        /** BoolRules const. */
        public const: boolean;

        /**
         * Encodes the specified BoolRules message. Does not implicitly {@link validate.BoolRules.verify|verify} messages.
         * @param message BoolRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IBoolRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BoolRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BoolRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.BoolRules;

        /**
         * Verifies a BoolRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BoolRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BoolRules
         */
        public static fromObject(object: { [k: string]: any }): validate.BoolRules;

        /**
         * Creates a plain object from a BoolRules message. Also converts values to other types if specified.
         * @param message BoolRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.BoolRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BoolRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BoolRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a StringRules. */
    interface IStringRules {

        /** StringRules const */
        "const"?: (string|null);

        /** StringRules len */
        len?: (number|Long|null);

        /** StringRules minLen */
        minLen?: (number|Long|null);

        /** StringRules maxLen */
        maxLen?: (number|Long|null);

        /** StringRules lenBytes */
        lenBytes?: (number|Long|null);

        /** StringRules minBytes */
        minBytes?: (number|Long|null);

        /** StringRules maxBytes */
        maxBytes?: (number|Long|null);

        /** StringRules pattern */
        pattern?: (string|null);

        /** StringRules prefix */
        prefix?: (string|null);

        /** StringRules suffix */
        suffix?: (string|null);

        /** StringRules contains */
        contains?: (string|null);

        /** StringRules notContains */
        notContains?: (string|null);

        /** StringRules in */
        "in"?: (string[]|null);

        /** StringRules notIn */
        notIn?: (string[]|null);

        /** StringRules email */
        email?: (boolean|null);

        /** StringRules hostname */
        hostname?: (boolean|null);

        /** StringRules ip */
        ip?: (boolean|null);

        /** StringRules ipv4 */
        ipv4?: (boolean|null);

        /** StringRules ipv6 */
        ipv6?: (boolean|null);

        /** StringRules uri */
        uri?: (boolean|null);

        /** StringRules uriRef */
        uriRef?: (boolean|null);

        /** StringRules address */
        address?: (boolean|null);

        /** StringRules uuid */
        uuid?: (boolean|null);

        /** StringRules wellKnownRegex */
        wellKnownRegex?: (validate.KnownRegex|null);

        /** StringRules alphabets */
        alphabets?: (boolean|null);

        /** StringRules alphanums */
        alphanums?: (boolean|null);

        /** StringRules lowercase */
        lowercase?: (boolean|null);

        /** StringRules tsecstr */
        tsecstr?: (boolean|null);

        /** StringRules json */
        json?: (boolean|null);

        /** StringRules strict */
        strict?: (boolean|null);

        /** StringRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a StringRules. */
    class StringRules implements IStringRules {

        /**
         * Constructs a new StringRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IStringRules);

        /** StringRules const. */
        public const: string;

        /** StringRules len. */
        public len: (number|Long);

        /** StringRules minLen. */
        public minLen: (number|Long);

        /** StringRules maxLen. */
        public maxLen: (number|Long);

        /** StringRules lenBytes. */
        public lenBytes: (number|Long);

        /** StringRules minBytes. */
        public minBytes: (number|Long);

        /** StringRules maxBytes. */
        public maxBytes: (number|Long);

        /** StringRules pattern. */
        public pattern: string;

        /** StringRules prefix. */
        public prefix: string;

        /** StringRules suffix. */
        public suffix: string;

        /** StringRules contains. */
        public contains: string;

        /** StringRules notContains. */
        public notContains: string;

        /** StringRules in. */
        public in: string[];

        /** StringRules notIn. */
        public notIn: string[];

        /** StringRules email. */
        public email?: (boolean|null);

        /** StringRules hostname. */
        public hostname?: (boolean|null);

        /** StringRules ip. */
        public ip?: (boolean|null);

        /** StringRules ipv4. */
        public ipv4?: (boolean|null);

        /** StringRules ipv6. */
        public ipv6?: (boolean|null);

        /** StringRules uri. */
        public uri?: (boolean|null);

        /** StringRules uriRef. */
        public uriRef?: (boolean|null);

        /** StringRules address. */
        public address?: (boolean|null);

        /** StringRules uuid. */
        public uuid?: (boolean|null);

        /** StringRules wellKnownRegex. */
        public wellKnownRegex?: (validate.KnownRegex|null);

        /** StringRules alphabets. */
        public alphabets?: (boolean|null);

        /** StringRules alphanums. */
        public alphanums?: (boolean|null);

        /** StringRules lowercase. */
        public lowercase?: (boolean|null);

        /** StringRules tsecstr. */
        public tsecstr?: (boolean|null);

        /** StringRules json. */
        public json?: (boolean|null);

        /** StringRules strict. */
        public strict: boolean;

        /** StringRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /** StringRules wellKnown. */
        public wellKnown?: ("email"|"hostname"|"ip"|"ipv4"|"ipv6"|"uri"|"uriRef"|"address"|"uuid"|"wellKnownRegex"|"alphabets"|"alphanums"|"lowercase"|"tsecstr"|"json");

        /**
         * Encodes the specified StringRules message. Does not implicitly {@link validate.StringRules.verify|verify} messages.
         * @param message StringRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IStringRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a StringRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns StringRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.StringRules;

        /**
         * Verifies a StringRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a StringRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns StringRules
         */
        public static fromObject(object: { [k: string]: any }): validate.StringRules;

        /**
         * Creates a plain object from a StringRules message. Also converts values to other types if specified.
         * @param message StringRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.StringRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this StringRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for StringRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** KnownRegex enum. */
    enum KnownRegex {
        UNKNOWN = 0,
        HTTP_HEADER_NAME = 1,
        HTTP_HEADER_VALUE = 2
    }

    /** Properties of a BytesRules. */
    interface IBytesRules {

        /** BytesRules const */
        "const"?: (Uint8Array|null);

        /** BytesRules len */
        len?: (number|Long|null);

        /** BytesRules minLen */
        minLen?: (number|Long|null);

        /** BytesRules maxLen */
        maxLen?: (number|Long|null);

        /** BytesRules pattern */
        pattern?: (string|null);

        /** BytesRules prefix */
        prefix?: (Uint8Array|null);

        /** BytesRules suffix */
        suffix?: (Uint8Array|null);

        /** BytesRules contains */
        contains?: (Uint8Array|null);

        /** BytesRules in */
        "in"?: (Uint8Array[]|null);

        /** BytesRules notIn */
        notIn?: (Uint8Array[]|null);

        /** BytesRules ip */
        ip?: (boolean|null);

        /** BytesRules ipv4 */
        ipv4?: (boolean|null);

        /** BytesRules ipv6 */
        ipv6?: (boolean|null);

        /** BytesRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a BytesRules. */
    class BytesRules implements IBytesRules {

        /**
         * Constructs a new BytesRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IBytesRules);

        /** BytesRules const. */
        public const: Uint8Array;

        /** BytesRules len. */
        public len: (number|Long);

        /** BytesRules minLen. */
        public minLen: (number|Long);

        /** BytesRules maxLen. */
        public maxLen: (number|Long);

        /** BytesRules pattern. */
        public pattern: string;

        /** BytesRules prefix. */
        public prefix: Uint8Array;

        /** BytesRules suffix. */
        public suffix: Uint8Array;

        /** BytesRules contains. */
        public contains: Uint8Array;

        /** BytesRules in. */
        public in: Uint8Array[];

        /** BytesRules notIn. */
        public notIn: Uint8Array[];

        /** BytesRules ip. */
        public ip?: (boolean|null);

        /** BytesRules ipv4. */
        public ipv4?: (boolean|null);

        /** BytesRules ipv6. */
        public ipv6?: (boolean|null);

        /** BytesRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /** BytesRules wellKnown. */
        public wellKnown?: ("ip"|"ipv4"|"ipv6");

        /**
         * Encodes the specified BytesRules message. Does not implicitly {@link validate.BytesRules.verify|verify} messages.
         * @param message BytesRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IBytesRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a BytesRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns BytesRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.BytesRules;

        /**
         * Verifies a BytesRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a BytesRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns BytesRules
         */
        public static fromObject(object: { [k: string]: any }): validate.BytesRules;

        /**
         * Creates a plain object from a BytesRules message. Also converts values to other types if specified.
         * @param message BytesRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.BytesRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this BytesRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for BytesRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an EnumRules. */
    interface IEnumRules {

        /** EnumRules const */
        "const"?: (number|null);

        /** EnumRules definedOnly */
        definedOnly?: (boolean|null);

        /** EnumRules in */
        "in"?: (number[]|null);

        /** EnumRules notIn */
        notIn?: (number[]|null);
    }

    /** Represents an EnumRules. */
    class EnumRules implements IEnumRules {

        /**
         * Constructs a new EnumRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IEnumRules);

        /** EnumRules const. */
        public const: number;

        /** EnumRules definedOnly. */
        public definedOnly: boolean;

        /** EnumRules in. */
        public in: number[];

        /** EnumRules notIn. */
        public notIn: number[];

        /**
         * Encodes the specified EnumRules message. Does not implicitly {@link validate.EnumRules.verify|verify} messages.
         * @param message EnumRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IEnumRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an EnumRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns EnumRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.EnumRules;

        /**
         * Verifies an EnumRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an EnumRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns EnumRules
         */
        public static fromObject(object: { [k: string]: any }): validate.EnumRules;

        /**
         * Creates a plain object from an EnumRules message. Also converts values to other types if specified.
         * @param message EnumRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.EnumRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this EnumRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for EnumRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a MessageRules. */
    interface IMessageRules {

        /** MessageRules skip */
        skip?: (boolean|null);

        /** MessageRules required */
        required?: (boolean|null);
    }

    /** Represents a MessageRules. */
    class MessageRules implements IMessageRules {

        /**
         * Constructs a new MessageRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IMessageRules);

        /** MessageRules skip. */
        public skip: boolean;

        /** MessageRules required. */
        public required: boolean;

        /**
         * Encodes the specified MessageRules message. Does not implicitly {@link validate.MessageRules.verify|verify} messages.
         * @param message MessageRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IMessageRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MessageRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MessageRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.MessageRules;

        /**
         * Verifies a MessageRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MessageRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MessageRules
         */
        public static fromObject(object: { [k: string]: any }): validate.MessageRules;

        /**
         * Creates a plain object from a MessageRules message. Also converts values to other types if specified.
         * @param message MessageRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.MessageRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MessageRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for MessageRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a RepeatedRules. */
    interface IRepeatedRules {

        /** RepeatedRules minItems */
        minItems?: (number|Long|null);

        /** RepeatedRules maxItems */
        maxItems?: (number|Long|null);

        /** RepeatedRules unique */
        unique?: (boolean|null);

        /** RepeatedRules items */
        items?: (validate.IFieldRules|null);

        /** RepeatedRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a RepeatedRules. */
    class RepeatedRules implements IRepeatedRules {

        /**
         * Constructs a new RepeatedRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IRepeatedRules);

        /** RepeatedRules minItems. */
        public minItems: (number|Long);

        /** RepeatedRules maxItems. */
        public maxItems: (number|Long);

        /** RepeatedRules unique. */
        public unique: boolean;

        /** RepeatedRules items. */
        public items?: (validate.IFieldRules|null);

        /** RepeatedRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified RepeatedRules message. Does not implicitly {@link validate.RepeatedRules.verify|verify} messages.
         * @param message RepeatedRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IRepeatedRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a RepeatedRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns RepeatedRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.RepeatedRules;

        /**
         * Verifies a RepeatedRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a RepeatedRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns RepeatedRules
         */
        public static fromObject(object: { [k: string]: any }): validate.RepeatedRules;

        /**
         * Creates a plain object from a RepeatedRules message. Also converts values to other types if specified.
         * @param message RepeatedRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.RepeatedRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this RepeatedRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for RepeatedRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a MapRules. */
    interface IMapRules {

        /** MapRules minPairs */
        minPairs?: (number|Long|null);

        /** MapRules maxPairs */
        maxPairs?: (number|Long|null);

        /** MapRules noSparse */
        noSparse?: (boolean|null);

        /** MapRules keys */
        keys?: (validate.IFieldRules|null);

        /** MapRules values */
        values?: (validate.IFieldRules|null);

        /** MapRules ignoreEmpty */
        ignoreEmpty?: (boolean|null);
    }

    /** Represents a MapRules. */
    class MapRules implements IMapRules {

        /**
         * Constructs a new MapRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IMapRules);

        /** MapRules minPairs. */
        public minPairs: (number|Long);

        /** MapRules maxPairs. */
        public maxPairs: (number|Long);

        /** MapRules noSparse. */
        public noSparse: boolean;

        /** MapRules keys. */
        public keys?: (validate.IFieldRules|null);

        /** MapRules values. */
        public values?: (validate.IFieldRules|null);

        /** MapRules ignoreEmpty. */
        public ignoreEmpty: boolean;

        /**
         * Encodes the specified MapRules message. Does not implicitly {@link validate.MapRules.verify|verify} messages.
         * @param message MapRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IMapRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a MapRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns MapRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.MapRules;

        /**
         * Verifies a MapRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a MapRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns MapRules
         */
        public static fromObject(object: { [k: string]: any }): validate.MapRules;

        /**
         * Creates a plain object from a MapRules message. Also converts values to other types if specified.
         * @param message MapRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.MapRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this MapRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for MapRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an AnyRules. */
    interface IAnyRules {

        /** AnyRules required */
        required?: (boolean|null);

        /** AnyRules in */
        "in"?: (string[]|null);

        /** AnyRules notIn */
        notIn?: (string[]|null);
    }

    /** Represents an AnyRules. */
    class AnyRules implements IAnyRules {

        /**
         * Constructs a new AnyRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IAnyRules);

        /** AnyRules required. */
        public required: boolean;

        /** AnyRules in. */
        public in: string[];

        /** AnyRules notIn. */
        public notIn: string[];

        /**
         * Encodes the specified AnyRules message. Does not implicitly {@link validate.AnyRules.verify|verify} messages.
         * @param message AnyRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IAnyRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an AnyRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns AnyRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.AnyRules;

        /**
         * Verifies an AnyRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an AnyRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns AnyRules
         */
        public static fromObject(object: { [k: string]: any }): validate.AnyRules;

        /**
         * Creates a plain object from an AnyRules message. Also converts values to other types if specified.
         * @param message AnyRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.AnyRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this AnyRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for AnyRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a DurationRules. */
    interface IDurationRules {

        /** DurationRules required */
        required?: (boolean|null);

        /** DurationRules const */
        "const"?: (google.protobuf.IDuration|null);

        /** DurationRules lt */
        lt?: (google.protobuf.IDuration|null);

        /** DurationRules lte */
        lte?: (google.protobuf.IDuration|null);

        /** DurationRules gt */
        gt?: (google.protobuf.IDuration|null);

        /** DurationRules gte */
        gte?: (google.protobuf.IDuration|null);

        /** DurationRules in */
        "in"?: (google.protobuf.IDuration[]|null);

        /** DurationRules notIn */
        notIn?: (google.protobuf.IDuration[]|null);
    }

    /** Represents a DurationRules. */
    class DurationRules implements IDurationRules {

        /**
         * Constructs a new DurationRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.IDurationRules);

        /** DurationRules required. */
        public required: boolean;

        /** DurationRules const. */
        public const?: (google.protobuf.IDuration|null);

        /** DurationRules lt. */
        public lt?: (google.protobuf.IDuration|null);

        /** DurationRules lte. */
        public lte?: (google.protobuf.IDuration|null);

        /** DurationRules gt. */
        public gt?: (google.protobuf.IDuration|null);

        /** DurationRules gte. */
        public gte?: (google.protobuf.IDuration|null);

        /** DurationRules in. */
        public in: google.protobuf.IDuration[];

        /** DurationRules notIn. */
        public notIn: google.protobuf.IDuration[];

        /**
         * Encodes the specified DurationRules message. Does not implicitly {@link validate.DurationRules.verify|verify} messages.
         * @param message DurationRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.IDurationRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a DurationRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns DurationRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.DurationRules;

        /**
         * Verifies a DurationRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a DurationRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns DurationRules
         */
        public static fromObject(object: { [k: string]: any }): validate.DurationRules;

        /**
         * Creates a plain object from a DurationRules message. Also converts values to other types if specified.
         * @param message DurationRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.DurationRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this DurationRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for DurationRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a TimestampRules. */
    interface ITimestampRules {

        /** TimestampRules required */
        required?: (boolean|null);

        /** TimestampRules const */
        "const"?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt */
        lt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lte */
        lte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gt */
        gt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gte */
        gte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules ltNow */
        ltNow?: (boolean|null);

        /** TimestampRules gtNow */
        gtNow?: (boolean|null);

        /** TimestampRules within */
        within?: (google.protobuf.IDuration|null);
    }

    /** Represents a TimestampRules. */
    class TimestampRules implements ITimestampRules {

        /**
         * Constructs a new TimestampRules.
         * @param [properties] Properties to set
         */
        constructor(properties?: validate.ITimestampRules);

        /** TimestampRules required. */
        public required: boolean;

        /** TimestampRules const. */
        public const?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lt. */
        public lt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules lte. */
        public lte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gt. */
        public gt?: (google.protobuf.ITimestamp|null);

        /** TimestampRules gte. */
        public gte?: (google.protobuf.ITimestamp|null);

        /** TimestampRules ltNow. */
        public ltNow: boolean;

        /** TimestampRules gtNow. */
        public gtNow: boolean;

        /** TimestampRules within. */
        public within?: (google.protobuf.IDuration|null);

        /**
         * Encodes the specified TimestampRules message. Does not implicitly {@link validate.TimestampRules.verify|verify} messages.
         * @param message TimestampRules message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: validate.ITimestampRules, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a TimestampRules message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns TimestampRules
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): validate.TimestampRules;

        /**
         * Verifies a TimestampRules message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a TimestampRules message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns TimestampRules
         */
        public static fromObject(object: { [k: string]: any }): validate.TimestampRules;

        /**
         * Creates a plain object from a TimestampRules message. Also converts values to other types if specified.
         * @param message TimestampRules
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: validate.TimestampRules, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this TimestampRules to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for TimestampRules
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

/** Namespace google. */
export namespace google {

    /** Namespace protobuf. */
    namespace protobuf {

        /** Properties of a FileDescriptorSet. */
        interface IFileDescriptorSet {

            /** FileDescriptorSet file */
            file?: (google.protobuf.IFileDescriptorProto[]|null);
        }

        /** Represents a FileDescriptorSet. */
        class FileDescriptorSet implements IFileDescriptorSet {

            /**
             * Constructs a new FileDescriptorSet.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorSet);

            /** FileDescriptorSet file. */
            public file: google.protobuf.IFileDescriptorProto[];

            /**
             * Encodes the specified FileDescriptorSet message. Does not implicitly {@link google.protobuf.FileDescriptorSet.verify|verify} messages.
             * @param message FileDescriptorSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorSet message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorSet;

            /**
             * Verifies a FileDescriptorSet message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorSet message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorSet
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorSet;

            /**
             * Creates a plain object from a FileDescriptorSet message. Also converts values to other types if specified.
             * @param message FileDescriptorSet
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorSet, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorSet to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileDescriptorSet
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Edition enum. */
        enum Edition {
            EDITION_UNKNOWN = 0,
            EDITION_LEGACY = 900,
            EDITION_PROTO2 = 998,
            EDITION_PROTO3 = 999,
            EDITION_2023 = 1000,
            EDITION_2024 = 1001,
            EDITION_1_TEST_ONLY = 1,
            EDITION_2_TEST_ONLY = 2,
            EDITION_99997_TEST_ONLY = 99997,
            EDITION_99998_TEST_ONLY = 99998,
            EDITION_99999_TEST_ONLY = 99999,
            EDITION_MAX = 2147483647
        }

        /** Properties of a FileDescriptorProto. */
        interface IFileDescriptorProto {

            /** FileDescriptorProto name */
            name?: (string|null);

            /** FileDescriptorProto package */
            "package"?: (string|null);

            /** FileDescriptorProto dependency */
            dependency?: (string[]|null);

            /** FileDescriptorProto publicDependency */
            publicDependency?: (number[]|null);

            /** FileDescriptorProto weakDependency */
            weakDependency?: (number[]|null);

            /** FileDescriptorProto optionDependency */
            optionDependency?: (string[]|null);

            /** FileDescriptorProto messageType */
            messageType?: (google.protobuf.IDescriptorProto[]|null);

            /** FileDescriptorProto enumType */
            enumType?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** FileDescriptorProto service */
            service?: (google.protobuf.IServiceDescriptorProto[]|null);

            /** FileDescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** FileDescriptorProto options */
            options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto sourceCodeInfo */
            sourceCodeInfo?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax */
            syntax?: (string|null);

            /** FileDescriptorProto edition */
            edition?: (google.protobuf.Edition|null);
        }

        /** Represents a FileDescriptorProto. */
        class FileDescriptorProto implements IFileDescriptorProto {

            /**
             * Constructs a new FileDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileDescriptorProto);

            /** FileDescriptorProto name. */
            public name: string;

            /** FileDescriptorProto package. */
            public package: string;

            /** FileDescriptorProto dependency. */
            public dependency: string[];

            /** FileDescriptorProto publicDependency. */
            public publicDependency: number[];

            /** FileDescriptorProto weakDependency. */
            public weakDependency: number[];

            /** FileDescriptorProto optionDependency. */
            public optionDependency: string[];

            /** FileDescriptorProto messageType. */
            public messageType: google.protobuf.IDescriptorProto[];

            /** FileDescriptorProto enumType. */
            public enumType: google.protobuf.IEnumDescriptorProto[];

            /** FileDescriptorProto service. */
            public service: google.protobuf.IServiceDescriptorProto[];

            /** FileDescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** FileDescriptorProto options. */
            public options?: (google.protobuf.IFileOptions|null);

            /** FileDescriptorProto sourceCodeInfo. */
            public sourceCodeInfo?: (google.protobuf.ISourceCodeInfo|null);

            /** FileDescriptorProto syntax. */
            public syntax: string;

            /** FileDescriptorProto edition. */
            public edition: google.protobuf.Edition;

            /**
             * Encodes the specified FileDescriptorProto message. Does not implicitly {@link google.protobuf.FileDescriptorProto.verify|verify} messages.
             * @param message FileDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileDescriptorProto;

            /**
             * Verifies a FileDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileDescriptorProto;

            /**
             * Creates a plain object from a FileDescriptorProto message. Also converts values to other types if specified.
             * @param message FileDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a DescriptorProto. */
        interface IDescriptorProto {

            /** DescriptorProto name */
            name?: (string|null);

            /** DescriptorProto field */
            field?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto extension */
            extension?: (google.protobuf.IFieldDescriptorProto[]|null);

            /** DescriptorProto nestedType */
            nestedType?: (google.protobuf.IDescriptorProto[]|null);

            /** DescriptorProto enumType */
            enumType?: (google.protobuf.IEnumDescriptorProto[]|null);

            /** DescriptorProto extensionRange */
            extensionRange?: (google.protobuf.DescriptorProto.IExtensionRange[]|null);

            /** DescriptorProto oneofDecl */
            oneofDecl?: (google.protobuf.IOneofDescriptorProto[]|null);

            /** DescriptorProto options */
            options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reservedRange */
            reservedRange?: (google.protobuf.DescriptorProto.IReservedRange[]|null);

            /** DescriptorProto reservedName */
            reservedName?: (string[]|null);

            /** DescriptorProto visibility */
            visibility?: (google.protobuf.SymbolVisibility|null);
        }

        /** Represents a DescriptorProto. */
        class DescriptorProto implements IDescriptorProto {

            /**
             * Constructs a new DescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDescriptorProto);

            /** DescriptorProto name. */
            public name: string;

            /** DescriptorProto field. */
            public field: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto extension. */
            public extension: google.protobuf.IFieldDescriptorProto[];

            /** DescriptorProto nestedType. */
            public nestedType: google.protobuf.IDescriptorProto[];

            /** DescriptorProto enumType. */
            public enumType: google.protobuf.IEnumDescriptorProto[];

            /** DescriptorProto extensionRange. */
            public extensionRange: google.protobuf.DescriptorProto.IExtensionRange[];

            /** DescriptorProto oneofDecl. */
            public oneofDecl: google.protobuf.IOneofDescriptorProto[];

            /** DescriptorProto options. */
            public options?: (google.protobuf.IMessageOptions|null);

            /** DescriptorProto reservedRange. */
            public reservedRange: google.protobuf.DescriptorProto.IReservedRange[];

            /** DescriptorProto reservedName. */
            public reservedName: string[];

            /** DescriptorProto visibility. */
            public visibility: google.protobuf.SymbolVisibility;

            /**
             * Encodes the specified DescriptorProto message. Does not implicitly {@link google.protobuf.DescriptorProto.verify|verify} messages.
             * @param message DescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a DescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns DescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto;

            /**
             * Verifies a DescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a DescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns DescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto;

            /**
             * Creates a plain object from a DescriptorProto message. Also converts values to other types if specified.
             * @param message DescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.DescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this DescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for DescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace DescriptorProto {

            /** Properties of an ExtensionRange. */
            interface IExtensionRange {

                /** ExtensionRange start */
                start?: (number|null);

                /** ExtensionRange end */
                end?: (number|null);

                /** ExtensionRange options */
                options?: (google.protobuf.IExtensionRangeOptions|null);
            }

            /** Represents an ExtensionRange. */
            class ExtensionRange implements IExtensionRange {

                /**
                 * Constructs a new ExtensionRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IExtensionRange);

                /** ExtensionRange start. */
                public start: number;

                /** ExtensionRange end. */
                public end: number;

                /** ExtensionRange options. */
                public options?: (google.protobuf.IExtensionRangeOptions|null);

                /**
                 * Encodes the specified ExtensionRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ExtensionRange.verify|verify} messages.
                 * @param message ExtensionRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IExtensionRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an ExtensionRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ExtensionRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Verifies an ExtensionRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an ExtensionRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ExtensionRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ExtensionRange;

                /**
                 * Creates a plain object from an ExtensionRange message. Also converts values to other types if specified.
                 * @param message ExtensionRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ExtensionRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ExtensionRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ExtensionRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a ReservedRange. */
            interface IReservedRange {

                /** ReservedRange start */
                start?: (number|null);

                /** ReservedRange end */
                end?: (number|null);
            }

            /** Represents a ReservedRange. */
            class ReservedRange implements IReservedRange {

                /**
                 * Constructs a new ReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.DescriptorProto.IReservedRange);

                /** ReservedRange start. */
                public start: number;

                /** ReservedRange end. */
                public end: number;

                /**
                 * Encodes the specified ReservedRange message. Does not implicitly {@link google.protobuf.DescriptorProto.ReservedRange.verify|verify} messages.
                 * @param message ReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.DescriptorProto.IReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a ReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns ReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Verifies a ReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a ReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns ReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.DescriptorProto.ReservedRange;

                /**
                 * Creates a plain object from a ReservedRange message. Also converts values to other types if specified.
                 * @param message ReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.DescriptorProto.ReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this ReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for ReservedRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an ExtensionRangeOptions. */
        interface IExtensionRangeOptions {

            /** ExtensionRangeOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);

            /** ExtensionRangeOptions declaration */
            declaration?: (google.protobuf.ExtensionRangeOptions.IDeclaration[]|null);

            /** ExtensionRangeOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** ExtensionRangeOptions verification */
            verification?: (google.protobuf.ExtensionRangeOptions.VerificationState|null);
        }

        /** Represents an ExtensionRangeOptions. */
        class ExtensionRangeOptions implements IExtensionRangeOptions {

            /**
             * Constructs a new ExtensionRangeOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IExtensionRangeOptions);

            /** ExtensionRangeOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /** ExtensionRangeOptions declaration. */
            public declaration: google.protobuf.ExtensionRangeOptions.IDeclaration[];

            /** ExtensionRangeOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** ExtensionRangeOptions verification. */
            public verification: google.protobuf.ExtensionRangeOptions.VerificationState;

            /**
             * Encodes the specified ExtensionRangeOptions message. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.verify|verify} messages.
             * @param message ExtensionRangeOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IExtensionRangeOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an ExtensionRangeOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ExtensionRangeOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ExtensionRangeOptions;

            /**
             * Verifies an ExtensionRangeOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an ExtensionRangeOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ExtensionRangeOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ExtensionRangeOptions;

            /**
             * Creates a plain object from an ExtensionRangeOptions message. Also converts values to other types if specified.
             * @param message ExtensionRangeOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ExtensionRangeOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ExtensionRangeOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ExtensionRangeOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace ExtensionRangeOptions {

            /** Properties of a Declaration. */
            interface IDeclaration {

                /** Declaration number */
                number?: (number|null);

                /** Declaration fullName */
                fullName?: (string|null);

                /** Declaration type */
                type?: (string|null);

                /** Declaration reserved */
                reserved?: (boolean|null);

                /** Declaration repeated */
                repeated?: (boolean|null);
            }

            /** Represents a Declaration. */
            class Declaration implements IDeclaration {

                /**
                 * Constructs a new Declaration.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.ExtensionRangeOptions.IDeclaration);

                /** Declaration number. */
                public number: number;

                /** Declaration fullName. */
                public fullName: string;

                /** Declaration type. */
                public type: string;

                /** Declaration reserved. */
                public reserved: boolean;

                /** Declaration repeated. */
                public repeated: boolean;

                /**
                 * Encodes the specified Declaration message. Does not implicitly {@link google.protobuf.ExtensionRangeOptions.Declaration.verify|verify} messages.
                 * @param message Declaration message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.ExtensionRangeOptions.IDeclaration, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Declaration message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Declaration
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ExtensionRangeOptions.Declaration;

                /**
                 * Verifies a Declaration message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Declaration message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Declaration
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.ExtensionRangeOptions.Declaration;

                /**
                 * Creates a plain object from a Declaration message. Also converts values to other types if specified.
                 * @param message Declaration
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.ExtensionRangeOptions.Declaration, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Declaration to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Declaration
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** VerificationState enum. */
            enum VerificationState {
                DECLARATION = 0,
                UNVERIFIED = 1
            }
        }

        /** Properties of a FieldDescriptorProto. */
        interface IFieldDescriptorProto {

            /** FieldDescriptorProto name */
            name?: (string|null);

            /** FieldDescriptorProto number */
            number?: (number|null);

            /** FieldDescriptorProto label */
            label?: (google.protobuf.FieldDescriptorProto.Label|null);

            /** FieldDescriptorProto type */
            type?: (google.protobuf.FieldDescriptorProto.Type|null);

            /** FieldDescriptorProto typeName */
            typeName?: (string|null);

            /** FieldDescriptorProto extendee */
            extendee?: (string|null);

            /** FieldDescriptorProto defaultValue */
            defaultValue?: (string|null);

            /** FieldDescriptorProto oneofIndex */
            oneofIndex?: (number|null);

            /** FieldDescriptorProto jsonName */
            jsonName?: (string|null);

            /** FieldDescriptorProto options */
            options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3Optional */
            proto3Optional?: (boolean|null);
        }

        /** Represents a FieldDescriptorProto. */
        class FieldDescriptorProto implements IFieldDescriptorProto {

            /**
             * Constructs a new FieldDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldDescriptorProto);

            /** FieldDescriptorProto name. */
            public name: string;

            /** FieldDescriptorProto number. */
            public number: number;

            /** FieldDescriptorProto label. */
            public label: google.protobuf.FieldDescriptorProto.Label;

            /** FieldDescriptorProto type. */
            public type: google.protobuf.FieldDescriptorProto.Type;

            /** FieldDescriptorProto typeName. */
            public typeName: string;

            /** FieldDescriptorProto extendee. */
            public extendee: string;

            /** FieldDescriptorProto defaultValue. */
            public defaultValue: string;

            /** FieldDescriptorProto oneofIndex. */
            public oneofIndex: number;

            /** FieldDescriptorProto jsonName. */
            public jsonName: string;

            /** FieldDescriptorProto options. */
            public options?: (google.protobuf.IFieldOptions|null);

            /** FieldDescriptorProto proto3Optional. */
            public proto3Optional: boolean;

            /**
             * Encodes the specified FieldDescriptorProto message. Does not implicitly {@link google.protobuf.FieldDescriptorProto.verify|verify} messages.
             * @param message FieldDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldDescriptorProto;

            /**
             * Verifies a FieldDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldDescriptorProto;

            /**
             * Creates a plain object from a FieldDescriptorProto message. Also converts values to other types if specified.
             * @param message FieldDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FieldDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FieldDescriptorProto {

            /** Type enum. */
            enum Type {
                TYPE_DOUBLE = 1,
                TYPE_FLOAT = 2,
                TYPE_INT64 = 3,
                TYPE_UINT64 = 4,
                TYPE_INT32 = 5,
                TYPE_FIXED64 = 6,
                TYPE_FIXED32 = 7,
                TYPE_BOOL = 8,
                TYPE_STRING = 9,
                TYPE_GROUP = 10,
                TYPE_MESSAGE = 11,
                TYPE_BYTES = 12,
                TYPE_UINT32 = 13,
                TYPE_ENUM = 14,
                TYPE_SFIXED32 = 15,
                TYPE_SFIXED64 = 16,
                TYPE_SINT32 = 17,
                TYPE_SINT64 = 18
            }

            /** Label enum. */
            enum Label {
                LABEL_OPTIONAL = 1,
                LABEL_REPEATED = 3,
                LABEL_REQUIRED = 2
            }
        }

        /** Properties of an OneofDescriptorProto. */
        interface IOneofDescriptorProto {

            /** OneofDescriptorProto name */
            name?: (string|null);

            /** OneofDescriptorProto options */
            options?: (google.protobuf.IOneofOptions|null);
        }

        /** Represents an OneofDescriptorProto. */
        class OneofDescriptorProto implements IOneofDescriptorProto {

            /**
             * Constructs a new OneofDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofDescriptorProto);

            /** OneofDescriptorProto name. */
            public name: string;

            /** OneofDescriptorProto options. */
            public options?: (google.protobuf.IOneofOptions|null);

            /**
             * Encodes the specified OneofDescriptorProto message. Does not implicitly {@link google.protobuf.OneofDescriptorProto.verify|verify} messages.
             * @param message OneofDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofDescriptorProto;

            /**
             * Verifies an OneofDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofDescriptorProto;

            /**
             * Creates a plain object from an OneofDescriptorProto message. Also converts values to other types if specified.
             * @param message OneofDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for OneofDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumDescriptorProto. */
        interface IEnumDescriptorProto {

            /** EnumDescriptorProto name */
            name?: (string|null);

            /** EnumDescriptorProto value */
            value?: (google.protobuf.IEnumValueDescriptorProto[]|null);

            /** EnumDescriptorProto options */
            options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reservedRange */
            reservedRange?: (google.protobuf.EnumDescriptorProto.IEnumReservedRange[]|null);

            /** EnumDescriptorProto reservedName */
            reservedName?: (string[]|null);

            /** EnumDescriptorProto visibility */
            visibility?: (google.protobuf.SymbolVisibility|null);
        }

        /** Represents an EnumDescriptorProto. */
        class EnumDescriptorProto implements IEnumDescriptorProto {

            /**
             * Constructs a new EnumDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumDescriptorProto);

            /** EnumDescriptorProto name. */
            public name: string;

            /** EnumDescriptorProto value. */
            public value: google.protobuf.IEnumValueDescriptorProto[];

            /** EnumDescriptorProto options. */
            public options?: (google.protobuf.IEnumOptions|null);

            /** EnumDescriptorProto reservedRange. */
            public reservedRange: google.protobuf.EnumDescriptorProto.IEnumReservedRange[];

            /** EnumDescriptorProto reservedName. */
            public reservedName: string[];

            /** EnumDescriptorProto visibility. */
            public visibility: google.protobuf.SymbolVisibility;

            /**
             * Encodes the specified EnumDescriptorProto message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.verify|verify} messages.
             * @param message EnumDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto;

            /**
             * Verifies an EnumDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto;

            /**
             * Creates a plain object from an EnumDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace EnumDescriptorProto {

            /** Properties of an EnumReservedRange. */
            interface IEnumReservedRange {

                /** EnumReservedRange start */
                start?: (number|null);

                /** EnumReservedRange end */
                end?: (number|null);
            }

            /** Represents an EnumReservedRange. */
            class EnumReservedRange implements IEnumReservedRange {

                /**
                 * Constructs a new EnumReservedRange.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.EnumDescriptorProto.IEnumReservedRange);

                /** EnumReservedRange start. */
                public start: number;

                /** EnumReservedRange end. */
                public end: number;

                /**
                 * Encodes the specified EnumReservedRange message. Does not implicitly {@link google.protobuf.EnumDescriptorProto.EnumReservedRange.verify|verify} messages.
                 * @param message EnumReservedRange message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.EnumDescriptorProto.IEnumReservedRange, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an EnumReservedRange message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns EnumReservedRange
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Verifies an EnumReservedRange message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an EnumReservedRange message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns EnumReservedRange
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.EnumDescriptorProto.EnumReservedRange;

                /**
                 * Creates a plain object from an EnumReservedRange message. Also converts values to other types if specified.
                 * @param message EnumReservedRange
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.EnumDescriptorProto.EnumReservedRange, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this EnumReservedRange to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for EnumReservedRange
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an EnumValueDescriptorProto. */
        interface IEnumValueDescriptorProto {

            /** EnumValueDescriptorProto name */
            name?: (string|null);

            /** EnumValueDescriptorProto number */
            number?: (number|null);

            /** EnumValueDescriptorProto options */
            options?: (google.protobuf.IEnumValueOptions|null);
        }

        /** Represents an EnumValueDescriptorProto. */
        class EnumValueDescriptorProto implements IEnumValueDescriptorProto {

            /**
             * Constructs a new EnumValueDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueDescriptorProto);

            /** EnumValueDescriptorProto name. */
            public name: string;

            /** EnumValueDescriptorProto number. */
            public number: number;

            /** EnumValueDescriptorProto options. */
            public options?: (google.protobuf.IEnumValueOptions|null);

            /**
             * Encodes the specified EnumValueDescriptorProto message. Does not implicitly {@link google.protobuf.EnumValueDescriptorProto.verify|verify} messages.
             * @param message EnumValueDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueDescriptorProto;

            /**
             * Verifies an EnumValueDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueDescriptorProto;

            /**
             * Creates a plain object from an EnumValueDescriptorProto message. Also converts values to other types if specified.
             * @param message EnumValueDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumValueDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a ServiceDescriptorProto. */
        interface IServiceDescriptorProto {

            /** ServiceDescriptorProto name */
            name?: (string|null);

            /** ServiceDescriptorProto method */
            method?: (google.protobuf.IMethodDescriptorProto[]|null);

            /** ServiceDescriptorProto options */
            options?: (google.protobuf.IServiceOptions|null);
        }

        /** Represents a ServiceDescriptorProto. */
        class ServiceDescriptorProto implements IServiceDescriptorProto {

            /**
             * Constructs a new ServiceDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceDescriptorProto);

            /** ServiceDescriptorProto name. */
            public name: string;

            /** ServiceDescriptorProto method. */
            public method: google.protobuf.IMethodDescriptorProto[];

            /** ServiceDescriptorProto options. */
            public options?: (google.protobuf.IServiceOptions|null);

            /**
             * Encodes the specified ServiceDescriptorProto message. Does not implicitly {@link google.protobuf.ServiceDescriptorProto.verify|verify} messages.
             * @param message ServiceDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceDescriptorProto;

            /**
             * Verifies a ServiceDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceDescriptorProto;

            /**
             * Creates a plain object from a ServiceDescriptorProto message. Also converts values to other types if specified.
             * @param message ServiceDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ServiceDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a MethodDescriptorProto. */
        interface IMethodDescriptorProto {

            /** MethodDescriptorProto name */
            name?: (string|null);

            /** MethodDescriptorProto inputType */
            inputType?: (string|null);

            /** MethodDescriptorProto outputType */
            outputType?: (string|null);

            /** MethodDescriptorProto options */
            options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto clientStreaming */
            clientStreaming?: (boolean|null);

            /** MethodDescriptorProto serverStreaming */
            serverStreaming?: (boolean|null);
        }

        /** Represents a MethodDescriptorProto. */
        class MethodDescriptorProto implements IMethodDescriptorProto {

            /**
             * Constructs a new MethodDescriptorProto.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodDescriptorProto);

            /** MethodDescriptorProto name. */
            public name: string;

            /** MethodDescriptorProto inputType. */
            public inputType: string;

            /** MethodDescriptorProto outputType. */
            public outputType: string;

            /** MethodDescriptorProto options. */
            public options?: (google.protobuf.IMethodOptions|null);

            /** MethodDescriptorProto clientStreaming. */
            public clientStreaming: boolean;

            /** MethodDescriptorProto serverStreaming. */
            public serverStreaming: boolean;

            /**
             * Encodes the specified MethodDescriptorProto message. Does not implicitly {@link google.protobuf.MethodDescriptorProto.verify|verify} messages.
             * @param message MethodDescriptorProto message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodDescriptorProto, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodDescriptorProto message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodDescriptorProto
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodDescriptorProto;

            /**
             * Verifies a MethodDescriptorProto message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodDescriptorProto message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodDescriptorProto
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodDescriptorProto;

            /**
             * Creates a plain object from a MethodDescriptorProto message. Also converts values to other types if specified.
             * @param message MethodDescriptorProto
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodDescriptorProto, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodDescriptorProto to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MethodDescriptorProto
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FileOptions. */
        interface IFileOptions {

            /** FileOptions javaPackage */
            javaPackage?: (string|null);

            /** FileOptions javaOuterClassname */
            javaOuterClassname?: (string|null);

            /** FileOptions javaMultipleFiles */
            javaMultipleFiles?: (boolean|null);

            /** FileOptions javaGenerateEqualsAndHash */
            javaGenerateEqualsAndHash?: (boolean|null);

            /** FileOptions javaStringCheckUtf8 */
            javaStringCheckUtf8?: (boolean|null);

            /** FileOptions optimizeFor */
            optimizeFor?: (google.protobuf.FileOptions.OptimizeMode|null);

            /** FileOptions goPackage */
            goPackage?: (string|null);

            /** FileOptions ccGenericServices */
            ccGenericServices?: (boolean|null);

            /** FileOptions javaGenericServices */
            javaGenericServices?: (boolean|null);

            /** FileOptions pyGenericServices */
            pyGenericServices?: (boolean|null);

            /** FileOptions deprecated */
            deprecated?: (boolean|null);

            /** FileOptions ccEnableArenas */
            ccEnableArenas?: (boolean|null);

            /** FileOptions objcClassPrefix */
            objcClassPrefix?: (string|null);

            /** FileOptions csharpNamespace */
            csharpNamespace?: (string|null);

            /** FileOptions swiftPrefix */
            swiftPrefix?: (string|null);

            /** FileOptions phpClassPrefix */
            phpClassPrefix?: (string|null);

            /** FileOptions phpNamespace */
            phpNamespace?: (string|null);

            /** FileOptions phpMetadataNamespace */
            phpMetadataNamespace?: (string|null);

            /** FileOptions rubyPackage */
            rubyPackage?: (string|null);

            /** FileOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** FileOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a FileOptions. */
        class FileOptions implements IFileOptions {

            /**
             * Constructs a new FileOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFileOptions);

            /** FileOptions javaPackage. */
            public javaPackage: string;

            /** FileOptions javaOuterClassname. */
            public javaOuterClassname: string;

            /** FileOptions javaMultipleFiles. */
            public javaMultipleFiles: boolean;

            /** FileOptions javaGenerateEqualsAndHash. */
            public javaGenerateEqualsAndHash: boolean;

            /** FileOptions javaStringCheckUtf8. */
            public javaStringCheckUtf8: boolean;

            /** FileOptions optimizeFor. */
            public optimizeFor: google.protobuf.FileOptions.OptimizeMode;

            /** FileOptions goPackage. */
            public goPackage: string;

            /** FileOptions ccGenericServices. */
            public ccGenericServices: boolean;

            /** FileOptions javaGenericServices. */
            public javaGenericServices: boolean;

            /** FileOptions pyGenericServices. */
            public pyGenericServices: boolean;

            /** FileOptions deprecated. */
            public deprecated: boolean;

            /** FileOptions ccEnableArenas. */
            public ccEnableArenas: boolean;

            /** FileOptions objcClassPrefix. */
            public objcClassPrefix: string;

            /** FileOptions csharpNamespace. */
            public csharpNamespace: string;

            /** FileOptions swiftPrefix. */
            public swiftPrefix: string;

            /** FileOptions phpClassPrefix. */
            public phpClassPrefix: string;

            /** FileOptions phpNamespace. */
            public phpNamespace: string;

            /** FileOptions phpMetadataNamespace. */
            public phpMetadataNamespace: string;

            /** FileOptions rubyPackage. */
            public rubyPackage: string;

            /** FileOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** FileOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified FileOptions message. Does not implicitly {@link google.protobuf.FileOptions.verify|verify} messages.
             * @param message FileOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFileOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FileOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FileOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FileOptions;

            /**
             * Verifies a FileOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FileOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FileOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FileOptions;

            /**
             * Creates a plain object from a FileOptions message. Also converts values to other types if specified.
             * @param message FileOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FileOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FileOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FileOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FileOptions {

            /** OptimizeMode enum. */
            enum OptimizeMode {
                SPEED = 1,
                CODE_SIZE = 2,
                LITE_RUNTIME = 3
            }
        }

        /** Properties of a MessageOptions. */
        interface IMessageOptions {

            /** MessageOptions messageSetWireFormat */
            messageSetWireFormat?: (boolean|null);

            /** MessageOptions noStandardDescriptorAccessor */
            noStandardDescriptorAccessor?: (boolean|null);

            /** MessageOptions deprecated */
            deprecated?: (boolean|null);

            /** MessageOptions mapEntry */
            mapEntry?: (boolean|null);

            /** MessageOptions deprecatedLegacyJsonFieldConflicts */
            deprecatedLegacyJsonFieldConflicts?: (boolean|null);

            /** MessageOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** MessageOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);

            /** MessageOptions .validate.disabled */
            ".validate.disabled"?: (boolean|null);

            /** MessageOptions .validate.ignored */
            ".validate.ignored"?: (boolean|null);
        }

        /** Represents a MessageOptions. */
        class MessageOptions implements IMessageOptions {

            /**
             * Constructs a new MessageOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMessageOptions);

            /** MessageOptions messageSetWireFormat. */
            public messageSetWireFormat: boolean;

            /** MessageOptions noStandardDescriptorAccessor. */
            public noStandardDescriptorAccessor: boolean;

            /** MessageOptions deprecated. */
            public deprecated: boolean;

            /** MessageOptions mapEntry. */
            public mapEntry: boolean;

            /** MessageOptions deprecatedLegacyJsonFieldConflicts. */
            public deprecatedLegacyJsonFieldConflicts: boolean;

            /** MessageOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** MessageOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified MessageOptions message. Does not implicitly {@link google.protobuf.MessageOptions.verify|verify} messages.
             * @param message MessageOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMessageOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MessageOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MessageOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MessageOptions;

            /**
             * Verifies a MessageOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MessageOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MessageOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MessageOptions;

            /**
             * Creates a plain object from a MessageOptions message. Also converts values to other types if specified.
             * @param message MessageOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MessageOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MessageOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MessageOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a FieldOptions. */
        interface IFieldOptions {

            /** FieldOptions ctype */
            ctype?: (google.protobuf.FieldOptions.CType|null);

            /** FieldOptions packed */
            packed?: (boolean|null);

            /** FieldOptions jstype */
            jstype?: (google.protobuf.FieldOptions.JSType|null);

            /** FieldOptions lazy */
            lazy?: (boolean|null);

            /** FieldOptions unverifiedLazy */
            unverifiedLazy?: (boolean|null);

            /** FieldOptions deprecated */
            deprecated?: (boolean|null);

            /** FieldOptions weak */
            weak?: (boolean|null);

            /** FieldOptions debugRedact */
            debugRedact?: (boolean|null);

            /** FieldOptions retention */
            retention?: (google.protobuf.FieldOptions.OptionRetention|null);

            /** FieldOptions targets */
            targets?: (google.protobuf.FieldOptions.OptionTargetType[]|null);

            /** FieldOptions editionDefaults */
            editionDefaults?: (google.protobuf.FieldOptions.IEditionDefault[]|null);

            /** FieldOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** FieldOptions featureSupport */
            featureSupport?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** FieldOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);

            /** FieldOptions .validate.rules */
            ".validate.rules"?: (validate.IFieldRules|null);
        }

        /** Represents a FieldOptions. */
        class FieldOptions implements IFieldOptions {

            /**
             * Constructs a new FieldOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFieldOptions);

            /** FieldOptions ctype. */
            public ctype: google.protobuf.FieldOptions.CType;

            /** FieldOptions packed. */
            public packed: boolean;

            /** FieldOptions jstype. */
            public jstype: google.protobuf.FieldOptions.JSType;

            /** FieldOptions lazy. */
            public lazy: boolean;

            /** FieldOptions unverifiedLazy. */
            public unverifiedLazy: boolean;

            /** FieldOptions deprecated. */
            public deprecated: boolean;

            /** FieldOptions weak. */
            public weak: boolean;

            /** FieldOptions debugRedact. */
            public debugRedact: boolean;

            /** FieldOptions retention. */
            public retention: google.protobuf.FieldOptions.OptionRetention;

            /** FieldOptions targets. */
            public targets: google.protobuf.FieldOptions.OptionTargetType[];

            /** FieldOptions editionDefaults. */
            public editionDefaults: google.protobuf.FieldOptions.IEditionDefault[];

            /** FieldOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** FieldOptions featureSupport. */
            public featureSupport?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** FieldOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified FieldOptions message. Does not implicitly {@link google.protobuf.FieldOptions.verify|verify} messages.
             * @param message FieldOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFieldOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FieldOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FieldOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions;

            /**
             * Verifies a FieldOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FieldOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FieldOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions;

            /**
             * Creates a plain object from a FieldOptions message. Also converts values to other types if specified.
             * @param message FieldOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FieldOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FieldOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FieldOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FieldOptions {

            /** CType enum. */
            enum CType {
                STRING = 0,
                CORD = 1,
                STRING_PIECE = 2
            }

            /** JSType enum. */
            enum JSType {
                JS_NORMAL = 0,
                JS_STRING = 1,
                JS_NUMBER = 2
            }

            /** OptionRetention enum. */
            enum OptionRetention {
                RETENTION_UNKNOWN = 0,
                RETENTION_RUNTIME = 1,
                RETENTION_SOURCE = 2
            }

            /** OptionTargetType enum. */
            enum OptionTargetType {
                TARGET_TYPE_UNKNOWN = 0,
                TARGET_TYPE_FILE = 1,
                TARGET_TYPE_EXTENSION_RANGE = 2,
                TARGET_TYPE_MESSAGE = 3,
                TARGET_TYPE_FIELD = 4,
                TARGET_TYPE_ONEOF = 5,
                TARGET_TYPE_ENUM = 6,
                TARGET_TYPE_ENUM_ENTRY = 7,
                TARGET_TYPE_SERVICE = 8,
                TARGET_TYPE_METHOD = 9
            }

            /** Properties of an EditionDefault. */
            interface IEditionDefault {

                /** EditionDefault edition */
                edition?: (google.protobuf.Edition|null);

                /** EditionDefault value */
                value?: (string|null);
            }

            /** Represents an EditionDefault. */
            class EditionDefault implements IEditionDefault {

                /**
                 * Constructs a new EditionDefault.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FieldOptions.IEditionDefault);

                /** EditionDefault edition. */
                public edition: google.protobuf.Edition;

                /** EditionDefault value. */
                public value: string;

                /**
                 * Encodes the specified EditionDefault message. Does not implicitly {@link google.protobuf.FieldOptions.EditionDefault.verify|verify} messages.
                 * @param message EditionDefault message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FieldOptions.IEditionDefault, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an EditionDefault message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns EditionDefault
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions.EditionDefault;

                /**
                 * Verifies an EditionDefault message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an EditionDefault message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns EditionDefault
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions.EditionDefault;

                /**
                 * Creates a plain object from an EditionDefault message. Also converts values to other types if specified.
                 * @param message EditionDefault
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FieldOptions.EditionDefault, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this EditionDefault to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for EditionDefault
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            /** Properties of a FeatureSupport. */
            interface IFeatureSupport {

                /** FeatureSupport editionIntroduced */
                editionIntroduced?: (google.protobuf.Edition|null);

                /** FeatureSupport editionDeprecated */
                editionDeprecated?: (google.protobuf.Edition|null);

                /** FeatureSupport deprecationWarning */
                deprecationWarning?: (string|null);

                /** FeatureSupport editionRemoved */
                editionRemoved?: (google.protobuf.Edition|null);
            }

            /** Represents a FeatureSupport. */
            class FeatureSupport implements IFeatureSupport {

                /**
                 * Constructs a new FeatureSupport.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FieldOptions.IFeatureSupport);

                /** FeatureSupport editionIntroduced. */
                public editionIntroduced: google.protobuf.Edition;

                /** FeatureSupport editionDeprecated. */
                public editionDeprecated: google.protobuf.Edition;

                /** FeatureSupport deprecationWarning. */
                public deprecationWarning: string;

                /** FeatureSupport editionRemoved. */
                public editionRemoved: google.protobuf.Edition;

                /**
                 * Encodes the specified FeatureSupport message. Does not implicitly {@link google.protobuf.FieldOptions.FeatureSupport.verify|verify} messages.
                 * @param message FeatureSupport message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FieldOptions.IFeatureSupport, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FeatureSupport message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FeatureSupport
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FieldOptions.FeatureSupport;

                /**
                 * Verifies a FeatureSupport message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FeatureSupport message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FeatureSupport
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FieldOptions.FeatureSupport;

                /**
                 * Creates a plain object from a FeatureSupport message. Also converts values to other types if specified.
                 * @param message FeatureSupport
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FieldOptions.FeatureSupport, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FeatureSupport to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FeatureSupport
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of an OneofOptions. */
        interface IOneofOptions {

            /** OneofOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** OneofOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);

            /** OneofOptions .validate.required */
            ".validate.required"?: (boolean|null);
        }

        /** Represents an OneofOptions. */
        class OneofOptions implements IOneofOptions {

            /**
             * Constructs a new OneofOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IOneofOptions);

            /** OneofOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** OneofOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified OneofOptions message. Does not implicitly {@link google.protobuf.OneofOptions.verify|verify} messages.
             * @param message OneofOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IOneofOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an OneofOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns OneofOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.OneofOptions;

            /**
             * Verifies an OneofOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an OneofOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns OneofOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.OneofOptions;

            /**
             * Creates a plain object from an OneofOptions message. Also converts values to other types if specified.
             * @param message OneofOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.OneofOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this OneofOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for OneofOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumOptions. */
        interface IEnumOptions {

            /** EnumOptions allowAlias */
            allowAlias?: (boolean|null);

            /** EnumOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumOptions deprecatedLegacyJsonFieldConflicts */
            deprecatedLegacyJsonFieldConflicts?: (boolean|null);

            /** EnumOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** EnumOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumOptions. */
        class EnumOptions implements IEnumOptions {

            /**
             * Constructs a new EnumOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumOptions);

            /** EnumOptions allowAlias. */
            public allowAlias: boolean;

            /** EnumOptions deprecated. */
            public deprecated: boolean;

            /** EnumOptions deprecatedLegacyJsonFieldConflicts. */
            public deprecatedLegacyJsonFieldConflicts: boolean;

            /** EnumOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** EnumOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified EnumOptions message. Does not implicitly {@link google.protobuf.EnumOptions.verify|verify} messages.
             * @param message EnumOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumOptions;

            /**
             * Verifies an EnumOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumOptions;

            /**
             * Creates a plain object from an EnumOptions message. Also converts values to other types if specified.
             * @param message EnumOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of an EnumValueOptions. */
        interface IEnumValueOptions {

            /** EnumValueOptions deprecated */
            deprecated?: (boolean|null);

            /** EnumValueOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** EnumValueOptions debugRedact */
            debugRedact?: (boolean|null);

            /** EnumValueOptions featureSupport */
            featureSupport?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** EnumValueOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents an EnumValueOptions. */
        class EnumValueOptions implements IEnumValueOptions {

            /**
             * Constructs a new EnumValueOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IEnumValueOptions);

            /** EnumValueOptions deprecated. */
            public deprecated: boolean;

            /** EnumValueOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** EnumValueOptions debugRedact. */
            public debugRedact: boolean;

            /** EnumValueOptions featureSupport. */
            public featureSupport?: (google.protobuf.FieldOptions.IFeatureSupport|null);

            /** EnumValueOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified EnumValueOptions message. Does not implicitly {@link google.protobuf.EnumValueOptions.verify|verify} messages.
             * @param message EnumValueOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IEnumValueOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an EnumValueOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns EnumValueOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.EnumValueOptions;

            /**
             * Verifies an EnumValueOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an EnumValueOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns EnumValueOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.EnumValueOptions;

            /**
             * Creates a plain object from an EnumValueOptions message. Also converts values to other types if specified.
             * @param message EnumValueOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.EnumValueOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this EnumValueOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for EnumValueOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a ServiceOptions. */
        interface IServiceOptions {

            /** ServiceOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** ServiceOptions deprecated */
            deprecated?: (boolean|null);

            /** ServiceOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a ServiceOptions. */
        class ServiceOptions implements IServiceOptions {

            /**
             * Constructs a new ServiceOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IServiceOptions);

            /** ServiceOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** ServiceOptions deprecated. */
            public deprecated: boolean;

            /** ServiceOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified ServiceOptions message. Does not implicitly {@link google.protobuf.ServiceOptions.verify|verify} messages.
             * @param message ServiceOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IServiceOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a ServiceOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns ServiceOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.ServiceOptions;

            /**
             * Verifies a ServiceOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a ServiceOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns ServiceOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.ServiceOptions;

            /**
             * Creates a plain object from a ServiceOptions message. Also converts values to other types if specified.
             * @param message ServiceOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.ServiceOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this ServiceOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for ServiceOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a MethodOptions. */
        interface IMethodOptions {

            /** MethodOptions deprecated */
            deprecated?: (boolean|null);

            /** MethodOptions idempotencyLevel */
            idempotencyLevel?: (google.protobuf.MethodOptions.IdempotencyLevel|null);

            /** MethodOptions features */
            features?: (google.protobuf.IFeatureSet|null);

            /** MethodOptions uninterpretedOption */
            uninterpretedOption?: (google.protobuf.IUninterpretedOption[]|null);
        }

        /** Represents a MethodOptions. */
        class MethodOptions implements IMethodOptions {

            /**
             * Constructs a new MethodOptions.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IMethodOptions);

            /** MethodOptions deprecated. */
            public deprecated: boolean;

            /** MethodOptions idempotencyLevel. */
            public idempotencyLevel: google.protobuf.MethodOptions.IdempotencyLevel;

            /** MethodOptions features. */
            public features?: (google.protobuf.IFeatureSet|null);

            /** MethodOptions uninterpretedOption. */
            public uninterpretedOption: google.protobuf.IUninterpretedOption[];

            /**
             * Encodes the specified MethodOptions message. Does not implicitly {@link google.protobuf.MethodOptions.verify|verify} messages.
             * @param message MethodOptions message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IMethodOptions, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a MethodOptions message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns MethodOptions
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.MethodOptions;

            /**
             * Verifies a MethodOptions message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a MethodOptions message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns MethodOptions
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.MethodOptions;

            /**
             * Creates a plain object from a MethodOptions message. Also converts values to other types if specified.
             * @param message MethodOptions
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.MethodOptions, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this MethodOptions to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for MethodOptions
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace MethodOptions {

            /** IdempotencyLevel enum. */
            enum IdempotencyLevel {
                IDEMPOTENCY_UNKNOWN = 0,
                NO_SIDE_EFFECTS = 1,
                IDEMPOTENT = 2
            }
        }

        /** Properties of an UninterpretedOption. */
        interface IUninterpretedOption {

            /** UninterpretedOption name */
            name?: (google.protobuf.UninterpretedOption.INamePart[]|null);

            /** UninterpretedOption identifierValue */
            identifierValue?: (string|null);

            /** UninterpretedOption positiveIntValue */
            positiveIntValue?: (number|Long|null);

            /** UninterpretedOption negativeIntValue */
            negativeIntValue?: (number|Long|null);

            /** UninterpretedOption doubleValue */
            doubleValue?: (number|null);

            /** UninterpretedOption stringValue */
            stringValue?: (Uint8Array|null);

            /** UninterpretedOption aggregateValue */
            aggregateValue?: (string|null);
        }

        /** Represents an UninterpretedOption. */
        class UninterpretedOption implements IUninterpretedOption {

            /**
             * Constructs a new UninterpretedOption.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IUninterpretedOption);

            /** UninterpretedOption name. */
            public name: google.protobuf.UninterpretedOption.INamePart[];

            /** UninterpretedOption identifierValue. */
            public identifierValue: string;

            /** UninterpretedOption positiveIntValue. */
            public positiveIntValue: (number|Long);

            /** UninterpretedOption negativeIntValue. */
            public negativeIntValue: (number|Long);

            /** UninterpretedOption doubleValue. */
            public doubleValue: number;

            /** UninterpretedOption stringValue. */
            public stringValue: Uint8Array;

            /** UninterpretedOption aggregateValue. */
            public aggregateValue: string;

            /**
             * Encodes the specified UninterpretedOption message. Does not implicitly {@link google.protobuf.UninterpretedOption.verify|verify} messages.
             * @param message UninterpretedOption message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IUninterpretedOption, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes an UninterpretedOption message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns UninterpretedOption
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption;

            /**
             * Verifies an UninterpretedOption message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates an UninterpretedOption message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns UninterpretedOption
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption;

            /**
             * Creates a plain object from an UninterpretedOption message. Also converts values to other types if specified.
             * @param message UninterpretedOption
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.UninterpretedOption, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this UninterpretedOption to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for UninterpretedOption
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace UninterpretedOption {

            /** Properties of a NamePart. */
            interface INamePart {

                /** NamePart namePart */
                namePart: string;

                /** NamePart isExtension */
                isExtension: boolean;
            }

            /** Represents a NamePart. */
            class NamePart implements INamePart {

                /**
                 * Constructs a new NamePart.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.UninterpretedOption.INamePart);

                /** NamePart namePart. */
                public namePart: string;

                /** NamePart isExtension. */
                public isExtension: boolean;

                /**
                 * Encodes the specified NamePart message. Does not implicitly {@link google.protobuf.UninterpretedOption.NamePart.verify|verify} messages.
                 * @param message NamePart message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.UninterpretedOption.INamePart, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a NamePart message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns NamePart
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Verifies a NamePart message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a NamePart message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns NamePart
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.UninterpretedOption.NamePart;

                /**
                 * Creates a plain object from a NamePart message. Also converts values to other types if specified.
                 * @param message NamePart
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.UninterpretedOption.NamePart, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this NamePart to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for NamePart
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a FeatureSet. */
        interface IFeatureSet {

            /** FeatureSet fieldPresence */
            fieldPresence?: (google.protobuf.FeatureSet.FieldPresence|null);

            /** FeatureSet enumType */
            enumType?: (google.protobuf.FeatureSet.EnumType|null);

            /** FeatureSet repeatedFieldEncoding */
            repeatedFieldEncoding?: (google.protobuf.FeatureSet.RepeatedFieldEncoding|null);

            /** FeatureSet utf8Validation */
            utf8Validation?: (google.protobuf.FeatureSet.Utf8Validation|null);

            /** FeatureSet messageEncoding */
            messageEncoding?: (google.protobuf.FeatureSet.MessageEncoding|null);

            /** FeatureSet jsonFormat */
            jsonFormat?: (google.protobuf.FeatureSet.JsonFormat|null);

            /** FeatureSet enforceNamingStyle */
            enforceNamingStyle?: (google.protobuf.FeatureSet.EnforceNamingStyle|null);

            /** FeatureSet defaultSymbolVisibility */
            defaultSymbolVisibility?: (google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility|null);
        }

        /** Represents a FeatureSet. */
        class FeatureSet implements IFeatureSet {

            /**
             * Constructs a new FeatureSet.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFeatureSet);

            /** FeatureSet fieldPresence. */
            public fieldPresence: google.protobuf.FeatureSet.FieldPresence;

            /** FeatureSet enumType. */
            public enumType: google.protobuf.FeatureSet.EnumType;

            /** FeatureSet repeatedFieldEncoding. */
            public repeatedFieldEncoding: google.protobuf.FeatureSet.RepeatedFieldEncoding;

            /** FeatureSet utf8Validation. */
            public utf8Validation: google.protobuf.FeatureSet.Utf8Validation;

            /** FeatureSet messageEncoding. */
            public messageEncoding: google.protobuf.FeatureSet.MessageEncoding;

            /** FeatureSet jsonFormat. */
            public jsonFormat: google.protobuf.FeatureSet.JsonFormat;

            /** FeatureSet enforceNamingStyle. */
            public enforceNamingStyle: google.protobuf.FeatureSet.EnforceNamingStyle;

            /** FeatureSet defaultSymbolVisibility. */
            public defaultSymbolVisibility: google.protobuf.FeatureSet.VisibilityFeature.DefaultSymbolVisibility;

            /**
             * Encodes the specified FeatureSet message. Does not implicitly {@link google.protobuf.FeatureSet.verify|verify} messages.
             * @param message FeatureSet message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFeatureSet, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FeatureSet message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FeatureSet
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSet;

            /**
             * Verifies a FeatureSet message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FeatureSet message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FeatureSet
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSet;

            /**
             * Creates a plain object from a FeatureSet message. Also converts values to other types if specified.
             * @param message FeatureSet
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FeatureSet, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FeatureSet to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FeatureSet
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FeatureSet {

            /** FieldPresence enum. */
            enum FieldPresence {
                FIELD_PRESENCE_UNKNOWN = 0,
                EXPLICIT = 1,
                IMPLICIT = 2,
                LEGACY_REQUIRED = 3
            }

            /** EnumType enum. */
            enum EnumType {
                ENUM_TYPE_UNKNOWN = 0,
                OPEN = 1,
                CLOSED = 2
            }

            /** RepeatedFieldEncoding enum. */
            enum RepeatedFieldEncoding {
                REPEATED_FIELD_ENCODING_UNKNOWN = 0,
                PACKED = 1,
                EXPANDED = 2
            }

            /** Utf8Validation enum. */
            enum Utf8Validation {
                UTF8_VALIDATION_UNKNOWN = 0,
                VERIFY = 2,
                NONE = 3
            }

            /** MessageEncoding enum. */
            enum MessageEncoding {
                MESSAGE_ENCODING_UNKNOWN = 0,
                LENGTH_PREFIXED = 1,
                DELIMITED = 2
            }

            /** JsonFormat enum. */
            enum JsonFormat {
                JSON_FORMAT_UNKNOWN = 0,
                ALLOW = 1,
                LEGACY_BEST_EFFORT = 2
            }

            /** EnforceNamingStyle enum. */
            enum EnforceNamingStyle {
                ENFORCE_NAMING_STYLE_UNKNOWN = 0,
                STYLE2024 = 1,
                STYLE_LEGACY = 2
            }

            /** Properties of a VisibilityFeature. */
            interface IVisibilityFeature {
            }

            /** Represents a VisibilityFeature. */
            class VisibilityFeature implements IVisibilityFeature {

                /**
                 * Constructs a new VisibilityFeature.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FeatureSet.IVisibilityFeature);

                /**
                 * Encodes the specified VisibilityFeature message. Does not implicitly {@link google.protobuf.FeatureSet.VisibilityFeature.verify|verify} messages.
                 * @param message VisibilityFeature message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FeatureSet.IVisibilityFeature, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a VisibilityFeature message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns VisibilityFeature
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSet.VisibilityFeature;

                /**
                 * Verifies a VisibilityFeature message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a VisibilityFeature message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns VisibilityFeature
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSet.VisibilityFeature;

                /**
                 * Creates a plain object from a VisibilityFeature message. Also converts values to other types if specified.
                 * @param message VisibilityFeature
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FeatureSet.VisibilityFeature, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this VisibilityFeature to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for VisibilityFeature
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            namespace VisibilityFeature {

                /** DefaultSymbolVisibility enum. */
                enum DefaultSymbolVisibility {
                    DEFAULT_SYMBOL_VISIBILITY_UNKNOWN = 0,
                    EXPORT_ALL = 1,
                    EXPORT_TOP_LEVEL = 2,
                    LOCAL_ALL = 3,
                    STRICT = 4
                }
            }
        }

        /** Properties of a FeatureSetDefaults. */
        interface IFeatureSetDefaults {

            /** FeatureSetDefaults defaults */
            defaults?: (google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault[]|null);

            /** FeatureSetDefaults minimumEdition */
            minimumEdition?: (google.protobuf.Edition|null);

            /** FeatureSetDefaults maximumEdition */
            maximumEdition?: (google.protobuf.Edition|null);
        }

        /** Represents a FeatureSetDefaults. */
        class FeatureSetDefaults implements IFeatureSetDefaults {

            /**
             * Constructs a new FeatureSetDefaults.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IFeatureSetDefaults);

            /** FeatureSetDefaults defaults. */
            public defaults: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault[];

            /** FeatureSetDefaults minimumEdition. */
            public minimumEdition: google.protobuf.Edition;

            /** FeatureSetDefaults maximumEdition. */
            public maximumEdition: google.protobuf.Edition;

            /**
             * Encodes the specified FeatureSetDefaults message. Does not implicitly {@link google.protobuf.FeatureSetDefaults.verify|verify} messages.
             * @param message FeatureSetDefaults message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IFeatureSetDefaults, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a FeatureSetDefaults message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns FeatureSetDefaults
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSetDefaults;

            /**
             * Verifies a FeatureSetDefaults message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a FeatureSetDefaults message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns FeatureSetDefaults
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSetDefaults;

            /**
             * Creates a plain object from a FeatureSetDefaults message. Also converts values to other types if specified.
             * @param message FeatureSetDefaults
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.FeatureSetDefaults, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this FeatureSetDefaults to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for FeatureSetDefaults
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace FeatureSetDefaults {

            /** Properties of a FeatureSetEditionDefault. */
            interface IFeatureSetEditionDefault {

                /** FeatureSetEditionDefault edition */
                edition?: (google.protobuf.Edition|null);

                /** FeatureSetEditionDefault overridableFeatures */
                overridableFeatures?: (google.protobuf.IFeatureSet|null);

                /** FeatureSetEditionDefault fixedFeatures */
                fixedFeatures?: (google.protobuf.IFeatureSet|null);
            }

            /** Represents a FeatureSetEditionDefault. */
            class FeatureSetEditionDefault implements IFeatureSetEditionDefault {

                /**
                 * Constructs a new FeatureSetEditionDefault.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault);

                /** FeatureSetEditionDefault edition. */
                public edition: google.protobuf.Edition;

                /** FeatureSetEditionDefault overridableFeatures. */
                public overridableFeatures?: (google.protobuf.IFeatureSet|null);

                /** FeatureSetEditionDefault fixedFeatures. */
                public fixedFeatures?: (google.protobuf.IFeatureSet|null);

                /**
                 * Encodes the specified FeatureSetEditionDefault message. Does not implicitly {@link google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault.verify|verify} messages.
                 * @param message FeatureSetEditionDefault message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.FeatureSetDefaults.IFeatureSetEditionDefault, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a FeatureSetEditionDefault message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns FeatureSetEditionDefault
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;

                /**
                 * Verifies a FeatureSetEditionDefault message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a FeatureSetEditionDefault message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns FeatureSetEditionDefault
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault;

                /**
                 * Creates a plain object from a FeatureSetEditionDefault message. Also converts values to other types if specified.
                 * @param message FeatureSetEditionDefault
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this FeatureSetEditionDefault to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for FeatureSetEditionDefault
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a SourceCodeInfo. */
        interface ISourceCodeInfo {

            /** SourceCodeInfo location */
            location?: (google.protobuf.SourceCodeInfo.ILocation[]|null);
        }

        /** Represents a SourceCodeInfo. */
        class SourceCodeInfo implements ISourceCodeInfo {

            /**
             * Constructs a new SourceCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.ISourceCodeInfo);

            /** SourceCodeInfo location. */
            public location: google.protobuf.SourceCodeInfo.ILocation[];

            /**
             * Encodes the specified SourceCodeInfo message. Does not implicitly {@link google.protobuf.SourceCodeInfo.verify|verify} messages.
             * @param message SourceCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.ISourceCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a SourceCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns SourceCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo;

            /**
             * Verifies a SourceCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a SourceCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns SourceCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo;

            /**
             * Creates a plain object from a SourceCodeInfo message. Also converts values to other types if specified.
             * @param message SourceCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.SourceCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this SourceCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for SourceCodeInfo
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace SourceCodeInfo {

            /** Properties of a Location. */
            interface ILocation {

                /** Location path */
                path?: (number[]|null);

                /** Location span */
                span?: (number[]|null);

                /** Location leadingComments */
                leadingComments?: (string|null);

                /** Location trailingComments */
                trailingComments?: (string|null);

                /** Location leadingDetachedComments */
                leadingDetachedComments?: (string[]|null);
            }

            /** Represents a Location. */
            class Location implements ILocation {

                /**
                 * Constructs a new Location.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.SourceCodeInfo.ILocation);

                /** Location path. */
                public path: number[];

                /** Location span. */
                public span: number[];

                /** Location leadingComments. */
                public leadingComments: string;

                /** Location trailingComments. */
                public trailingComments: string;

                /** Location leadingDetachedComments. */
                public leadingDetachedComments: string[];

                /**
                 * Encodes the specified Location message. Does not implicitly {@link google.protobuf.SourceCodeInfo.Location.verify|verify} messages.
                 * @param message Location message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.SourceCodeInfo.ILocation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes a Location message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Location
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Verifies a Location message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates a Location message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Location
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.SourceCodeInfo.Location;

                /**
                 * Creates a plain object from a Location message. Also converts values to other types if specified.
                 * @param message Location
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.SourceCodeInfo.Location, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Location to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Location
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }
        }

        /** Properties of a GeneratedCodeInfo. */
        interface IGeneratedCodeInfo {

            /** GeneratedCodeInfo annotation */
            annotation?: (google.protobuf.GeneratedCodeInfo.IAnnotation[]|null);
        }

        /** Represents a GeneratedCodeInfo. */
        class GeneratedCodeInfo implements IGeneratedCodeInfo {

            /**
             * Constructs a new GeneratedCodeInfo.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IGeneratedCodeInfo);

            /** GeneratedCodeInfo annotation. */
            public annotation: google.protobuf.GeneratedCodeInfo.IAnnotation[];

            /**
             * Encodes the specified GeneratedCodeInfo message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.verify|verify} messages.
             * @param message GeneratedCodeInfo message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IGeneratedCodeInfo, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a GeneratedCodeInfo message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns GeneratedCodeInfo
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo;

            /**
             * Verifies a GeneratedCodeInfo message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a GeneratedCodeInfo message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns GeneratedCodeInfo
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo;

            /**
             * Creates a plain object from a GeneratedCodeInfo message. Also converts values to other types if specified.
             * @param message GeneratedCodeInfo
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.GeneratedCodeInfo, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this GeneratedCodeInfo to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for GeneratedCodeInfo
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        namespace GeneratedCodeInfo {

            /** Properties of an Annotation. */
            interface IAnnotation {

                /** Annotation path */
                path?: (number[]|null);

                /** Annotation sourceFile */
                sourceFile?: (string|null);

                /** Annotation begin */
                begin?: (number|null);

                /** Annotation end */
                end?: (number|null);

                /** Annotation semantic */
                semantic?: (google.protobuf.GeneratedCodeInfo.Annotation.Semantic|null);
            }

            /** Represents an Annotation. */
            class Annotation implements IAnnotation {

                /**
                 * Constructs a new Annotation.
                 * @param [properties] Properties to set
                 */
                constructor(properties?: google.protobuf.GeneratedCodeInfo.IAnnotation);

                /** Annotation path. */
                public path: number[];

                /** Annotation sourceFile. */
                public sourceFile: string;

                /** Annotation begin. */
                public begin: number;

                /** Annotation end. */
                public end: number;

                /** Annotation semantic. */
                public semantic: google.protobuf.GeneratedCodeInfo.Annotation.Semantic;

                /**
                 * Encodes the specified Annotation message. Does not implicitly {@link google.protobuf.GeneratedCodeInfo.Annotation.verify|verify} messages.
                 * @param message Annotation message or plain object to encode
                 * @param [writer] Writer to encode to
                 * @returns Writer
                 */
                public static encode(message: google.protobuf.GeneratedCodeInfo.IAnnotation, writer?: $protobuf.Writer): $protobuf.Writer;

                /**
                 * Decodes an Annotation message from the specified reader or buffer.
                 * @param reader Reader or buffer to decode from
                 * @param [length] Message length if known beforehand
                 * @returns Annotation
                 * @throws {Error} If the payload is not a reader or valid buffer
                 * @throws {$protobuf.util.ProtocolError} If required fields are missing
                 */
                public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Verifies an Annotation message.
                 * @param message Plain object to verify
                 * @returns `null` if valid, otherwise the reason why it is not
                 */
                public static verify(message: { [k: string]: any }): (string|null);

                /**
                 * Creates an Annotation message from a plain object. Also converts values to their respective internal types.
                 * @param object Plain object
                 * @returns Annotation
                 */
                public static fromObject(object: { [k: string]: any }): google.protobuf.GeneratedCodeInfo.Annotation;

                /**
                 * Creates a plain object from an Annotation message. Also converts values to other types if specified.
                 * @param message Annotation
                 * @param [options] Conversion options
                 * @returns Plain object
                 */
                public static toObject(message: google.protobuf.GeneratedCodeInfo.Annotation, options?: $protobuf.IConversionOptions): { [k: string]: any };

                /**
                 * Converts this Annotation to JSON.
                 * @returns JSON object
                 */
                public toJSON(): { [k: string]: any };

                /**
                 * Gets the default type url for Annotation
                 * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
                 * @returns The default type url
                 */
                public static getTypeUrl(typeUrlPrefix?: string): string;
            }

            namespace Annotation {

                /** Semantic enum. */
                enum Semantic {
                    NONE = 0,
                    SET = 1,
                    ALIAS = 2
                }
            }
        }

        /** SymbolVisibility enum. */
        enum SymbolVisibility {
            VISIBILITY_UNSET = 0,
            VISIBILITY_LOCAL = 1,
            VISIBILITY_EXPORT = 2
        }

        /** Properties of a Duration. */
        interface IDuration {

            /** Duration seconds */
            seconds?: (number|Long|null);

            /** Duration nanos */
            nanos?: (number|null);
        }

        /** Represents a Duration. */
        class Duration implements IDuration {

            /**
             * Constructs a new Duration.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.IDuration);

            /** Duration seconds. */
            public seconds: (number|Long);

            /** Duration nanos. */
            public nanos: number;

            /**
             * Encodes the specified Duration message. Does not implicitly {@link google.protobuf.Duration.verify|verify} messages.
             * @param message Duration message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.IDuration, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Duration message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Duration
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Duration;

            /**
             * Verifies a Duration message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Duration message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Duration
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Duration;

            /**
             * Creates a plain object from a Duration message. Also converts values to other types if specified.
             * @param message Duration
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Duration, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Duration to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Duration
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }

        /** Properties of a Timestamp. */
        interface ITimestamp {

            /** Timestamp seconds */
            seconds?: (number|Long|null);

            /** Timestamp nanos */
            nanos?: (number|null);
        }

        /** Represents a Timestamp. */
        class Timestamp implements ITimestamp {

            /**
             * Constructs a new Timestamp.
             * @param [properties] Properties to set
             */
            constructor(properties?: google.protobuf.ITimestamp);

            /** Timestamp seconds. */
            public seconds: (number|Long);

            /** Timestamp nanos. */
            public nanos: number;

            /**
             * Encodes the specified Timestamp message. Does not implicitly {@link google.protobuf.Timestamp.verify|verify} messages.
             * @param message Timestamp message or plain object to encode
             * @param [writer] Writer to encode to
             * @returns Writer
             */
            public static encode(message: google.protobuf.ITimestamp, writer?: $protobuf.Writer): $protobuf.Writer;

            /**
             * Decodes a Timestamp message from the specified reader or buffer.
             * @param reader Reader or buffer to decode from
             * @param [length] Message length if known beforehand
             * @returns Timestamp
             * @throws {Error} If the payload is not a reader or valid buffer
             * @throws {$protobuf.util.ProtocolError} If required fields are missing
             */
            public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): google.protobuf.Timestamp;

            /**
             * Verifies a Timestamp message.
             * @param message Plain object to verify
             * @returns `null` if valid, otherwise the reason why it is not
             */
            public static verify(message: { [k: string]: any }): (string|null);

            /**
             * Creates a Timestamp message from a plain object. Also converts values to their respective internal types.
             * @param object Plain object
             * @returns Timestamp
             */
            public static fromObject(object: { [k: string]: any }): google.protobuf.Timestamp;

            /**
             * Creates a plain object from a Timestamp message. Also converts values to other types if specified.
             * @param message Timestamp
             * @param [options] Conversion options
             * @returns Plain object
             */
            public static toObject(message: google.protobuf.Timestamp, options?: $protobuf.IConversionOptions): { [k: string]: any };

            /**
             * Converts this Timestamp to JSON.
             * @returns JSON object
             */
            public toJSON(): { [k: string]: any };

            /**
             * Gets the default type url for Timestamp
             * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
             * @returns The default type url
             */
            public static getTypeUrl(typeUrlPrefix?: string): string;
        }
    }
}
