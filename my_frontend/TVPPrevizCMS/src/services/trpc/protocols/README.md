# @tencent/trpc-rpc-client

---

tRPC-Node 是 [tRPC 中台项目](https://git.woa.com/trpc/trpc) 的 Node.js 语言实现，旨在提供一个使用简单、通用、高性能、模块化可拔插等特性的新一代 RPC 开发框架。

本项目是 tRPC-Node 的客户端实现，完整的 tRPC-Node 项目请访问：[trpc-node](https://git.woa.com/trpc-node/trpc-rpc)。

本文档主要介绍 tRPC-Node 客户端的使用：

## 内容列表

- [@tencent/trpc-rpc-client](#tencenttrpc-rpc-client)
  - [内容列表](#内容列表)
  - [安装](#安装)
  - [使用](#使用)
    - [初始化实例](#初始化实例)
    - [注册插件](#注册插件)
    - [创建 Proxy](#创建-proxy)
    - [接口调用](#接口调用)
    - [调用返回码](#调用返回码)
  - [使用许可](#使用许可)

## 安装

你可以根据你的具体需求，选择完整安装 `trpc-rpc`：

```sh
$ tnpm install @tencent/trpc-rpc
```

或者你需要客户端的能力，只安装 `trpc-rpc-client`：

```sh
$ tnpm install @tencent/trpc-rpc-client
```

## 使用

### 初始化实例

一、如果你的服务计划部署在 [123 运营平台](https://123.woa.com/v2/test#/overview)

此时，推荐使用导出的标准实例，该实例已经自动完成框架配置的初始化，标准插件的注册等流程，直接上手即用。

```ts
import { client } from '@tencent/trpc-rpc';
```

二、如果你的服务在本地运行，或者计划部署在其他平台

> 标准实例注册的 `tracing`、`metrics` 等插件，依赖 tRPC 框架配置来完成初始化以及镜像里边的上报 agent，故在 123 平台之外，建议自行实例化，如果需要使用该插件，应当做符合自己平台的插件封装。

此时，推荐使用导出的 `Client`，自行实例化、初始化（支持三种方式），并完成插件的注册。

```ts
import { Client, TrpcConfig } from '@tencent/trpc-rpc';

// 方法1（配置Object）：
const config: TrpcConfig = {
  global: ...
  client: ...
  plugins: ...
}
const client1 = new Client(config);

// 方法2（配置文件）：
const configPath = path.resolve(..., './test.yaml');
const client2 = new Client(configPath);

// 方法3（调用 `initialize` 方法）
const client3 = new Client();
cilent3.initialize(config || configPath);

// 方法4（环境变量）：
process.env.SUMERU_CONFIG = path.resolve(..., './test.yaml');
const client4 = new Client();
```

想了解框架配置的详细定义，请查看：[trpc_nodejs.yaml](https://git.woa.com/wod_csc_paas/123_process_script/blob/master/trpc_nodejs/trpc_nodejs.yaml)。

### 注册插件

如果你是自行实例化的 client，或者你希望用个性化的插件代替标准 client 实例中的插件，请关注此节。

每一个插件注册方法传入的，都是按照 [插件标准定义](https://git.woa.com/trpc-node/trpc-rpc/tree/master/packages/rpc-types) 实现的 Class，由框架来完成插件的实例化和根据框架配置初始化。

```ts
import { TrpcClient } from '@tencent/trpc-rpc-protocol';
import { PolarisNamingPlugin } from '@tencent/trpc-rpc-naming';
import { MyMetricsPlugin } from '@tencent/trpc-my-metrics';

client.use('protocol', TrpcClient);
client.use('naming', PolarisNamingPlugin);
client.use('metrics', MyMetricsPlugin);
```

更详细的插件使用说明、以及如何定制个性化插件，请参考主文档：[插件](https://git.woa.com/trpc-node/trpc-rpc#%E6%8F%92%E4%BB%B6) 一节。

### 创建 Proxy

客户端主要实现的能力：

- 通信器初始化代理实例
- 远端连接管理器，通过名字服务插件实现服务寻址、服务路由、负载均衡等
- 连接管理、收发数据、数据组包/解包、管理请求队列等

在实现 RPC 调用之前，需要先创建对应的客户端 RPC 代理：

```ts
import { client, GetProxyOptions } from '@tencent/trpc-rpc';
import { GreeterProxy } from './proto/helloworld-proxy';
const callee = 'trpc.mtt.NodeServer.Greeter';
const options: GetProxyOptions = {
  ...
};
const prx = new GreeterProxy(client, callee, options);
```

通过 GetProxyOptions 选项，可以支持一些个性化的定制，比如：

```ts
const options: GetProxyOptions = {
  naming: {
    // 1、自定义被调服务寻址名字。框架默认是用 `getProxy` 方法的 `callee` 参数寻址，业务可以另外指定：
    callee: '5059**:5609****',
    // 2、自定义负载均衡模式
    balance: 'HashRingLoadBalancer',
    // ...
  },
};
```

更多定制请参考 `GetProxyOptions` 类型定义。

### 接口调用

此时就可以像调用本地接口一样，调用远程接口了。

> proxy 下的接口方法 TS 支持度，正在努力完善中，请耐心等待

```ts
const options: InvokeProperty = {
  ...
};
const result = await prx.funcName(req, options);
```

`InvokeProperty` 是在 RPC 调用时传入的附加参数，主要支持以下能力：

- 传递上下文
- 定制本次调用的传输协议、序列化方法等
- 本次调用插件相关的个性化配置

下面列举一些比较常见的调用场景的选项配置：

一些特殊的调用场景：

> 1、如何传递上下文（MetaData）：

```ts
const options: InvokeProperty = {
  context: {
    caller: 'my-caller-name',
  },
};
```

> 2、如何在调用时选择使用 UDP 传输协议：

```ts
const options: InvokeProperty = {
  transport: 'udp',
};
```

> 3、如何单向调用：

```ts
const options: InvokeProperty = {
  callType: trpc.TrpcCallType.TRPC_ONEWAY_CALL,
};
```

> 4、如何自定义序列化协议发起 RPC 调用（目前支持：JSON）：

```ts
const callee = 'trpc.test.helloworld.Greeter';
const pkg = callee; 
const prx = client.getProxy(callee);
const result = await prx.rpcCall(pkg, 'SayHello', req);
```

> 5、如何一致性 hash 调用：

```ts
// 需要预先在 `GetProxyOptions.naming` 中指定负载均衡模式为：`HashRingLoadBalancer`
const options: InvokeProperty = {
  naming: {
    hashCode: '1234',
  }
});
```

> 6、如何指定当前调用被调服务的环境或 env：

```ts
// tRPC 默认在同环境调用，但可以通过参数路由到指定的环境或 env。
// 在 `GetProxyOptions.naming` 或 `InvokeProperty.naming` 中指定，其中优先级 InvokeProperty > GetProxyOptions
const options: InvokeProperty = {
  naming: {
    namespace: 'Development',
    env: '9eb824',
  }
});
```

> 7、如何按 set 路由：

```ts
// step1: 初始化 proxy 时，GetProxyOptions 指定 set 路由类型 MatchType （详见该枚举类型定义）
const options: GetProxyOptions = {
  naming: {
    set: MatchType.SourceSet
  }
});

// step2: 调用时，InvokeProperty 指定 set 名字
const options: InvokeProperty = {
  naming: {
    set: 'test.sz.0'
  }
});
```

> 8、如何让名字服务路由时，检查主调的出规则：

```ts
const options: InvokeProperty = {
  naming: {
    caller: 'trpc.test.helloworld.Greeter'
  }
});
```

更多详细信息请参考 `InvokeProperty` 类型定义。

### 调用返回码

框架调用成功时：

```ts
const { retCode, costTime, request, response, error } = await prx.func(...);
// 此时，retCode 为 0，error（如果有）为业务接口自定义异常（funcCode）
```

框架调用失败时：

```ts
const { retCode, costTime, request, error } = await prx.func(...);
// 此时，retCode 不为 0，error 为框架异常
```

## 使用许可

[MIT](LICENSE) © Tencent