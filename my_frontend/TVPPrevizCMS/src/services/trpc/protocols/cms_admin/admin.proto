syntax = "proto3"; 
//package code generated by <PERSON>  DO NOT EDIT.
package   trpc.video_media.cms_admin;

//code generated by <PERSON>  DO NOT EDIT.
option  java_package="com.tencent.trpcprotocol.video_media.cms_admin.admin";
option  java_multiple_files = false; 
//java_outer_classname的命名不要与message、service、enum的命名相同
option  java_outer_classname  = "AdminPB"; 
//code generated by Rick  DO NOT EDIT.
option  go_package ="git.woa.com/trpcprotocol/video_media/cms_admin_admin";

// 建议使用谷歌protobuf规范 遵循PB语法检查 
// 谷歌protobuf规范地址：https://developers.google.com/protocol-buffers/docs/style
// Proto格式检查（Tencent）包含：Google protobuf规范和数据校验检查 

// 不建议使用 google/protobuf/any.proto 
// any强依赖package type.googleapis.com/_packagename_._messagename_.   
// https://developers.google.com/protocol-buffers/docs/proto3#any 

// tRPC-Go数据校验模块（**移除注释使用**） 
// tsecstr仅允许中英文字母、数字、=、+、/、@、#、_、-传入。注意，字符集不包括空格、|等符号，如有需要，请自定义校验表达式。
// 详参见规则手册：https://iwiki.woa.com/pages/viewpage.action?pageId=241919333  
import "/root/projects/TVPPrevizCMS/protocols/admin-1_0_5/src/trpc/common/validate.proto";  


// 资产类型枚举
enum AssetType {
  ASSET_TYPE_UNSPECIFIED = 0;
  MAP = 1;
  CHARACTER = 2;
  SOUND = 3;
}

// 资产数据结构
message Asset {
  string id = 1;
  repeated string tags = 2;
  AssetType asset_type = 3;
  string data = 4;
  int32 state = 5; // 1: 有效, 2: 删除
  repeated string project_id_list = 6;
  int64 created_at = 7;
  string created_by = 8;
}

// ---------- 上传接口 ----------

// 获取上传 URL 和 job_id
message StartUploadRequest {
  string asset_id = 1[(validate.rules).string.tsecstr = true];
  string file_name = 2[(validate.rules).string = {min_len: 1}];
  string content_type = 3[(validate.rules).string = {min_len: 1}];
}

message StartUploadResponse {
  AssetUploadJob upload_job = 1;
}

message AssetUploadJob {
  string id = 1;
}

// 确认上传成功
message FinishUploadRequest {
  string job_id = 1[(validate.rules).string.tsecstr = true];
  string asset_id = 2[(validate.rules).string.tsecstr = true];
  bool success = 3;
  string fail_reason = 4[(validate.rules).string = {ignore_empty: true}];
}

message FinishUploadResponse {
  bool success = 1;
}


// ---------- CRUD 接口 ----------

// 创建资产
message CreateAssetRequest {
  repeated string tags = 1;
  AssetType asset_type = 2;
  string data = 3[(validate.rules).string.tsecstr = true];
  repeated string project_id_list = 4;
}

message CreateAssetResponse {
  string asset_id = 1;
}

// 获取资产详情
message GetAssetRequest {
  string asset_id = 1[(validate.rules).string.tsecstr = true];
}

message GetAssetResponse {
  Asset asset = 1;
}

// 更新资产
message UpdateAssetRequest {
  string asset_id = 1[(validate.rules).string.tsecstr = true];
  repeated string tags = 2;
  string data = 3[(validate.rules).string.tsecstr = true];
  repeated string project_id_list = 4;
}

message UpdateAssetResponse {
  bool success = 1;
}

// 删除资产（逻辑删除）
message DeleteAssetRequest {
  string asset_id = 1[(validate.rules).string.tsecstr = true];
}

message DeleteAssetResponse {
  bool success = 1;
}

// 查询资产列表（分页 + 筛选）
message ListAssetsRequest {
  int32 page = 1;
  int32 page_size = 2;
  AssetType asset_type = 3;
  string tag = 4[(validate.rules).string.tsecstr = true];
  string project_id = 5[(validate.rules).string.tsecstr = true];
}

message ListAssetsResponse {
  int32 total = 1;
  repeated Asset assets = 2;
}

// ---------- Service 定义 ----------

service AssetService {
  // 上传流程
  rpc StartUpload(StartUploadRequest) returns (StartUploadResponse);
  rpc FinishUpload(FinishUploadRequest) returns (FinishUploadResponse);

  // CRUD
  rpc CreateAsset(CreateAssetRequest) returns (CreateAssetResponse);
  rpc GetAsset(GetAssetRequest) returns (GetAssetResponse);
  rpc UpdateAsset(UpdateAssetRequest) returns (UpdateAssetResponse);
  rpc DeleteAsset(DeleteAssetRequest) returns (DeleteAssetResponse);
  rpc ListAssets(ListAssetsRequest) returns (ListAssetsResponse);
}