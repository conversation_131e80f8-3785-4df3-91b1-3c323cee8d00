import { instance } from '../utils/request';

// 场次类型定义
export interface Sequence {
  id: string;
  scene_id: string;
  name: string;
  description: string;
  time_of_day: string;
  dialogue_group: string;
  status: number;
  metadata: string;
  created_at: string;
  updated_at: string;
  project_id: string;
  thumbnail_path: string;
  project_role: string;
}

// 创建场次请求类型
export interface SequenceCreateData {
  scene_id: string;
  name: string;
  description?: string;
  time_of_day?: string;
  dialogue_group?: string;
  project_id: string;
  thumbnail_path?: string;
  project_role?: string;
  metadata?: any;
}

// 更新场次请求类型
export interface SequenceUpdateData {
  name?: string;
  description?: string;
  time_of_day?: string;
  dialogue_group?: string;
  status?: number;
  project_id?: string;
  thumbnail_path?: string;
  project_role?: string;
  metadata?: any;
}

// 场次服务类
export class SequenceService {
  // 获取场次列表
  static async list(params: {
    scene_id?: string;
    project_id?: string;
    offset?: number;
    limit?: number;
    status?: number;
  } = {}) {
    const { offset = 0, limit = 10, ...filters } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/sequences?${queryParams}`);
    return {
      data: response.data?.list || [],
      total: response.data?.pagination?.total || 0,
    };
  }

  // 获取场次详情
  static async getById(id: string) {
    const response = await instance.get(`/sequences/${id}`);
    return response.data;
  }

  // 创建场次
  static async create(data: SequenceCreateData) {
    const response = await instance.post('/sequences', data);
    return response.data;
  }

  // 更新场次
  static async update(id: string, data: SequenceUpdateData) {
    const response = await instance.put(`/sequences/${id}`, data);
    return response.data;
  }

  // 删除场次
  static async delete(id: string) {
    const response = await instance.delete(`/sequences/${id}`);
    return response.data;
  }

  // 获取场景下的场次列表
  static async getByScene(sceneId: string, params: {
    offset?: number;
    limit?: number;
    status?: number;
  } = {}) {
    const { offset = 0, limit = 10, status } = params;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...(status !== undefined && { status: status.toString() }),
    });

    const response = await instance.get(`/scenes/${sceneId}/sequences?${queryParams}`);
    return response.data;
  }

  // 批量删除场次
  static async batchDelete(ids: string[]) {
    const response = await instance.post('/sequences/batch-delete', { ids });
    return response.data;
  }
}

export default SequenceService;
