import { instance } from '../utils/request';

export interface Project {
  id: string;
  name: string;
  description?: string;
  creator_id: string;
  status: number;
  created_at: string;
  updated_at: string;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  storyboard_count?: number;
  assets?: any[]; // 关联的角色资产
}

export interface ProjectCreateData {
  name: string;
  description?: string;
  creator_id: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectUpdateData {
  name?: string;
  description?: string;
  status?: number;
  thumbnail_path?: string;
  episode?: number;
  dialogue_group?: string;
  assetIds?: string[]; // 角色资产ID数组
}

export interface ProjectListParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface ProjectListResponse {
  list: Project[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * 项目API服务
 */
export class ProjectService {
  /**
   * 获取项目列表
   */
  static async getList(params: ProjectListParams = {}): Promise<ProjectListResponse> {
    const { page = 1, limit = 10, ...filters } = params;
    const offset = (page - 1) * limit;
    const queryParams = new URLSearchParams({
      offset: offset.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined)
      ),
    });

    const response = await instance.get(`/projects?${queryParams}`);
    return {
      list: response.data?.list || [],
      pagination: response.data?.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    };
  }

  /**
   * 获取项目详情
   */
  static async getById(id: string): Promise<Project> {
    const response = await instance.get(`/projects/${id}`);
    return response.data;
  }

  /**
   * 创建项目
   */
  static async create(data: ProjectCreateData): Promise<Project> {
    const response = await instance.post('/projects', data);
    return response.data;
  }

  /**
   * 更新项目
   */
  static async update(id: string, data: ProjectUpdateData): Promise<Project> {
    const response = await instance.put(`/projects/${id}`, data);
    return response.data;
  }

  /**
   * 删除项目
   */
  static async delete(id: string): Promise<void> {
    await instance.delete(`/projects/${id}`);
  }

  /**
   * 批量删除项目
   */
  static async batchDelete(ids: string[]): Promise<any> {
    const response = await instance.post('/projects/batch-delete', { ids });
    return response.data;
  }

  /**
   * 获取项目下的故事板列表
   */
  static async getStoryboards(projectId: string, params: { page?: number; limit?: number } = {}): Promise<any> {
    const response = await instance.get(`/projects/${projectId}/storyboards`, { params });
    return response.data;
  }
}

export default ProjectService;
