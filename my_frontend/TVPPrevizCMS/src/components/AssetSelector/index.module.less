/* AssetSelector 组件样式 */

.assetSelector {
  .searchBar {
    margin-bottom: 16px;
  }

  .assetGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    padding: 8px;
  }

  .assetCard {
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border: 2px solid #0052d9;
    }
  }

  .selectedIcon {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #0052d9;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }

  .thumbnail {
    height: 120px;
    overflow: hidden;
    border-radius: 4px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
  }

  .assetInfo {
    .assetName {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .assetMeta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .fileSize {
      font-size: 12px;
      color: #666;
    }
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }

  .pagination {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
}
