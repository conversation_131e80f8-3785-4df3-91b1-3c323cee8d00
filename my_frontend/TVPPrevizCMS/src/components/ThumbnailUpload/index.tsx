import React, { useState, useEffect } from 'react';
import { Upload, Image, Button, MessagePlugin } from 'tdesign-react';
import { DeleteIcon, AddIcon } from 'tdesign-icons-react';
import { buildThumbnailUrl, buildUploadUrl } from '../../utils/url';

interface ThumbnailUploadProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  width?: number;
  height?: number;
}

const ThumbnailUpload: React.FC<ThumbnailUploadProps> = ({
  value,
  onChange,
  disabled = false,
  width = 200,
  height = 150,
}) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const [previewUrl, setPreviewUrl] = useState<string>('');

  // 默认缩略图
  const defaultThumbnail = '/images/default-thumbnail.svg';

  // 当value变化时更新预览
  useEffect(() => {
    if (value) {
      // 使用统一的URL构建函数
      const imageUrl = buildThumbnailUrl(value);
      setPreviewUrl(imageUrl);
      setFileList([{
        name: value.split('/').pop() || 'thumbnail',
        url: imageUrl
      }]);
    } else {
      setPreviewUrl(defaultThumbnail);
      setFileList([]);
    }
  }, [value]);

  // 处理文件上传成功
  const handleSuccess = (res: any) => {
    console.log('handleSuccess - Raw res:', res);
    console.log('handleSuccess - res type:', typeof res);
    console.log('handleSuccess - res keys:', Object.keys(res || {}));

    // TDesign Upload 可能会包装响应，需要提取实际的响应数据
    const actualResponse = res?.response || res;
    console.log('handleSuccess - actualResponse:', actualResponse);

    if (actualResponse && actualResponse.code === 0) {
      const filePath = actualResponse.data.path;
      const imageUrl = buildThumbnailUrl(filePath);

      console.log('handleSuccess - Success! filePath:', filePath, 'imageUrl:', imageUrl);

      // 立即更新预览图片
      setPreviewUrl(imageUrl);
      setFileList([{
        name: actualResponse.data.filename || 'thumbnail',
        url: imageUrl
      }]);

      // 通知父组件值已更改
      onChange?.(filePath);
      MessagePlugin.success('图片上传成功');
    } else {
      console.log('handleSuccess - Failed! actualResponse:', actualResponse);
      MessagePlugin.error(actualResponse?.message || '上传失败');
    }
  };

  // 处理文件上传失败
  const handleFail = (res: any) => {
    console.error('handleFail - Raw res:', res);
    console.error('handleFail - res type:', typeof res);
    console.error('handleFail - res keys:', Object.keys(res || {}));

    const actualResponse = res?.response || res;
    console.error('handleFail - actualResponse:', actualResponse);
    MessagePlugin.error(actualResponse?.message || '图片上传失败');
  };

  // 处理文件移除
  const handleRemove = () => {
    setFileList([]);
    setPreviewUrl(defaultThumbnail);
    onChange?.('');
    MessagePlugin.success('图片已移除');
  };

  // 文件上传前的验证
  const beforeUpload = (file: any) => {
    console.log('Before upload:', file);
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    const fileType = file.type || file.raw?.type;
    if (!allowedTypes.includes(fileType)) {
      MessagePlugin.error('只支持 JPEG、PNG、GIF、WebP、SVG 格式的图片');
      return false;
    }

    // 检查文件大小 (5MB)
    const maxSize = 5 * 1024 * 1024;
    const fileSize = file.size || file.raw?.size;
    if (fileSize > maxSize) {
      MessagePlugin.error('图片大小不能超过 5MB');
      return false;
    }

    return true;
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      {/* 图片预览区域 */}
      <div 
        style={{ 
          width: `${width}px`, 
          height: `${height}px`,
          border: '1px dashed #d9d9d9',
          borderRadius: '6px',
          overflow: 'hidden',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#fafafa'
        }}
      >
        <Image
          src={previewUrl || defaultThumbnail}
          style={{ width: `${width}px`, height: `${height}px` }}
          fit="cover"
          error={
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999'
            }}>
              加载失败
            </div>
          }
          loading={
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#999'
            }}>
              加载中...
            </div>
          }
        />
      </div>

      {/* 上传控制区域 */}
      <div style={{ display: 'flex', gap: '8px' }}>
        <Upload
          action={buildUploadUrl('/upload/thumbnail')}
          name="thumbnail"
          onSuccess={handleSuccess}
          onFail={handleFail}
          beforeUpload={beforeUpload}
          disabled={disabled}
          accept="image/*"
          multiple={false}
          onProgress={(options) => {
            console.log('TDesign onProgress:', options);
          }}
          onRemove={(options) => {
            console.log('TDesign onRemove:', options);
          }}
          requestMethod={(files) => {
            console.log('TDesign requestMethod - files:', files);
            return new Promise((resolve) => {
              const fileArray = Array.isArray(files) ? files : [files];
              const file = fileArray[0];
              if (!file || !file.raw) {
                resolve({
                  status: 'fail',
                  response: { message: '请选择文件' }
                });
                return;
              }
              const formData = new FormData();
              formData.append('thumbnail', file.raw);

              const xhr = new XMLHttpRequest();
              xhr.open('POST', buildUploadUrl('/upload/thumbnail'));

              xhr.onload = () => {
                console.log('XHR onload - status:', xhr.status);
                console.log('XHR onload - responseText:', xhr.responseText);

                if (xhr.status === 200) {
                  try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('XHR onload - parsed response:', response);

                    // TDesign Upload 期望的响应格式
                    if (response && response.code === 0) {
                      resolve({
                        status: 'success',
                        response: response
                      });
                    } else {
                      resolve({
                        status: 'fail',
                        response: response
                      });
                    }
                  } catch (e) {
                    console.error('XHR onload - JSON parse error:', e);
                    resolve({
                      status: 'fail',
                      response: { message: 'JSON解析错误' }
                    });
                  }
                } else {
                  console.error('XHR onload - HTTP error:', xhr.status);
                  resolve({
                    status: 'fail',
                    response: { message: `HTTP错误: ${xhr.status}` }
                  });
                }
              };

              xhr.onerror = (error) => {
                console.error('XHR onerror:', error);
                resolve({
                  status: 'fail',
                  response: { message: '网络错误' }
                });
              };

              xhr.send(formData);
            });
          }}
        >
          <Button 
            icon={<AddIcon />} 
            variant="outline" 
            size="small"
            disabled={disabled}
          >
            {fileList.length > 0 ? '更换图片' : '上传图片'}
          </Button>
        </Upload>

        {value && (
          <Button
            icon={<DeleteIcon />}
            variant="outline"
            theme="danger"
            size="small"
            disabled={disabled}
            onClick={handleRemove}
          >
            删除图片
          </Button>
        )}
      </div>

      {/* 提示信息 */}
      <div style={{ fontSize: '12px', color: '#999', lineHeight: '1.4' }}>
        支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB
      </div>
    </div>
  );
};

export default ThumbnailUpload;
