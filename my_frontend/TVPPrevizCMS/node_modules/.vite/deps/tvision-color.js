import {
  __commonJS,
  __toESM
} from "./chunk-5WWUZCGV.js";

// node_modules/core-js-pure/internals/global-this.js
var require_global_this = __commonJS({
  "node_modules/core-js-pure/internals/global-this.js"(exports, module) {
    "use strict";
    var check = function(it) {
      return it && it.Math === Math && it;
    };
    module.exports = // eslint-disable-next-line es/no-global-this -- safe
    check(typeof globalThis == "object" && globalThis) || check(typeof window == "object" && window) || // eslint-disable-next-line no-restricted-globals -- safe
    check(typeof self == "object" && self) || check(typeof global == "object" && global) || check(typeof exports == "object" && exports) || // eslint-disable-next-line no-new-func -- fallback
    function() {
      return this;
    }() || Function("return this")();
  }
});

// node_modules/core-js-pure/internals/fails.js
var require_fails = __commonJS({
  "node_modules/core-js-pure/internals/fails.js"(exports, module) {
    "use strict";
    module.exports = function(exec) {
      try {
        return !!exec();
      } catch (error) {
        return true;
      }
    };
  }
});

// node_modules/core-js-pure/internals/function-bind-native.js
var require_function_bind_native = __commonJS({
  "node_modules/core-js-pure/internals/function-bind-native.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = !fails(function() {
      var test = (function() {
      }).bind();
      return typeof test != "function" || test.hasOwnProperty("prototype");
    });
  }
});

// node_modules/core-js-pure/internals/function-apply.js
var require_function_apply = __commonJS({
  "node_modules/core-js-pure/internals/function-apply.js"(exports, module) {
    "use strict";
    var NATIVE_BIND = require_function_bind_native();
    var FunctionPrototype = Function.prototype;
    var apply = FunctionPrototype.apply;
    var call = FunctionPrototype.call;
    module.exports = typeof Reflect == "object" && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function() {
      return call.apply(apply, arguments);
    });
  }
});

// node_modules/core-js-pure/internals/function-uncurry-this.js
var require_function_uncurry_this = __commonJS({
  "node_modules/core-js-pure/internals/function-uncurry-this.js"(exports, module) {
    "use strict";
    var NATIVE_BIND = require_function_bind_native();
    var FunctionPrototype = Function.prototype;
    var call = FunctionPrototype.call;
    var uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);
    module.exports = NATIVE_BIND ? uncurryThisWithBind : function(fn) {
      return function() {
        return call.apply(fn, arguments);
      };
    };
  }
});

// node_modules/core-js-pure/internals/classof-raw.js
var require_classof_raw = __commonJS({
  "node_modules/core-js-pure/internals/classof-raw.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var toString = uncurryThis({}.toString);
    var stringSlice = uncurryThis("".slice);
    module.exports = function(it) {
      return stringSlice(toString(it), 8, -1);
    };
  }
});

// node_modules/core-js-pure/internals/function-uncurry-this-clause.js
var require_function_uncurry_this_clause = __commonJS({
  "node_modules/core-js-pure/internals/function-uncurry-this-clause.js"(exports, module) {
    "use strict";
    var classofRaw = require_classof_raw();
    var uncurryThis = require_function_uncurry_this();
    module.exports = function(fn) {
      if (classofRaw(fn) === "Function")
        return uncurryThis(fn);
    };
  }
});

// node_modules/core-js-pure/internals/is-callable.js
var require_is_callable = __commonJS({
  "node_modules/core-js-pure/internals/is-callable.js"(exports, module) {
    "use strict";
    var documentAll = typeof document == "object" && document.all;
    module.exports = typeof documentAll == "undefined" && documentAll !== void 0 ? function(argument) {
      return typeof argument == "function" || argument === documentAll;
    } : function(argument) {
      return typeof argument == "function";
    };
  }
});

// node_modules/core-js-pure/internals/descriptors.js
var require_descriptors = __commonJS({
  "node_modules/core-js-pure/internals/descriptors.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = !fails(function() {
      return Object.defineProperty({}, 1, { get: function() {
        return 7;
      } })[1] !== 7;
    });
  }
});

// node_modules/core-js-pure/internals/function-call.js
var require_function_call = __commonJS({
  "node_modules/core-js-pure/internals/function-call.js"(exports, module) {
    "use strict";
    var NATIVE_BIND = require_function_bind_native();
    var call = Function.prototype.call;
    module.exports = NATIVE_BIND ? call.bind(call) : function() {
      return call.apply(call, arguments);
    };
  }
});

// node_modules/core-js-pure/internals/object-property-is-enumerable.js
var require_object_property_is_enumerable = __commonJS({
  "node_modules/core-js-pure/internals/object-property-is-enumerable.js"(exports) {
    "use strict";
    var $propertyIsEnumerable = {}.propertyIsEnumerable;
    var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
    var NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);
    exports.f = NASHORN_BUG ? function propertyIsEnumerable(V2) {
      var descriptor = getOwnPropertyDescriptor(this, V2);
      return !!descriptor && descriptor.enumerable;
    } : $propertyIsEnumerable;
  }
});

// node_modules/core-js-pure/internals/create-property-descriptor.js
var require_create_property_descriptor = __commonJS({
  "node_modules/core-js-pure/internals/create-property-descriptor.js"(exports, module) {
    "use strict";
    module.exports = function(bitmap, value) {
      return {
        enumerable: !(bitmap & 1),
        configurable: !(bitmap & 2),
        writable: !(bitmap & 4),
        value
      };
    };
  }
});

// node_modules/core-js-pure/internals/indexed-object.js
var require_indexed_object = __commonJS({
  "node_modules/core-js-pure/internals/indexed-object.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var fails = require_fails();
    var classof = require_classof_raw();
    var $Object = Object;
    var split = uncurryThis("".split);
    module.exports = fails(function() {
      return !$Object("z").propertyIsEnumerable(0);
    }) ? function(it) {
      return classof(it) === "String" ? split(it, "") : $Object(it);
    } : $Object;
  }
});

// node_modules/core-js-pure/internals/is-null-or-undefined.js
var require_is_null_or_undefined = __commonJS({
  "node_modules/core-js-pure/internals/is-null-or-undefined.js"(exports, module) {
    "use strict";
    module.exports = function(it) {
      return it === null || it === void 0;
    };
  }
});

// node_modules/core-js-pure/internals/require-object-coercible.js
var require_require_object_coercible = __commonJS({
  "node_modules/core-js-pure/internals/require-object-coercible.js"(exports, module) {
    "use strict";
    var isNullOrUndefined = require_is_null_or_undefined();
    var $TypeError = TypeError;
    module.exports = function(it) {
      if (isNullOrUndefined(it))
        throw new $TypeError("Can't call method on " + it);
      return it;
    };
  }
});

// node_modules/core-js-pure/internals/to-indexed-object.js
var require_to_indexed_object = __commonJS({
  "node_modules/core-js-pure/internals/to-indexed-object.js"(exports, module) {
    "use strict";
    var IndexedObject = require_indexed_object();
    var requireObjectCoercible = require_require_object_coercible();
    module.exports = function(it) {
      return IndexedObject(requireObjectCoercible(it));
    };
  }
});

// node_modules/core-js-pure/internals/is-object.js
var require_is_object = __commonJS({
  "node_modules/core-js-pure/internals/is-object.js"(exports, module) {
    "use strict";
    var isCallable = require_is_callable();
    module.exports = function(it) {
      return typeof it == "object" ? it !== null : isCallable(it);
    };
  }
});

// node_modules/core-js-pure/internals/path.js
var require_path = __commonJS({
  "node_modules/core-js-pure/internals/path.js"(exports, module) {
    "use strict";
    module.exports = {};
  }
});

// node_modules/core-js-pure/internals/get-built-in.js
var require_get_built_in = __commonJS({
  "node_modules/core-js-pure/internals/get-built-in.js"(exports, module) {
    "use strict";
    var path = require_path();
    var globalThis2 = require_global_this();
    var isCallable = require_is_callable();
    var aFunction = function(variable) {
      return isCallable(variable) ? variable : void 0;
    };
    module.exports = function(namespace, method) {
      return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(globalThis2[namespace]) : path[namespace] && path[namespace][method] || globalThis2[namespace] && globalThis2[namespace][method];
    };
  }
});

// node_modules/core-js-pure/internals/object-is-prototype-of.js
var require_object_is_prototype_of = __commonJS({
  "node_modules/core-js-pure/internals/object-is-prototype-of.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    module.exports = uncurryThis({}.isPrototypeOf);
  }
});

// node_modules/core-js-pure/internals/environment-user-agent.js
var require_environment_user_agent = __commonJS({
  "node_modules/core-js-pure/internals/environment-user-agent.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var navigator = globalThis2.navigator;
    var userAgent = navigator && navigator.userAgent;
    module.exports = userAgent ? String(userAgent) : "";
  }
});

// node_modules/core-js-pure/internals/environment-v8-version.js
var require_environment_v8_version = __commonJS({
  "node_modules/core-js-pure/internals/environment-v8-version.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var userAgent = require_environment_user_agent();
    var process = globalThis2.process;
    var Deno = globalThis2.Deno;
    var versions = process && process.versions || Deno && Deno.version;
    var v8 = versions && versions.v8;
    var match;
    var version;
    if (v8) {
      match = v8.split(".");
      version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);
    }
    if (!version && userAgent) {
      match = userAgent.match(/Edge\/(\d+)/);
      if (!match || match[1] >= 74) {
        match = userAgent.match(/Chrome\/(\d+)/);
        if (match)
          version = +match[1];
      }
    }
    module.exports = version;
  }
});

// node_modules/core-js-pure/internals/symbol-constructor-detection.js
var require_symbol_constructor_detection = __commonJS({
  "node_modules/core-js-pure/internals/symbol-constructor-detection.js"(exports, module) {
    "use strict";
    var V8_VERSION = require_environment_v8_version();
    var fails = require_fails();
    var globalThis2 = require_global_this();
    var $String = globalThis2.String;
    module.exports = !!Object.getOwnPropertySymbols && !fails(function() {
      var symbol = Symbol("symbol detection");
      return !$String(symbol) || !(Object(symbol) instanceof Symbol) || // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances
      !Symbol.sham && V8_VERSION && V8_VERSION < 41;
    });
  }
});

// node_modules/core-js-pure/internals/use-symbol-as-uid.js
var require_use_symbol_as_uid = __commonJS({
  "node_modules/core-js-pure/internals/use-symbol-as-uid.js"(exports, module) {
    "use strict";
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    module.exports = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == "symbol";
  }
});

// node_modules/core-js-pure/internals/is-symbol.js
var require_is_symbol = __commonJS({
  "node_modules/core-js-pure/internals/is-symbol.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var isCallable = require_is_callable();
    var isPrototypeOf = require_object_is_prototype_of();
    var USE_SYMBOL_AS_UID = require_use_symbol_as_uid();
    var $Object = Object;
    module.exports = USE_SYMBOL_AS_UID ? function(it) {
      return typeof it == "symbol";
    } : function(it) {
      var $Symbol = getBuiltIn("Symbol");
      return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));
    };
  }
});

// node_modules/core-js-pure/internals/try-to-string.js
var require_try_to_string = __commonJS({
  "node_modules/core-js-pure/internals/try-to-string.js"(exports, module) {
    "use strict";
    var $String = String;
    module.exports = function(argument) {
      try {
        return $String(argument);
      } catch (error) {
        return "Object";
      }
    };
  }
});

// node_modules/core-js-pure/internals/a-callable.js
var require_a_callable = __commonJS({
  "node_modules/core-js-pure/internals/a-callable.js"(exports, module) {
    "use strict";
    var isCallable = require_is_callable();
    var tryToString = require_try_to_string();
    var $TypeError = TypeError;
    module.exports = function(argument) {
      if (isCallable(argument))
        return argument;
      throw new $TypeError(tryToString(argument) + " is not a function");
    };
  }
});

// node_modules/core-js-pure/internals/get-method.js
var require_get_method = __commonJS({
  "node_modules/core-js-pure/internals/get-method.js"(exports, module) {
    "use strict";
    var aCallable = require_a_callable();
    var isNullOrUndefined = require_is_null_or_undefined();
    module.exports = function(V2, P2) {
      var func = V2[P2];
      return isNullOrUndefined(func) ? void 0 : aCallable(func);
    };
  }
});

// node_modules/core-js-pure/internals/ordinary-to-primitive.js
var require_ordinary_to_primitive = __commonJS({
  "node_modules/core-js-pure/internals/ordinary-to-primitive.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    var isCallable = require_is_callable();
    var isObject = require_is_object();
    var $TypeError = TypeError;
    module.exports = function(input, pref) {
      var fn, val;
      if (pref === "string" && isCallable(fn = input.toString) && !isObject(val = call(fn, input)))
        return val;
      if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input)))
        return val;
      if (pref !== "string" && isCallable(fn = input.toString) && !isObject(val = call(fn, input)))
        return val;
      throw new $TypeError("Can't convert object to primitive value");
    };
  }
});

// node_modules/core-js-pure/internals/is-pure.js
var require_is_pure = __commonJS({
  "node_modules/core-js-pure/internals/is-pure.js"(exports, module) {
    "use strict";
    module.exports = true;
  }
});

// node_modules/core-js-pure/internals/define-global-property.js
var require_define_global_property = __commonJS({
  "node_modules/core-js-pure/internals/define-global-property.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var defineProperty = Object.defineProperty;
    module.exports = function(key, value) {
      try {
        defineProperty(globalThis2, key, { value, configurable: true, writable: true });
      } catch (error) {
        globalThis2[key] = value;
      }
      return value;
    };
  }
});

// node_modules/core-js-pure/internals/shared-store.js
var require_shared_store = __commonJS({
  "node_modules/core-js-pure/internals/shared-store.js"(exports, module) {
    "use strict";
    var IS_PURE = require_is_pure();
    var globalThis2 = require_global_this();
    var defineGlobalProperty = require_define_global_property();
    var SHARED = "__core-js_shared__";
    var store = module.exports = globalThis2[SHARED] || defineGlobalProperty(SHARED, {});
    (store.versions || (store.versions = [])).push({
      version: "3.44.0",
      mode: IS_PURE ? "pure" : "global",
      copyright: "© 2014-2025 Denis Pushkarev (zloirock.ru)",
      license: "https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",
      source: "https://github.com/zloirock/core-js"
    });
  }
});

// node_modules/core-js-pure/internals/shared.js
var require_shared = __commonJS({
  "node_modules/core-js-pure/internals/shared.js"(exports, module) {
    "use strict";
    var store = require_shared_store();
    module.exports = function(key, value) {
      return store[key] || (store[key] = value || {});
    };
  }
});

// node_modules/core-js-pure/internals/to-object.js
var require_to_object = __commonJS({
  "node_modules/core-js-pure/internals/to-object.js"(exports, module) {
    "use strict";
    var requireObjectCoercible = require_require_object_coercible();
    var $Object = Object;
    module.exports = function(argument) {
      return $Object(requireObjectCoercible(argument));
    };
  }
});

// node_modules/core-js-pure/internals/has-own-property.js
var require_has_own_property = __commonJS({
  "node_modules/core-js-pure/internals/has-own-property.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var toObject = require_to_object();
    var hasOwnProperty = uncurryThis({}.hasOwnProperty);
    module.exports = Object.hasOwn || function hasOwn(it, key) {
      return hasOwnProperty(toObject(it), key);
    };
  }
});

// node_modules/core-js-pure/internals/uid.js
var require_uid = __commonJS({
  "node_modules/core-js-pure/internals/uid.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var id = 0;
    var postfix = Math.random();
    var toString = uncurryThis(1.1.toString);
    module.exports = function(key) {
      return "Symbol(" + (key === void 0 ? "" : key) + ")_" + toString(++id + postfix, 36);
    };
  }
});

// node_modules/core-js-pure/internals/well-known-symbol.js
var require_well_known_symbol = __commonJS({
  "node_modules/core-js-pure/internals/well-known-symbol.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var shared = require_shared();
    var hasOwn = require_has_own_property();
    var uid = require_uid();
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    var USE_SYMBOL_AS_UID = require_use_symbol_as_uid();
    var Symbol2 = globalThis2.Symbol;
    var WellKnownSymbolsStore = shared("wks");
    var createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol2["for"] || Symbol2 : Symbol2 && Symbol2.withoutSetter || uid;
    module.exports = function(name) {
      if (!hasOwn(WellKnownSymbolsStore, name)) {
        WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol2, name) ? Symbol2[name] : createWellKnownSymbol("Symbol." + name);
      }
      return WellKnownSymbolsStore[name];
    };
  }
});

// node_modules/core-js-pure/internals/to-primitive.js
var require_to_primitive = __commonJS({
  "node_modules/core-js-pure/internals/to-primitive.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    var isObject = require_is_object();
    var isSymbol = require_is_symbol();
    var getMethod = require_get_method();
    var ordinaryToPrimitive = require_ordinary_to_primitive();
    var wellKnownSymbol = require_well_known_symbol();
    var $TypeError = TypeError;
    var TO_PRIMITIVE = wellKnownSymbol("toPrimitive");
    module.exports = function(input, pref) {
      if (!isObject(input) || isSymbol(input))
        return input;
      var exoticToPrim = getMethod(input, TO_PRIMITIVE);
      var result;
      if (exoticToPrim) {
        if (pref === void 0)
          pref = "default";
        result = call(exoticToPrim, input, pref);
        if (!isObject(result) || isSymbol(result))
          return result;
        throw new $TypeError("Can't convert object to primitive value");
      }
      if (pref === void 0)
        pref = "number";
      return ordinaryToPrimitive(input, pref);
    };
  }
});

// node_modules/core-js-pure/internals/to-property-key.js
var require_to_property_key = __commonJS({
  "node_modules/core-js-pure/internals/to-property-key.js"(exports, module) {
    "use strict";
    var toPrimitive = require_to_primitive();
    var isSymbol = require_is_symbol();
    module.exports = function(argument) {
      var key = toPrimitive(argument, "string");
      return isSymbol(key) ? key : key + "";
    };
  }
});

// node_modules/core-js-pure/internals/document-create-element.js
var require_document_create_element = __commonJS({
  "node_modules/core-js-pure/internals/document-create-element.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var isObject = require_is_object();
    var document2 = globalThis2.document;
    var EXISTS = isObject(document2) && isObject(document2.createElement);
    module.exports = function(it) {
      return EXISTS ? document2.createElement(it) : {};
    };
  }
});

// node_modules/core-js-pure/internals/ie8-dom-define.js
var require_ie8_dom_define = __commonJS({
  "node_modules/core-js-pure/internals/ie8-dom-define.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var fails = require_fails();
    var createElement = require_document_create_element();
    module.exports = !DESCRIPTORS && !fails(function() {
      return Object.defineProperty(createElement("div"), "a", {
        get: function() {
          return 7;
        }
      }).a !== 7;
    });
  }
});

// node_modules/core-js-pure/internals/object-get-own-property-descriptor.js
var require_object_get_own_property_descriptor = __commonJS({
  "node_modules/core-js-pure/internals/object-get-own-property-descriptor.js"(exports) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var call = require_function_call();
    var propertyIsEnumerableModule = require_object_property_is_enumerable();
    var createPropertyDescriptor = require_create_property_descriptor();
    var toIndexedObject = require_to_indexed_object();
    var toPropertyKey = require_to_property_key();
    var hasOwn = require_has_own_property();
    var IE8_DOM_DEFINE = require_ie8_dom_define();
    var $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
    exports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O2, P2) {
      O2 = toIndexedObject(O2);
      P2 = toPropertyKey(P2);
      if (IE8_DOM_DEFINE)
        try {
          return $getOwnPropertyDescriptor(O2, P2);
        } catch (error) {
        }
      if (hasOwn(O2, P2))
        return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O2, P2), O2[P2]);
    };
  }
});

// node_modules/core-js-pure/internals/is-forced.js
var require_is_forced = __commonJS({
  "node_modules/core-js-pure/internals/is-forced.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    var isCallable = require_is_callable();
    var replacement = /#|\.prototype\./;
    var isForced = function(feature, detection) {
      var value = data[normalize(feature)];
      return value === POLYFILL ? true : value === NATIVE ? false : isCallable(detection) ? fails(detection) : !!detection;
    };
    var normalize = isForced.normalize = function(string) {
      return String(string).replace(replacement, ".").toLowerCase();
    };
    var data = isForced.data = {};
    var NATIVE = isForced.NATIVE = "N";
    var POLYFILL = isForced.POLYFILL = "P";
    module.exports = isForced;
  }
});

// node_modules/core-js-pure/internals/function-bind-context.js
var require_function_bind_context = __commonJS({
  "node_modules/core-js-pure/internals/function-bind-context.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this_clause();
    var aCallable = require_a_callable();
    var NATIVE_BIND = require_function_bind_native();
    var bind = uncurryThis(uncurryThis.bind);
    module.exports = function(fn, that) {
      aCallable(fn);
      return that === void 0 ? fn : NATIVE_BIND ? bind(fn, that) : function() {
        return fn.apply(that, arguments);
      };
    };
  }
});

// node_modules/core-js-pure/internals/v8-prototype-define-bug.js
var require_v8_prototype_define_bug = __commonJS({
  "node_modules/core-js-pure/internals/v8-prototype-define-bug.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var fails = require_fails();
    module.exports = DESCRIPTORS && fails(function() {
      return Object.defineProperty(function() {
      }, "prototype", {
        value: 42,
        writable: false
      }).prototype !== 42;
    });
  }
});

// node_modules/core-js-pure/internals/an-object.js
var require_an_object = __commonJS({
  "node_modules/core-js-pure/internals/an-object.js"(exports, module) {
    "use strict";
    var isObject = require_is_object();
    var $String = String;
    var $TypeError = TypeError;
    module.exports = function(argument) {
      if (isObject(argument))
        return argument;
      throw new $TypeError($String(argument) + " is not an object");
    };
  }
});

// node_modules/core-js-pure/internals/object-define-property.js
var require_object_define_property = __commonJS({
  "node_modules/core-js-pure/internals/object-define-property.js"(exports) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var IE8_DOM_DEFINE = require_ie8_dom_define();
    var V8_PROTOTYPE_DEFINE_BUG = require_v8_prototype_define_bug();
    var anObject = require_an_object();
    var toPropertyKey = require_to_property_key();
    var $TypeError = TypeError;
    var $defineProperty = Object.defineProperty;
    var $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
    var ENUMERABLE = "enumerable";
    var CONFIGURABLE = "configurable";
    var WRITABLE = "writable";
    exports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O2, P2, Attributes) {
      anObject(O2);
      P2 = toPropertyKey(P2);
      anObject(Attributes);
      if (typeof O2 === "function" && P2 === "prototype" && "value" in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {
        var current = $getOwnPropertyDescriptor(O2, P2);
        if (current && current[WRITABLE]) {
          O2[P2] = Attributes.value;
          Attributes = {
            configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],
            enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],
            writable: false
          };
        }
      }
      return $defineProperty(O2, P2, Attributes);
    } : $defineProperty : function defineProperty(O2, P2, Attributes) {
      anObject(O2);
      P2 = toPropertyKey(P2);
      anObject(Attributes);
      if (IE8_DOM_DEFINE)
        try {
          return $defineProperty(O2, P2, Attributes);
        } catch (error) {
        }
      if ("get" in Attributes || "set" in Attributes)
        throw new $TypeError("Accessors not supported");
      if ("value" in Attributes)
        O2[P2] = Attributes.value;
      return O2;
    };
  }
});

// node_modules/core-js-pure/internals/create-non-enumerable-property.js
var require_create_non_enumerable_property = __commonJS({
  "node_modules/core-js-pure/internals/create-non-enumerable-property.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var definePropertyModule = require_object_define_property();
    var createPropertyDescriptor = require_create_property_descriptor();
    module.exports = DESCRIPTORS ? function(object, key, value) {
      return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));
    } : function(object, key, value) {
      object[key] = value;
      return object;
    };
  }
});

// node_modules/core-js-pure/internals/export.js
var require_export = __commonJS({
  "node_modules/core-js-pure/internals/export.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var apply = require_function_apply();
    var uncurryThis = require_function_uncurry_this_clause();
    var isCallable = require_is_callable();
    var getOwnPropertyDescriptor = require_object_get_own_property_descriptor().f;
    var isForced = require_is_forced();
    var path = require_path();
    var bind = require_function_bind_context();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var hasOwn = require_has_own_property();
    require_shared_store();
    var wrapConstructor = function(NativeConstructor) {
      var Wrapper = function(a2, b2, c2) {
        if (this instanceof Wrapper) {
          switch (arguments.length) {
            case 0:
              return new NativeConstructor();
            case 1:
              return new NativeConstructor(a2);
            case 2:
              return new NativeConstructor(a2, b2);
          }
          return new NativeConstructor(a2, b2, c2);
        }
        return apply(NativeConstructor, this, arguments);
      };
      Wrapper.prototype = NativeConstructor.prototype;
      return Wrapper;
    };
    module.exports = function(options, source) {
      var TARGET = options.target;
      var GLOBAL = options.global;
      var STATIC = options.stat;
      var PROTO = options.proto;
      var nativeSource = GLOBAL ? globalThis2 : STATIC ? globalThis2[TARGET] : globalThis2[TARGET] && globalThis2[TARGET].prototype;
      var target = GLOBAL ? path : path[TARGET] || createNonEnumerableProperty(path, TARGET, {})[TARGET];
      var targetPrototype = target.prototype;
      var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;
      var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;
      for (key in source) {
        FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? "." : "#") + key, options.forced);
        USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);
        targetProperty = target[key];
        if (USE_NATIVE)
          if (options.dontCallGetSet) {
            descriptor = getOwnPropertyDescriptor(nativeSource, key);
            nativeProperty = descriptor && descriptor.value;
          } else
            nativeProperty = nativeSource[key];
        sourceProperty = USE_NATIVE && nativeProperty ? nativeProperty : source[key];
        if (!FORCED && !PROTO && typeof targetProperty == typeof sourceProperty)
          continue;
        if (options.bind && USE_NATIVE)
          resultProperty = bind(sourceProperty, globalThis2);
        else if (options.wrap && USE_NATIVE)
          resultProperty = wrapConstructor(sourceProperty);
        else if (PROTO && isCallable(sourceProperty))
          resultProperty = uncurryThis(sourceProperty);
        else
          resultProperty = sourceProperty;
        if (options.sham || sourceProperty && sourceProperty.sham || targetProperty && targetProperty.sham) {
          createNonEnumerableProperty(resultProperty, "sham", true);
        }
        createNonEnumerableProperty(target, key, resultProperty);
        if (PROTO) {
          VIRTUAL_PROTOTYPE = TARGET + "Prototype";
          if (!hasOwn(path, VIRTUAL_PROTOTYPE)) {
            createNonEnumerableProperty(path, VIRTUAL_PROTOTYPE, {});
          }
          createNonEnumerableProperty(path[VIRTUAL_PROTOTYPE], key, sourceProperty);
          if (options.real && targetPrototype && (FORCED || !targetPrototype[key])) {
            createNonEnumerableProperty(targetPrototype, key, sourceProperty);
          }
        }
      }
    };
  }
});

// node_modules/core-js-pure/internals/is-array.js
var require_is_array = __commonJS({
  "node_modules/core-js-pure/internals/is-array.js"(exports, module) {
    "use strict";
    var classof = require_classof_raw();
    module.exports = Array.isArray || function isArray(argument) {
      return classof(argument) === "Array";
    };
  }
});

// node_modules/core-js-pure/modules/es.array.is-array.js
var require_es_array_is_array = __commonJS({
  "node_modules/core-js-pure/modules/es.array.is-array.js"() {
    "use strict";
    var $2 = require_export();
    var isArray = require_is_array();
    $2({ target: "Array", stat: true }, {
      isArray
    });
  }
});

// node_modules/core-js-pure/es/array/is-array.js
var require_is_array2 = __commonJS({
  "node_modules/core-js-pure/es/array/is-array.js"(exports, module) {
    "use strict";
    require_es_array_is_array();
    var path = require_path();
    module.exports = path.Array.isArray;
  }
});

// node_modules/core-js-pure/stable/array/is-array.js
var require_is_array3 = __commonJS({
  "node_modules/core-js-pure/stable/array/is-array.js"(exports, module) {
    "use strict";
    var parent = require_is_array2();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/array/is-array.js
var require_is_array4 = __commonJS({
  "node_modules/core-js-pure/actual/array/is-array.js"(exports, module) {
    "use strict";
    var parent = require_is_array3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/array/is-array.js
var require_is_array5 = __commonJS({
  "node_modules/core-js-pure/full/array/is-array.js"(exports, module) {
    "use strict";
    var parent = require_is_array4();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/array/is-array.js
var require_is_array6 = __commonJS({
  "node_modules/core-js-pure/features/array/is-array.js"(exports, module) {
    "use strict";
    module.exports = require_is_array5();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/array/is-array.js
var require_is_array7 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/array/is-array.js"(exports, module) {
    module.exports = require_is_array6();
  }
});

// node_modules/core-js-pure/internals/math-trunc.js
var require_math_trunc = __commonJS({
  "node_modules/core-js-pure/internals/math-trunc.js"(exports, module) {
    "use strict";
    var ceil = Math.ceil;
    var floor = Math.floor;
    module.exports = Math.trunc || function trunc(x2) {
      var n = +x2;
      return (n > 0 ? floor : ceil)(n);
    };
  }
});

// node_modules/core-js-pure/internals/to-integer-or-infinity.js
var require_to_integer_or_infinity = __commonJS({
  "node_modules/core-js-pure/internals/to-integer-or-infinity.js"(exports, module) {
    "use strict";
    var trunc = require_math_trunc();
    module.exports = function(argument) {
      var number = +argument;
      return number !== number || number === 0 ? 0 : trunc(number);
    };
  }
});

// node_modules/core-js-pure/internals/to-length.js
var require_to_length = __commonJS({
  "node_modules/core-js-pure/internals/to-length.js"(exports, module) {
    "use strict";
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var min = Math.min;
    module.exports = function(argument) {
      var len = toIntegerOrInfinity(argument);
      return len > 0 ? min(len, 9007199254740991) : 0;
    };
  }
});

// node_modules/core-js-pure/internals/length-of-array-like.js
var require_length_of_array_like = __commonJS({
  "node_modules/core-js-pure/internals/length-of-array-like.js"(exports, module) {
    "use strict";
    var toLength = require_to_length();
    module.exports = function(obj) {
      return toLength(obj.length);
    };
  }
});

// node_modules/core-js-pure/internals/does-not-exceed-safe-integer.js
var require_does_not_exceed_safe_integer = __commonJS({
  "node_modules/core-js-pure/internals/does-not-exceed-safe-integer.js"(exports, module) {
    "use strict";
    var $TypeError = TypeError;
    var MAX_SAFE_INTEGER = 9007199254740991;
    module.exports = function(it) {
      if (it > MAX_SAFE_INTEGER)
        throw $TypeError("Maximum allowed index exceeded");
      return it;
    };
  }
});

// node_modules/core-js-pure/internals/create-property.js
var require_create_property = __commonJS({
  "node_modules/core-js-pure/internals/create-property.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var definePropertyModule = require_object_define_property();
    var createPropertyDescriptor = require_create_property_descriptor();
    module.exports = function(object, key, value) {
      if (DESCRIPTORS)
        definePropertyModule.f(object, key, createPropertyDescriptor(0, value));
      else
        object[key] = value;
    };
  }
});

// node_modules/core-js-pure/internals/to-string-tag-support.js
var require_to_string_tag_support = __commonJS({
  "node_modules/core-js-pure/internals/to-string-tag-support.js"(exports, module) {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    var TO_STRING_TAG = wellKnownSymbol("toStringTag");
    var test = {};
    test[TO_STRING_TAG] = "z";
    module.exports = String(test) === "[object z]";
  }
});

// node_modules/core-js-pure/internals/classof.js
var require_classof = __commonJS({
  "node_modules/core-js-pure/internals/classof.js"(exports, module) {
    "use strict";
    var TO_STRING_TAG_SUPPORT = require_to_string_tag_support();
    var isCallable = require_is_callable();
    var classofRaw = require_classof_raw();
    var wellKnownSymbol = require_well_known_symbol();
    var TO_STRING_TAG = wellKnownSymbol("toStringTag");
    var $Object = Object;
    var CORRECT_ARGUMENTS = classofRaw(function() {
      return arguments;
    }()) === "Arguments";
    var tryGet = function(it, key) {
      try {
        return it[key];
      } catch (error) {
      }
    };
    module.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function(it) {
      var O2, tag, result;
      return it === void 0 ? "Undefined" : it === null ? "Null" : typeof (tag = tryGet(O2 = $Object(it), TO_STRING_TAG)) == "string" ? tag : CORRECT_ARGUMENTS ? classofRaw(O2) : (result = classofRaw(O2)) === "Object" && isCallable(O2.callee) ? "Arguments" : result;
    };
  }
});

// node_modules/core-js-pure/internals/inspect-source.js
var require_inspect_source = __commonJS({
  "node_modules/core-js-pure/internals/inspect-source.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var isCallable = require_is_callable();
    var store = require_shared_store();
    var functionToString = uncurryThis(Function.toString);
    if (!isCallable(store.inspectSource)) {
      store.inspectSource = function(it) {
        return functionToString(it);
      };
    }
    module.exports = store.inspectSource;
  }
});

// node_modules/core-js-pure/internals/is-constructor.js
var require_is_constructor = __commonJS({
  "node_modules/core-js-pure/internals/is-constructor.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var fails = require_fails();
    var isCallable = require_is_callable();
    var classof = require_classof();
    var getBuiltIn = require_get_built_in();
    var inspectSource = require_inspect_source();
    var noop = function() {
    };
    var construct = getBuiltIn("Reflect", "construct");
    var constructorRegExp = /^\s*(?:class|function)\b/;
    var exec = uncurryThis(constructorRegExp.exec);
    var INCORRECT_TO_STRING = !constructorRegExp.test(noop);
    var isConstructorModern = function isConstructor(argument) {
      if (!isCallable(argument))
        return false;
      try {
        construct(noop, [], argument);
        return true;
      } catch (error) {
        return false;
      }
    };
    var isConstructorLegacy = function isConstructor(argument) {
      if (!isCallable(argument))
        return false;
      switch (classof(argument)) {
        case "AsyncFunction":
        case "GeneratorFunction":
        case "AsyncGeneratorFunction":
          return false;
      }
      try {
        return INCORRECT_TO_STRING || !!exec(constructorRegExp, inspectSource(argument));
      } catch (error) {
        return true;
      }
    };
    isConstructorLegacy.sham = true;
    module.exports = !construct || fails(function() {
      var called;
      return isConstructorModern(isConstructorModern.call) || !isConstructorModern(Object) || !isConstructorModern(function() {
        called = true;
      }) || called;
    }) ? isConstructorLegacy : isConstructorModern;
  }
});

// node_modules/core-js-pure/internals/array-species-constructor.js
var require_array_species_constructor = __commonJS({
  "node_modules/core-js-pure/internals/array-species-constructor.js"(exports, module) {
    "use strict";
    var isArray = require_is_array();
    var isConstructor = require_is_constructor();
    var isObject = require_is_object();
    var wellKnownSymbol = require_well_known_symbol();
    var SPECIES = wellKnownSymbol("species");
    var $Array = Array;
    module.exports = function(originalArray) {
      var C;
      if (isArray(originalArray)) {
        C = originalArray.constructor;
        if (isConstructor(C) && (C === $Array || isArray(C.prototype)))
          C = void 0;
        else if (isObject(C)) {
          C = C[SPECIES];
          if (C === null)
            C = void 0;
        }
      }
      return C === void 0 ? $Array : C;
    };
  }
});

// node_modules/core-js-pure/internals/array-species-create.js
var require_array_species_create = __commonJS({
  "node_modules/core-js-pure/internals/array-species-create.js"(exports, module) {
    "use strict";
    var arraySpeciesConstructor = require_array_species_constructor();
    module.exports = function(originalArray, length) {
      return new (arraySpeciesConstructor(originalArray))(length === 0 ? 0 : length);
    };
  }
});

// node_modules/core-js-pure/internals/array-method-has-species-support.js
var require_array_method_has_species_support = __commonJS({
  "node_modules/core-js-pure/internals/array-method-has-species-support.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    var wellKnownSymbol = require_well_known_symbol();
    var V8_VERSION = require_environment_v8_version();
    var SPECIES = wellKnownSymbol("species");
    module.exports = function(METHOD_NAME) {
      return V8_VERSION >= 51 || !fails(function() {
        var array = [];
        var constructor = array.constructor = {};
        constructor[SPECIES] = function() {
          return { foo: 1 };
        };
        return array[METHOD_NAME](Boolean).foo !== 1;
      });
    };
  }
});

// node_modules/core-js-pure/modules/es.array.concat.js
var require_es_array_concat = __commonJS({
  "node_modules/core-js-pure/modules/es.array.concat.js"() {
    "use strict";
    var $2 = require_export();
    var fails = require_fails();
    var isArray = require_is_array();
    var isObject = require_is_object();
    var toObject = require_to_object();
    var lengthOfArrayLike = require_length_of_array_like();
    var doesNotExceedSafeInteger = require_does_not_exceed_safe_integer();
    var createProperty = require_create_property();
    var arraySpeciesCreate = require_array_species_create();
    var arrayMethodHasSpeciesSupport = require_array_method_has_species_support();
    var wellKnownSymbol = require_well_known_symbol();
    var V8_VERSION = require_environment_v8_version();
    var IS_CONCAT_SPREADABLE = wellKnownSymbol("isConcatSpreadable");
    var IS_CONCAT_SPREADABLE_SUPPORT = V8_VERSION >= 51 || !fails(function() {
      var array = [];
      array[IS_CONCAT_SPREADABLE] = false;
      return array.concat()[0] !== array;
    });
    var isConcatSpreadable = function(O2) {
      if (!isObject(O2))
        return false;
      var spreadable = O2[IS_CONCAT_SPREADABLE];
      return spreadable !== void 0 ? !!spreadable : isArray(O2);
    };
    var FORCED = !IS_CONCAT_SPREADABLE_SUPPORT || !arrayMethodHasSpeciesSupport("concat");
    $2({ target: "Array", proto: true, arity: 1, forced: FORCED }, {
      // eslint-disable-next-line no-unused-vars -- required for `.length`
      concat: function concat(arg) {
        var O2 = toObject(this);
        var A = arraySpeciesCreate(O2, 0);
        var n = 0;
        var i2, k2, length, len, E2;
        for (i2 = -1, length = arguments.length; i2 < length; i2++) {
          E2 = i2 === -1 ? O2 : arguments[i2];
          if (isConcatSpreadable(E2)) {
            len = lengthOfArrayLike(E2);
            doesNotExceedSafeInteger(n + len);
            for (k2 = 0; k2 < len; k2++, n++)
              if (k2 in E2)
                createProperty(A, n, E2[k2]);
          } else {
            doesNotExceedSafeInteger(n + 1);
            createProperty(A, n++, E2);
          }
        }
        A.length = n;
        return A;
      }
    });
  }
});

// node_modules/core-js-pure/modules/es.object.to-string.js
var require_es_object_to_string = __commonJS({
  "node_modules/core-js-pure/modules/es.object.to-string.js"() {
  }
});

// node_modules/core-js-pure/internals/to-string.js
var require_to_string = __commonJS({
  "node_modules/core-js-pure/internals/to-string.js"(exports, module) {
    "use strict";
    var classof = require_classof();
    var $String = String;
    module.exports = function(argument) {
      if (classof(argument) === "Symbol")
        throw new TypeError("Cannot convert a Symbol value to a string");
      return $String(argument);
    };
  }
});

// node_modules/core-js-pure/internals/to-absolute-index.js
var require_to_absolute_index = __commonJS({
  "node_modules/core-js-pure/internals/to-absolute-index.js"(exports, module) {
    "use strict";
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var max = Math.max;
    var min = Math.min;
    module.exports = function(index, length) {
      var integer = toIntegerOrInfinity(index);
      return integer < 0 ? max(integer + length, 0) : min(integer, length);
    };
  }
});

// node_modules/core-js-pure/internals/array-includes.js
var require_array_includes = __commonJS({
  "node_modules/core-js-pure/internals/array-includes.js"(exports, module) {
    "use strict";
    var toIndexedObject = require_to_indexed_object();
    var toAbsoluteIndex = require_to_absolute_index();
    var lengthOfArrayLike = require_length_of_array_like();
    var createMethod = function(IS_INCLUDES) {
      return function($this, el, fromIndex) {
        var O2 = toIndexedObject($this);
        var length = lengthOfArrayLike(O2);
        if (length === 0)
          return !IS_INCLUDES && -1;
        var index = toAbsoluteIndex(fromIndex, length);
        var value;
        if (IS_INCLUDES && el !== el)
          while (length > index) {
            value = O2[index++];
            if (value !== value)
              return true;
          }
        else
          for (; length > index; index++) {
            if ((IS_INCLUDES || index in O2) && O2[index] === el)
              return IS_INCLUDES || index || 0;
          }
        return !IS_INCLUDES && -1;
      };
    };
    module.exports = {
      // `Array.prototype.includes` method
      // https://tc39.es/ecma262/#sec-array.prototype.includes
      includes: createMethod(true),
      // `Array.prototype.indexOf` method
      // https://tc39.es/ecma262/#sec-array.prototype.indexof
      indexOf: createMethod(false)
    };
  }
});

// node_modules/core-js-pure/internals/hidden-keys.js
var require_hidden_keys = __commonJS({
  "node_modules/core-js-pure/internals/hidden-keys.js"(exports, module) {
    "use strict";
    module.exports = {};
  }
});

// node_modules/core-js-pure/internals/object-keys-internal.js
var require_object_keys_internal = __commonJS({
  "node_modules/core-js-pure/internals/object-keys-internal.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var hasOwn = require_has_own_property();
    var toIndexedObject = require_to_indexed_object();
    var indexOf = require_array_includes().indexOf;
    var hiddenKeys = require_hidden_keys();
    var push = uncurryThis([].push);
    module.exports = function(object, names) {
      var O2 = toIndexedObject(object);
      var i2 = 0;
      var result = [];
      var key;
      for (key in O2)
        !hasOwn(hiddenKeys, key) && hasOwn(O2, key) && push(result, key);
      while (names.length > i2)
        if (hasOwn(O2, key = names[i2++])) {
          ~indexOf(result, key) || push(result, key);
        }
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/enum-bug-keys.js
var require_enum_bug_keys = __commonJS({
  "node_modules/core-js-pure/internals/enum-bug-keys.js"(exports, module) {
    "use strict";
    module.exports = [
      "constructor",
      "hasOwnProperty",
      "isPrototypeOf",
      "propertyIsEnumerable",
      "toLocaleString",
      "toString",
      "valueOf"
    ];
  }
});

// node_modules/core-js-pure/internals/object-keys.js
var require_object_keys = __commonJS({
  "node_modules/core-js-pure/internals/object-keys.js"(exports, module) {
    "use strict";
    var internalObjectKeys = require_object_keys_internal();
    var enumBugKeys = require_enum_bug_keys();
    module.exports = Object.keys || function keys(O2) {
      return internalObjectKeys(O2, enumBugKeys);
    };
  }
});

// node_modules/core-js-pure/internals/object-define-properties.js
var require_object_define_properties = __commonJS({
  "node_modules/core-js-pure/internals/object-define-properties.js"(exports) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var V8_PROTOTYPE_DEFINE_BUG = require_v8_prototype_define_bug();
    var definePropertyModule = require_object_define_property();
    var anObject = require_an_object();
    var toIndexedObject = require_to_indexed_object();
    var objectKeys = require_object_keys();
    exports.f = DESCRIPTORS && !V8_PROTOTYPE_DEFINE_BUG ? Object.defineProperties : function defineProperties(O2, Properties) {
      anObject(O2);
      var props = toIndexedObject(Properties);
      var keys = objectKeys(Properties);
      var length = keys.length;
      var index = 0;
      var key;
      while (length > index)
        definePropertyModule.f(O2, key = keys[index++], props[key]);
      return O2;
    };
  }
});

// node_modules/core-js-pure/internals/html.js
var require_html = __commonJS({
  "node_modules/core-js-pure/internals/html.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    module.exports = getBuiltIn("document", "documentElement");
  }
});

// node_modules/core-js-pure/internals/shared-key.js
var require_shared_key = __commonJS({
  "node_modules/core-js-pure/internals/shared-key.js"(exports, module) {
    "use strict";
    var shared = require_shared();
    var uid = require_uid();
    var keys = shared("keys");
    module.exports = function(key) {
      return keys[key] || (keys[key] = uid(key));
    };
  }
});

// node_modules/core-js-pure/internals/object-create.js
var require_object_create = __commonJS({
  "node_modules/core-js-pure/internals/object-create.js"(exports, module) {
    "use strict";
    var anObject = require_an_object();
    var definePropertiesModule = require_object_define_properties();
    var enumBugKeys = require_enum_bug_keys();
    var hiddenKeys = require_hidden_keys();
    var html = require_html();
    var documentCreateElement = require_document_create_element();
    var sharedKey = require_shared_key();
    var GT = ">";
    var LT = "<";
    var PROTOTYPE = "prototype";
    var SCRIPT = "script";
    var IE_PROTO = sharedKey("IE_PROTO");
    var EmptyConstructor = function() {
    };
    var scriptTag = function(content) {
      return LT + SCRIPT + GT + content + LT + "/" + SCRIPT + GT;
    };
    var NullProtoObjectViaActiveX = function(activeXDocument2) {
      activeXDocument2.write(scriptTag(""));
      activeXDocument2.close();
      var temp = activeXDocument2.parentWindow.Object;
      activeXDocument2 = null;
      return temp;
    };
    var NullProtoObjectViaIFrame = function() {
      var iframe = documentCreateElement("iframe");
      var JS = "java" + SCRIPT + ":";
      var iframeDocument;
      iframe.style.display = "none";
      html.appendChild(iframe);
      iframe.src = String(JS);
      iframeDocument = iframe.contentWindow.document;
      iframeDocument.open();
      iframeDocument.write(scriptTag("document.F=Object"));
      iframeDocument.close();
      return iframeDocument.F;
    };
    var activeXDocument;
    var NullProtoObject = function() {
      try {
        activeXDocument = new ActiveXObject("htmlfile");
      } catch (error) {
      }
      NullProtoObject = typeof document != "undefined" ? document.domain && activeXDocument ? NullProtoObjectViaActiveX(activeXDocument) : NullProtoObjectViaIFrame() : NullProtoObjectViaActiveX(activeXDocument);
      var length = enumBugKeys.length;
      while (length--)
        delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];
      return NullProtoObject();
    };
    hiddenKeys[IE_PROTO] = true;
    module.exports = Object.create || function create(O2, Properties) {
      var result;
      if (O2 !== null) {
        EmptyConstructor[PROTOTYPE] = anObject(O2);
        result = new EmptyConstructor();
        EmptyConstructor[PROTOTYPE] = null;
        result[IE_PROTO] = O2;
      } else
        result = NullProtoObject();
      return Properties === void 0 ? result : definePropertiesModule.f(result, Properties);
    };
  }
});

// node_modules/core-js-pure/internals/object-get-own-property-names.js
var require_object_get_own_property_names = __commonJS({
  "node_modules/core-js-pure/internals/object-get-own-property-names.js"(exports) {
    "use strict";
    var internalObjectKeys = require_object_keys_internal();
    var enumBugKeys = require_enum_bug_keys();
    var hiddenKeys = enumBugKeys.concat("length", "prototype");
    exports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O2) {
      return internalObjectKeys(O2, hiddenKeys);
    };
  }
});

// node_modules/core-js-pure/internals/array-slice.js
var require_array_slice = __commonJS({
  "node_modules/core-js-pure/internals/array-slice.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    module.exports = uncurryThis([].slice);
  }
});

// node_modules/core-js-pure/internals/object-get-own-property-names-external.js
var require_object_get_own_property_names_external = __commonJS({
  "node_modules/core-js-pure/internals/object-get-own-property-names-external.js"(exports, module) {
    "use strict";
    var classof = require_classof_raw();
    var toIndexedObject = require_to_indexed_object();
    var $getOwnPropertyNames = require_object_get_own_property_names().f;
    var arraySlice = require_array_slice();
    var windowNames = typeof window == "object" && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [];
    var getWindowNames = function(it) {
      try {
        return $getOwnPropertyNames(it);
      } catch (error) {
        return arraySlice(windowNames);
      }
    };
    module.exports.f = function getOwnPropertyNames(it) {
      return windowNames && classof(it) === "Window" ? getWindowNames(it) : $getOwnPropertyNames(toIndexedObject(it));
    };
  }
});

// node_modules/core-js-pure/internals/object-get-own-property-symbols.js
var require_object_get_own_property_symbols = __commonJS({
  "node_modules/core-js-pure/internals/object-get-own-property-symbols.js"(exports) {
    "use strict";
    exports.f = Object.getOwnPropertySymbols;
  }
});

// node_modules/core-js-pure/internals/define-built-in.js
var require_define_built_in = __commonJS({
  "node_modules/core-js-pure/internals/define-built-in.js"(exports, module) {
    "use strict";
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    module.exports = function(target, key, value, options) {
      if (options && options.enumerable)
        target[key] = value;
      else
        createNonEnumerableProperty(target, key, value);
      return target;
    };
  }
});

// node_modules/core-js-pure/internals/define-built-in-accessor.js
var require_define_built_in_accessor = __commonJS({
  "node_modules/core-js-pure/internals/define-built-in-accessor.js"(exports, module) {
    "use strict";
    var defineProperty = require_object_define_property();
    module.exports = function(target, name, descriptor) {
      return defineProperty.f(target, name, descriptor);
    };
  }
});

// node_modules/core-js-pure/internals/well-known-symbol-wrapped.js
var require_well_known_symbol_wrapped = __commonJS({
  "node_modules/core-js-pure/internals/well-known-symbol-wrapped.js"(exports) {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    exports.f = wellKnownSymbol;
  }
});

// node_modules/core-js-pure/internals/well-known-symbol-define.js
var require_well_known_symbol_define = __commonJS({
  "node_modules/core-js-pure/internals/well-known-symbol-define.js"(exports, module) {
    "use strict";
    var path = require_path();
    var hasOwn = require_has_own_property();
    var wrappedWellKnownSymbolModule = require_well_known_symbol_wrapped();
    var defineProperty = require_object_define_property().f;
    module.exports = function(NAME) {
      var Symbol2 = path.Symbol || (path.Symbol = {});
      if (!hasOwn(Symbol2, NAME))
        defineProperty(Symbol2, NAME, {
          value: wrappedWellKnownSymbolModule.f(NAME)
        });
    };
  }
});

// node_modules/core-js-pure/internals/symbol-define-to-primitive.js
var require_symbol_define_to_primitive = __commonJS({
  "node_modules/core-js-pure/internals/symbol-define-to-primitive.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    var getBuiltIn = require_get_built_in();
    var wellKnownSymbol = require_well_known_symbol();
    var defineBuiltIn = require_define_built_in();
    module.exports = function() {
      var Symbol2 = getBuiltIn("Symbol");
      var SymbolPrototype = Symbol2 && Symbol2.prototype;
      var valueOf = SymbolPrototype && SymbolPrototype.valueOf;
      var TO_PRIMITIVE = wellKnownSymbol("toPrimitive");
      if (SymbolPrototype && !SymbolPrototype[TO_PRIMITIVE]) {
        defineBuiltIn(SymbolPrototype, TO_PRIMITIVE, function(hint) {
          return call(valueOf, this);
        }, { arity: 1 });
      }
    };
  }
});

// node_modules/core-js-pure/internals/object-to-string.js
var require_object_to_string = __commonJS({
  "node_modules/core-js-pure/internals/object-to-string.js"(exports, module) {
    "use strict";
    var TO_STRING_TAG_SUPPORT = require_to_string_tag_support();
    var classof = require_classof();
    module.exports = TO_STRING_TAG_SUPPORT ? {}.toString : function toString() {
      return "[object " + classof(this) + "]";
    };
  }
});

// node_modules/core-js-pure/internals/set-to-string-tag.js
var require_set_to_string_tag = __commonJS({
  "node_modules/core-js-pure/internals/set-to-string-tag.js"(exports, module) {
    "use strict";
    var TO_STRING_TAG_SUPPORT = require_to_string_tag_support();
    var defineProperty = require_object_define_property().f;
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var hasOwn = require_has_own_property();
    var toString = require_object_to_string();
    var wellKnownSymbol = require_well_known_symbol();
    var TO_STRING_TAG = wellKnownSymbol("toStringTag");
    module.exports = function(it, TAG, STATIC, SET_METHOD) {
      var target = STATIC ? it : it && it.prototype;
      if (target) {
        if (!hasOwn(target, TO_STRING_TAG)) {
          defineProperty(target, TO_STRING_TAG, { configurable: true, value: TAG });
        }
        if (SET_METHOD && !TO_STRING_TAG_SUPPORT) {
          createNonEnumerableProperty(target, "toString", toString);
        }
      }
    };
  }
});

// node_modules/core-js-pure/internals/weak-map-basic-detection.js
var require_weak_map_basic_detection = __commonJS({
  "node_modules/core-js-pure/internals/weak-map-basic-detection.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var isCallable = require_is_callable();
    var WeakMap = globalThis2.WeakMap;
    module.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));
  }
});

// node_modules/core-js-pure/internals/internal-state.js
var require_internal_state = __commonJS({
  "node_modules/core-js-pure/internals/internal-state.js"(exports, module) {
    "use strict";
    var NATIVE_WEAK_MAP = require_weak_map_basic_detection();
    var globalThis2 = require_global_this();
    var isObject = require_is_object();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var hasOwn = require_has_own_property();
    var shared = require_shared_store();
    var sharedKey = require_shared_key();
    var hiddenKeys = require_hidden_keys();
    var OBJECT_ALREADY_INITIALIZED = "Object already initialized";
    var TypeError2 = globalThis2.TypeError;
    var WeakMap = globalThis2.WeakMap;
    var set;
    var get;
    var has;
    var enforce = function(it) {
      return has(it) ? get(it) : set(it, {});
    };
    var getterFor = function(TYPE) {
      return function(it) {
        var state;
        if (!isObject(it) || (state = get(it)).type !== TYPE) {
          throw new TypeError2("Incompatible receiver, " + TYPE + " required");
        }
        return state;
      };
    };
    if (NATIVE_WEAK_MAP || shared.state) {
      store = shared.state || (shared.state = new WeakMap());
      store.get = store.get;
      store.has = store.has;
      store.set = store.set;
      set = function(it, metadata) {
        if (store.has(it))
          throw new TypeError2(OBJECT_ALREADY_INITIALIZED);
        metadata.facade = it;
        store.set(it, metadata);
        return metadata;
      };
      get = function(it) {
        return store.get(it) || {};
      };
      has = function(it) {
        return store.has(it);
      };
    } else {
      STATE = sharedKey("state");
      hiddenKeys[STATE] = true;
      set = function(it, metadata) {
        if (hasOwn(it, STATE))
          throw new TypeError2(OBJECT_ALREADY_INITIALIZED);
        metadata.facade = it;
        createNonEnumerableProperty(it, STATE, metadata);
        return metadata;
      };
      get = function(it) {
        return hasOwn(it, STATE) ? it[STATE] : {};
      };
      has = function(it) {
        return hasOwn(it, STATE);
      };
    }
    var store;
    var STATE;
    module.exports = {
      set,
      get,
      has,
      enforce,
      getterFor
    };
  }
});

// node_modules/core-js-pure/internals/array-iteration.js
var require_array_iteration = __commonJS({
  "node_modules/core-js-pure/internals/array-iteration.js"(exports, module) {
    "use strict";
    var bind = require_function_bind_context();
    var uncurryThis = require_function_uncurry_this();
    var IndexedObject = require_indexed_object();
    var toObject = require_to_object();
    var lengthOfArrayLike = require_length_of_array_like();
    var arraySpeciesCreate = require_array_species_create();
    var push = uncurryThis([].push);
    var createMethod = function(TYPE) {
      var IS_MAP = TYPE === 1;
      var IS_FILTER = TYPE === 2;
      var IS_SOME = TYPE === 3;
      var IS_EVERY = TYPE === 4;
      var IS_FIND_INDEX = TYPE === 6;
      var IS_FILTER_REJECT = TYPE === 7;
      var NO_HOLES = TYPE === 5 || IS_FIND_INDEX;
      return function($this, callbackfn, that, specificCreate) {
        var O2 = toObject($this);
        var self2 = IndexedObject(O2);
        var length = lengthOfArrayLike(self2);
        var boundFunction = bind(callbackfn, that);
        var index = 0;
        var create = specificCreate || arraySpeciesCreate;
        var target = IS_MAP ? create($this, length) : IS_FILTER || IS_FILTER_REJECT ? create($this, 0) : void 0;
        var value, result;
        for (; length > index; index++)
          if (NO_HOLES || index in self2) {
            value = self2[index];
            result = boundFunction(value, index, O2);
            if (TYPE) {
              if (IS_MAP)
                target[index] = result;
              else if (result)
                switch (TYPE) {
                  case 3:
                    return true;
                  case 5:
                    return value;
                  case 6:
                    return index;
                  case 2:
                    push(target, value);
                }
              else
                switch (TYPE) {
                  case 4:
                    return false;
                  case 7:
                    push(target, value);
                }
            }
          }
        return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;
      };
    };
    module.exports = {
      // `Array.prototype.forEach` method
      // https://tc39.es/ecma262/#sec-array.prototype.foreach
      forEach: createMethod(0),
      // `Array.prototype.map` method
      // https://tc39.es/ecma262/#sec-array.prototype.map
      map: createMethod(1),
      // `Array.prototype.filter` method
      // https://tc39.es/ecma262/#sec-array.prototype.filter
      filter: createMethod(2),
      // `Array.prototype.some` method
      // https://tc39.es/ecma262/#sec-array.prototype.some
      some: createMethod(3),
      // `Array.prototype.every` method
      // https://tc39.es/ecma262/#sec-array.prototype.every
      every: createMethod(4),
      // `Array.prototype.find` method
      // https://tc39.es/ecma262/#sec-array.prototype.find
      find: createMethod(5),
      // `Array.prototype.findIndex` method
      // https://tc39.es/ecma262/#sec-array.prototype.findIndex
      findIndex: createMethod(6),
      // `Array.prototype.filterReject` method
      // https://github.com/tc39/proposal-array-filtering
      filterReject: createMethod(7)
    };
  }
});

// node_modules/core-js-pure/modules/es.symbol.constructor.js
var require_es_symbol_constructor = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.constructor.js"() {
    "use strict";
    var $2 = require_export();
    var globalThis2 = require_global_this();
    var call = require_function_call();
    var uncurryThis = require_function_uncurry_this();
    var IS_PURE = require_is_pure();
    var DESCRIPTORS = require_descriptors();
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    var fails = require_fails();
    var hasOwn = require_has_own_property();
    var isPrototypeOf = require_object_is_prototype_of();
    var anObject = require_an_object();
    var toIndexedObject = require_to_indexed_object();
    var toPropertyKey = require_to_property_key();
    var $toString = require_to_string();
    var createPropertyDescriptor = require_create_property_descriptor();
    var nativeObjectCreate = require_object_create();
    var objectKeys = require_object_keys();
    var getOwnPropertyNamesModule = require_object_get_own_property_names();
    var getOwnPropertyNamesExternal = require_object_get_own_property_names_external();
    var getOwnPropertySymbolsModule = require_object_get_own_property_symbols();
    var getOwnPropertyDescriptorModule = require_object_get_own_property_descriptor();
    var definePropertyModule = require_object_define_property();
    var definePropertiesModule = require_object_define_properties();
    var propertyIsEnumerableModule = require_object_property_is_enumerable();
    var defineBuiltIn = require_define_built_in();
    var defineBuiltInAccessor = require_define_built_in_accessor();
    var shared = require_shared();
    var sharedKey = require_shared_key();
    var hiddenKeys = require_hidden_keys();
    var uid = require_uid();
    var wellKnownSymbol = require_well_known_symbol();
    var wrappedWellKnownSymbolModule = require_well_known_symbol_wrapped();
    var defineWellKnownSymbol = require_well_known_symbol_define();
    var defineSymbolToPrimitive = require_symbol_define_to_primitive();
    var setToStringTag = require_set_to_string_tag();
    var InternalStateModule = require_internal_state();
    var $forEach = require_array_iteration().forEach;
    var HIDDEN = sharedKey("hidden");
    var SYMBOL = "Symbol";
    var PROTOTYPE = "prototype";
    var setInternalState = InternalStateModule.set;
    var getInternalState = InternalStateModule.getterFor(SYMBOL);
    var ObjectPrototype = Object[PROTOTYPE];
    var $Symbol = globalThis2.Symbol;
    var SymbolPrototype = $Symbol && $Symbol[PROTOTYPE];
    var RangeError2 = globalThis2.RangeError;
    var TypeError2 = globalThis2.TypeError;
    var QObject = globalThis2.QObject;
    var nativeGetOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;
    var nativeDefineProperty = definePropertyModule.f;
    var nativeGetOwnPropertyNames = getOwnPropertyNamesExternal.f;
    var nativePropertyIsEnumerable = propertyIsEnumerableModule.f;
    var push = uncurryThis([].push);
    var AllSymbols = shared("symbols");
    var ObjectPrototypeSymbols = shared("op-symbols");
    var WellKnownSymbolsStore = shared("wks");
    var USE_SETTER = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;
    var fallbackDefineProperty = function(O2, P2, Attributes) {
      var ObjectPrototypeDescriptor = nativeGetOwnPropertyDescriptor(ObjectPrototype, P2);
      if (ObjectPrototypeDescriptor)
        delete ObjectPrototype[P2];
      nativeDefineProperty(O2, P2, Attributes);
      if (ObjectPrototypeDescriptor && O2 !== ObjectPrototype) {
        nativeDefineProperty(ObjectPrototype, P2, ObjectPrototypeDescriptor);
      }
    };
    var setSymbolDescriptor = DESCRIPTORS && fails(function() {
      return nativeObjectCreate(nativeDefineProperty({}, "a", {
        get: function() {
          return nativeDefineProperty(this, "a", { value: 7 }).a;
        }
      })).a !== 7;
    }) ? fallbackDefineProperty : nativeDefineProperty;
    var wrap = function(tag, description) {
      var symbol = AllSymbols[tag] = nativeObjectCreate(SymbolPrototype);
      setInternalState(symbol, {
        type: SYMBOL,
        tag,
        description
      });
      if (!DESCRIPTORS)
        symbol.description = description;
      return symbol;
    };
    var $defineProperty = function defineProperty(O2, P2, Attributes) {
      if (O2 === ObjectPrototype)
        $defineProperty(ObjectPrototypeSymbols, P2, Attributes);
      anObject(O2);
      var key = toPropertyKey(P2);
      anObject(Attributes);
      if (hasOwn(AllSymbols, key)) {
        if (!Attributes.enumerable) {
          if (!hasOwn(O2, HIDDEN))
            nativeDefineProperty(O2, HIDDEN, createPropertyDescriptor(1, nativeObjectCreate(null)));
          O2[HIDDEN][key] = true;
        } else {
          if (hasOwn(O2, HIDDEN) && O2[HIDDEN][key])
            O2[HIDDEN][key] = false;
          Attributes = nativeObjectCreate(Attributes, { enumerable: createPropertyDescriptor(0, false) });
        }
        return setSymbolDescriptor(O2, key, Attributes);
      }
      return nativeDefineProperty(O2, key, Attributes);
    };
    var $defineProperties = function defineProperties(O2, Properties) {
      anObject(O2);
      var properties = toIndexedObject(Properties);
      var keys = objectKeys(properties).concat($getOwnPropertySymbols(properties));
      $forEach(keys, function(key) {
        if (!DESCRIPTORS || call($propertyIsEnumerable, properties, key))
          $defineProperty(O2, key, properties[key]);
      });
      return O2;
    };
    var $create = function create(O2, Properties) {
      return Properties === void 0 ? nativeObjectCreate(O2) : $defineProperties(nativeObjectCreate(O2), Properties);
    };
    var $propertyIsEnumerable = function propertyIsEnumerable(V2) {
      var P2 = toPropertyKey(V2);
      var enumerable = call(nativePropertyIsEnumerable, this, P2);
      if (this === ObjectPrototype && hasOwn(AllSymbols, P2) && !hasOwn(ObjectPrototypeSymbols, P2))
        return false;
      return enumerable || !hasOwn(this, P2) || !hasOwn(AllSymbols, P2) || hasOwn(this, HIDDEN) && this[HIDDEN][P2] ? enumerable : true;
    };
    var $getOwnPropertyDescriptor = function getOwnPropertyDescriptor(O2, P2) {
      var it = toIndexedObject(O2);
      var key = toPropertyKey(P2);
      if (it === ObjectPrototype && hasOwn(AllSymbols, key) && !hasOwn(ObjectPrototypeSymbols, key))
        return;
      var descriptor = nativeGetOwnPropertyDescriptor(it, key);
      if (descriptor && hasOwn(AllSymbols, key) && !(hasOwn(it, HIDDEN) && it[HIDDEN][key])) {
        descriptor.enumerable = true;
      }
      return descriptor;
    };
    var $getOwnPropertyNames = function getOwnPropertyNames(O2) {
      var names = nativeGetOwnPropertyNames(toIndexedObject(O2));
      var result = [];
      $forEach(names, function(key) {
        if (!hasOwn(AllSymbols, key) && !hasOwn(hiddenKeys, key))
          push(result, key);
      });
      return result;
    };
    var $getOwnPropertySymbols = function(O2) {
      var IS_OBJECT_PROTOTYPE = O2 === ObjectPrototype;
      var names = nativeGetOwnPropertyNames(IS_OBJECT_PROTOTYPE ? ObjectPrototypeSymbols : toIndexedObject(O2));
      var result = [];
      $forEach(names, function(key) {
        if (hasOwn(AllSymbols, key) && (!IS_OBJECT_PROTOTYPE || hasOwn(ObjectPrototype, key))) {
          push(result, AllSymbols[key]);
        }
      });
      return result;
    };
    if (!NATIVE_SYMBOL) {
      $Symbol = function Symbol2() {
        if (isPrototypeOf(SymbolPrototype, this))
          throw new TypeError2("Symbol is not a constructor");
        var description = !arguments.length || arguments[0] === void 0 ? void 0 : $toString(arguments[0]);
        var tag = uid(description);
        var setter = function(value) {
          var $this = this === void 0 ? globalThis2 : this;
          if ($this === ObjectPrototype)
            call(setter, ObjectPrototypeSymbols, value);
          if (hasOwn($this, HIDDEN) && hasOwn($this[HIDDEN], tag))
            $this[HIDDEN][tag] = false;
          var descriptor = createPropertyDescriptor(1, value);
          try {
            setSymbolDescriptor($this, tag, descriptor);
          } catch (error) {
            if (!(error instanceof RangeError2))
              throw error;
            fallbackDefineProperty($this, tag, descriptor);
          }
        };
        if (DESCRIPTORS && USE_SETTER)
          setSymbolDescriptor(ObjectPrototype, tag, { configurable: true, set: setter });
        return wrap(tag, description);
      };
      SymbolPrototype = $Symbol[PROTOTYPE];
      defineBuiltIn(SymbolPrototype, "toString", function toString() {
        return getInternalState(this).tag;
      });
      defineBuiltIn($Symbol, "withoutSetter", function(description) {
        return wrap(uid(description), description);
      });
      propertyIsEnumerableModule.f = $propertyIsEnumerable;
      definePropertyModule.f = $defineProperty;
      definePropertiesModule.f = $defineProperties;
      getOwnPropertyDescriptorModule.f = $getOwnPropertyDescriptor;
      getOwnPropertyNamesModule.f = getOwnPropertyNamesExternal.f = $getOwnPropertyNames;
      getOwnPropertySymbolsModule.f = $getOwnPropertySymbols;
      wrappedWellKnownSymbolModule.f = function(name) {
        return wrap(wellKnownSymbol(name), name);
      };
      if (DESCRIPTORS) {
        defineBuiltInAccessor(SymbolPrototype, "description", {
          configurable: true,
          get: function description() {
            return getInternalState(this).description;
          }
        });
        if (!IS_PURE) {
          defineBuiltIn(ObjectPrototype, "propertyIsEnumerable", $propertyIsEnumerable, { unsafe: true });
        }
      }
    }
    $2({ global: true, constructor: true, wrap: true, forced: !NATIVE_SYMBOL, sham: !NATIVE_SYMBOL }, {
      Symbol: $Symbol
    });
    $forEach(objectKeys(WellKnownSymbolsStore), function(name) {
      defineWellKnownSymbol(name);
    });
    $2({ target: SYMBOL, stat: true, forced: !NATIVE_SYMBOL }, {
      useSetter: function() {
        USE_SETTER = true;
      },
      useSimple: function() {
        USE_SETTER = false;
      }
    });
    $2({ target: "Object", stat: true, forced: !NATIVE_SYMBOL, sham: !DESCRIPTORS }, {
      // `Object.create` method
      // https://tc39.es/ecma262/#sec-object.create
      create: $create,
      // `Object.defineProperty` method
      // https://tc39.es/ecma262/#sec-object.defineproperty
      defineProperty: $defineProperty,
      // `Object.defineProperties` method
      // https://tc39.es/ecma262/#sec-object.defineproperties
      defineProperties: $defineProperties,
      // `Object.getOwnPropertyDescriptor` method
      // https://tc39.es/ecma262/#sec-object.getownpropertydescriptors
      getOwnPropertyDescriptor: $getOwnPropertyDescriptor
    });
    $2({ target: "Object", stat: true, forced: !NATIVE_SYMBOL }, {
      // `Object.getOwnPropertyNames` method
      // https://tc39.es/ecma262/#sec-object.getownpropertynames
      getOwnPropertyNames: $getOwnPropertyNames
    });
    defineSymbolToPrimitive();
    setToStringTag($Symbol, SYMBOL);
    hiddenKeys[HIDDEN] = true;
  }
});

// node_modules/core-js-pure/internals/symbol-registry-detection.js
var require_symbol_registry_detection = __commonJS({
  "node_modules/core-js-pure/internals/symbol-registry-detection.js"(exports, module) {
    "use strict";
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    module.exports = NATIVE_SYMBOL && !!Symbol["for"] && !!Symbol.keyFor;
  }
});

// node_modules/core-js-pure/modules/es.symbol.for.js
var require_es_symbol_for = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.for.js"() {
    "use strict";
    var $2 = require_export();
    var getBuiltIn = require_get_built_in();
    var hasOwn = require_has_own_property();
    var toString = require_to_string();
    var shared = require_shared();
    var NATIVE_SYMBOL_REGISTRY = require_symbol_registry_detection();
    var StringToSymbolRegistry = shared("string-to-symbol-registry");
    var SymbolToStringRegistry = shared("symbol-to-string-registry");
    $2({ target: "Symbol", stat: true, forced: !NATIVE_SYMBOL_REGISTRY }, {
      "for": function(key) {
        var string = toString(key);
        if (hasOwn(StringToSymbolRegistry, string))
          return StringToSymbolRegistry[string];
        var symbol = getBuiltIn("Symbol")(string);
        StringToSymbolRegistry[string] = symbol;
        SymbolToStringRegistry[symbol] = string;
        return symbol;
      }
    });
  }
});

// node_modules/core-js-pure/modules/es.symbol.key-for.js
var require_es_symbol_key_for = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.key-for.js"() {
    "use strict";
    var $2 = require_export();
    var hasOwn = require_has_own_property();
    var isSymbol = require_is_symbol();
    var tryToString = require_try_to_string();
    var shared = require_shared();
    var NATIVE_SYMBOL_REGISTRY = require_symbol_registry_detection();
    var SymbolToStringRegistry = shared("symbol-to-string-registry");
    $2({ target: "Symbol", stat: true, forced: !NATIVE_SYMBOL_REGISTRY }, {
      keyFor: function keyFor(sym) {
        if (!isSymbol(sym))
          throw new TypeError(tryToString(sym) + " is not a symbol");
        if (hasOwn(SymbolToStringRegistry, sym))
          return SymbolToStringRegistry[sym];
      }
    });
  }
});

// node_modules/core-js-pure/internals/get-json-replacer-function.js
var require_get_json_replacer_function = __commonJS({
  "node_modules/core-js-pure/internals/get-json-replacer-function.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var isArray = require_is_array();
    var isCallable = require_is_callable();
    var classof = require_classof_raw();
    var toString = require_to_string();
    var push = uncurryThis([].push);
    module.exports = function(replacer) {
      if (isCallable(replacer))
        return replacer;
      if (!isArray(replacer))
        return;
      var rawLength = replacer.length;
      var keys = [];
      for (var i2 = 0; i2 < rawLength; i2++) {
        var element = replacer[i2];
        if (typeof element == "string")
          push(keys, element);
        else if (typeof element == "number" || classof(element) === "Number" || classof(element) === "String")
          push(keys, toString(element));
      }
      var keysLength = keys.length;
      var root = true;
      return function(key, value) {
        if (root) {
          root = false;
          return value;
        }
        if (isArray(this))
          return value;
        for (var j = 0; j < keysLength; j++)
          if (keys[j] === key)
            return value;
      };
    };
  }
});

// node_modules/core-js-pure/modules/es.json.stringify.js
var require_es_json_stringify = __commonJS({
  "node_modules/core-js-pure/modules/es.json.stringify.js"() {
    "use strict";
    var $2 = require_export();
    var getBuiltIn = require_get_built_in();
    var apply = require_function_apply();
    var call = require_function_call();
    var uncurryThis = require_function_uncurry_this();
    var fails = require_fails();
    var isCallable = require_is_callable();
    var isSymbol = require_is_symbol();
    var arraySlice = require_array_slice();
    var getReplacerFunction = require_get_json_replacer_function();
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    var $String = String;
    var $stringify = getBuiltIn("JSON", "stringify");
    var exec = uncurryThis(/./.exec);
    var charAt = uncurryThis("".charAt);
    var charCodeAt = uncurryThis("".charCodeAt);
    var replace = uncurryThis("".replace);
    var numberToString = uncurryThis(1.1.toString);
    var tester = /[\uD800-\uDFFF]/g;
    var low = /^[\uD800-\uDBFF]$/;
    var hi = /^[\uDC00-\uDFFF]$/;
    var WRONG_SYMBOLS_CONVERSION = !NATIVE_SYMBOL || fails(function() {
      var symbol = getBuiltIn("Symbol")("stringify detection");
      return $stringify([symbol]) !== "[null]" || $stringify({ a: symbol }) !== "{}" || $stringify(Object(symbol)) !== "{}";
    });
    var ILL_FORMED_UNICODE = fails(function() {
      return $stringify("\uDF06\uD834") !== '"\\udf06\\ud834"' || $stringify("\uDEAD") !== '"\\udead"';
    });
    var stringifyWithSymbolsFix = function(it, replacer) {
      var args = arraySlice(arguments);
      var $replacer = getReplacerFunction(replacer);
      if (!isCallable($replacer) && (it === void 0 || isSymbol(it)))
        return;
      args[1] = function(key, value) {
        if (isCallable($replacer))
          value = call($replacer, this, $String(key), value);
        if (!isSymbol(value))
          return value;
      };
      return apply($stringify, null, args);
    };
    var fixIllFormed = function(match, offset, string) {
      var prev = charAt(string, offset - 1);
      var next = charAt(string, offset + 1);
      if (exec(low, match) && !exec(hi, next) || exec(hi, match) && !exec(low, prev)) {
        return "\\u" + numberToString(charCodeAt(match, 0), 16);
      }
      return match;
    };
    if ($stringify) {
      $2({ target: "JSON", stat: true, arity: 3, forced: WRONG_SYMBOLS_CONVERSION || ILL_FORMED_UNICODE }, {
        // eslint-disable-next-line no-unused-vars -- required for `.length`
        stringify: function stringify(it, replacer, space) {
          var args = arraySlice(arguments);
          var result = apply(WRONG_SYMBOLS_CONVERSION ? stringifyWithSymbolsFix : $stringify, null, args);
          return ILL_FORMED_UNICODE && typeof result == "string" ? replace(result, tester, fixIllFormed) : result;
        }
      });
    }
  }
});

// node_modules/core-js-pure/modules/es.object.get-own-property-symbols.js
var require_es_object_get_own_property_symbols = __commonJS({
  "node_modules/core-js-pure/modules/es.object.get-own-property-symbols.js"() {
    "use strict";
    var $2 = require_export();
    var NATIVE_SYMBOL = require_symbol_constructor_detection();
    var fails = require_fails();
    var getOwnPropertySymbolsModule = require_object_get_own_property_symbols();
    var toObject = require_to_object();
    var FORCED = !NATIVE_SYMBOL || fails(function() {
      getOwnPropertySymbolsModule.f(1);
    });
    $2({ target: "Object", stat: true, forced: FORCED }, {
      getOwnPropertySymbols: function getOwnPropertySymbols(it) {
        var $getOwnPropertySymbols = getOwnPropertySymbolsModule.f;
        return $getOwnPropertySymbols ? $getOwnPropertySymbols(toObject(it)) : [];
      }
    });
  }
});

// node_modules/core-js-pure/modules/es.symbol.js
var require_es_symbol = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.js"() {
    "use strict";
    require_es_symbol_constructor();
    require_es_symbol_for();
    require_es_symbol_key_for();
    require_es_json_stringify();
    require_es_object_get_own_property_symbols();
  }
});

// node_modules/core-js-pure/modules/es.symbol.async-dispose.js
var require_es_symbol_async_dispose = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.async-dispose.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("asyncDispose");
  }
});

// node_modules/core-js-pure/modules/es.symbol.async-iterator.js
var require_es_symbol_async_iterator = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.async-iterator.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("asyncIterator");
  }
});

// node_modules/core-js-pure/modules/es.symbol.description.js
var require_es_symbol_description = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.description.js"() {
  }
});

// node_modules/core-js-pure/modules/es.symbol.dispose.js
var require_es_symbol_dispose = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.dispose.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("dispose");
  }
});

// node_modules/core-js-pure/modules/es.symbol.has-instance.js
var require_es_symbol_has_instance = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.has-instance.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("hasInstance");
  }
});

// node_modules/core-js-pure/modules/es.symbol.is-concat-spreadable.js
var require_es_symbol_is_concat_spreadable = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.is-concat-spreadable.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("isConcatSpreadable");
  }
});

// node_modules/core-js-pure/modules/es.symbol.iterator.js
var require_es_symbol_iterator = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.iterator.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("iterator");
  }
});

// node_modules/core-js-pure/modules/es.symbol.match.js
var require_es_symbol_match = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.match.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("match");
  }
});

// node_modules/core-js-pure/modules/es.symbol.match-all.js
var require_es_symbol_match_all = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.match-all.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("matchAll");
  }
});

// node_modules/core-js-pure/modules/es.symbol.replace.js
var require_es_symbol_replace = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.replace.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("replace");
  }
});

// node_modules/core-js-pure/modules/es.symbol.search.js
var require_es_symbol_search = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.search.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("search");
  }
});

// node_modules/core-js-pure/modules/es.symbol.species.js
var require_es_symbol_species = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.species.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("species");
  }
});

// node_modules/core-js-pure/modules/es.symbol.split.js
var require_es_symbol_split = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.split.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("split");
  }
});

// node_modules/core-js-pure/modules/es.symbol.to-primitive.js
var require_es_symbol_to_primitive = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.to-primitive.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    var defineSymbolToPrimitive = require_symbol_define_to_primitive();
    defineWellKnownSymbol("toPrimitive");
    defineSymbolToPrimitive();
  }
});

// node_modules/core-js-pure/modules/es.symbol.to-string-tag.js
var require_es_symbol_to_string_tag = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.to-string-tag.js"() {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var defineWellKnownSymbol = require_well_known_symbol_define();
    var setToStringTag = require_set_to_string_tag();
    defineWellKnownSymbol("toStringTag");
    setToStringTag(getBuiltIn("Symbol"), "Symbol");
  }
});

// node_modules/core-js-pure/modules/es.symbol.unscopables.js
var require_es_symbol_unscopables = __commonJS({
  "node_modules/core-js-pure/modules/es.symbol.unscopables.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("unscopables");
  }
});

// node_modules/core-js-pure/modules/es.json.to-string-tag.js
var require_es_json_to_string_tag = __commonJS({
  "node_modules/core-js-pure/modules/es.json.to-string-tag.js"() {
    "use strict";
    var globalThis2 = require_global_this();
    var setToStringTag = require_set_to_string_tag();
    setToStringTag(globalThis2.JSON, "JSON", true);
  }
});

// node_modules/core-js-pure/modules/es.math.to-string-tag.js
var require_es_math_to_string_tag = __commonJS({
  "node_modules/core-js-pure/modules/es.math.to-string-tag.js"() {
  }
});

// node_modules/core-js-pure/modules/es.reflect.to-string-tag.js
var require_es_reflect_to_string_tag = __commonJS({
  "node_modules/core-js-pure/modules/es.reflect.to-string-tag.js"() {
  }
});

// node_modules/core-js-pure/es/symbol/index.js
var require_symbol = __commonJS({
  "node_modules/core-js-pure/es/symbol/index.js"(exports, module) {
    "use strict";
    require_es_array_concat();
    require_es_object_to_string();
    require_es_symbol();
    require_es_symbol_async_dispose();
    require_es_symbol_async_iterator();
    require_es_symbol_description();
    require_es_symbol_dispose();
    require_es_symbol_has_instance();
    require_es_symbol_is_concat_spreadable();
    require_es_symbol_iterator();
    require_es_symbol_match();
    require_es_symbol_match_all();
    require_es_symbol_replace();
    require_es_symbol_search();
    require_es_symbol_species();
    require_es_symbol_split();
    require_es_symbol_to_primitive();
    require_es_symbol_to_string_tag();
    require_es_symbol_unscopables();
    require_es_json_to_string_tag();
    require_es_math_to_string_tag();
    require_es_reflect_to_string_tag();
    var path = require_path();
    module.exports = path.Symbol;
  }
});

// node_modules/core-js-pure/internals/add-to-unscopables.js
var require_add_to_unscopables = __commonJS({
  "node_modules/core-js-pure/internals/add-to-unscopables.js"(exports, module) {
    "use strict";
    module.exports = function() {
    };
  }
});

// node_modules/core-js-pure/internals/iterators.js
var require_iterators = __commonJS({
  "node_modules/core-js-pure/internals/iterators.js"(exports, module) {
    "use strict";
    module.exports = {};
  }
});

// node_modules/core-js-pure/internals/function-name.js
var require_function_name = __commonJS({
  "node_modules/core-js-pure/internals/function-name.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var hasOwn = require_has_own_property();
    var FunctionPrototype = Function.prototype;
    var getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;
    var EXISTS = hasOwn(FunctionPrototype, "name");
    var PROPER = EXISTS && (function something() {
    }).name === "something";
    var CONFIGURABLE = EXISTS && (!DESCRIPTORS || DESCRIPTORS && getDescriptor(FunctionPrototype, "name").configurable);
    module.exports = {
      EXISTS,
      PROPER,
      CONFIGURABLE
    };
  }
});

// node_modules/core-js-pure/internals/correct-prototype-getter.js
var require_correct_prototype_getter = __commonJS({
  "node_modules/core-js-pure/internals/correct-prototype-getter.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = !fails(function() {
      function F2() {
      }
      F2.prototype.constructor = null;
      return Object.getPrototypeOf(new F2()) !== F2.prototype;
    });
  }
});

// node_modules/core-js-pure/internals/object-get-prototype-of.js
var require_object_get_prototype_of = __commonJS({
  "node_modules/core-js-pure/internals/object-get-prototype-of.js"(exports, module) {
    "use strict";
    var hasOwn = require_has_own_property();
    var isCallable = require_is_callable();
    var toObject = require_to_object();
    var sharedKey = require_shared_key();
    var CORRECT_PROTOTYPE_GETTER = require_correct_prototype_getter();
    var IE_PROTO = sharedKey("IE_PROTO");
    var $Object = Object;
    var ObjectPrototype = $Object.prototype;
    module.exports = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function(O2) {
      var object = toObject(O2);
      if (hasOwn(object, IE_PROTO))
        return object[IE_PROTO];
      var constructor = object.constructor;
      if (isCallable(constructor) && object instanceof constructor) {
        return constructor.prototype;
      }
      return object instanceof $Object ? ObjectPrototype : null;
    };
  }
});

// node_modules/core-js-pure/internals/iterators-core.js
var require_iterators_core = __commonJS({
  "node_modules/core-js-pure/internals/iterators-core.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    var isCallable = require_is_callable();
    var isObject = require_is_object();
    var create = require_object_create();
    var getPrototypeOf = require_object_get_prototype_of();
    var defineBuiltIn = require_define_built_in();
    var wellKnownSymbol = require_well_known_symbol();
    var IS_PURE = require_is_pure();
    var ITERATOR = wellKnownSymbol("iterator");
    var BUGGY_SAFARI_ITERATORS = false;
    var IteratorPrototype;
    var PrototypeOfArrayIteratorPrototype;
    var arrayIterator;
    if ([].keys) {
      arrayIterator = [].keys();
      if (!("next" in arrayIterator))
        BUGGY_SAFARI_ITERATORS = true;
      else {
        PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));
        if (PrototypeOfArrayIteratorPrototype !== Object.prototype)
          IteratorPrototype = PrototypeOfArrayIteratorPrototype;
      }
    }
    var NEW_ITERATOR_PROTOTYPE = !isObject(IteratorPrototype) || fails(function() {
      var test = {};
      return IteratorPrototype[ITERATOR].call(test) !== test;
    });
    if (NEW_ITERATOR_PROTOTYPE)
      IteratorPrototype = {};
    else if (IS_PURE)
      IteratorPrototype = create(IteratorPrototype);
    if (!isCallable(IteratorPrototype[ITERATOR])) {
      defineBuiltIn(IteratorPrototype, ITERATOR, function() {
        return this;
      });
    }
    module.exports = {
      IteratorPrototype,
      BUGGY_SAFARI_ITERATORS
    };
  }
});

// node_modules/core-js-pure/internals/iterator-create-constructor.js
var require_iterator_create_constructor = __commonJS({
  "node_modules/core-js-pure/internals/iterator-create-constructor.js"(exports, module) {
    "use strict";
    var IteratorPrototype = require_iterators_core().IteratorPrototype;
    var create = require_object_create();
    var createPropertyDescriptor = require_create_property_descriptor();
    var setToStringTag = require_set_to_string_tag();
    var Iterators = require_iterators();
    var returnThis = function() {
      return this;
    };
    module.exports = function(IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {
      var TO_STRING_TAG = NAME + " Iterator";
      IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(+!ENUMERABLE_NEXT, next) });
      setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);
      Iterators[TO_STRING_TAG] = returnThis;
      return IteratorConstructor;
    };
  }
});

// node_modules/core-js-pure/internals/function-uncurry-this-accessor.js
var require_function_uncurry_this_accessor = __commonJS({
  "node_modules/core-js-pure/internals/function-uncurry-this-accessor.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var aCallable = require_a_callable();
    module.exports = function(object, key, method) {
      try {
        return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));
      } catch (error) {
      }
    };
  }
});

// node_modules/core-js-pure/internals/is-possible-prototype.js
var require_is_possible_prototype = __commonJS({
  "node_modules/core-js-pure/internals/is-possible-prototype.js"(exports, module) {
    "use strict";
    var isObject = require_is_object();
    module.exports = function(argument) {
      return isObject(argument) || argument === null;
    };
  }
});

// node_modules/core-js-pure/internals/a-possible-prototype.js
var require_a_possible_prototype = __commonJS({
  "node_modules/core-js-pure/internals/a-possible-prototype.js"(exports, module) {
    "use strict";
    var isPossiblePrototype = require_is_possible_prototype();
    var $String = String;
    var $TypeError = TypeError;
    module.exports = function(argument) {
      if (isPossiblePrototype(argument))
        return argument;
      throw new $TypeError("Can't set " + $String(argument) + " as a prototype");
    };
  }
});

// node_modules/core-js-pure/internals/object-set-prototype-of.js
var require_object_set_prototype_of = __commonJS({
  "node_modules/core-js-pure/internals/object-set-prototype-of.js"(exports, module) {
    "use strict";
    var uncurryThisAccessor = require_function_uncurry_this_accessor();
    var isObject = require_is_object();
    var requireObjectCoercible = require_require_object_coercible();
    var aPossiblePrototype = require_a_possible_prototype();
    module.exports = Object.setPrototypeOf || ("__proto__" in {} ? function() {
      var CORRECT_SETTER = false;
      var test = {};
      var setter;
      try {
        setter = uncurryThisAccessor(Object.prototype, "__proto__", "set");
        setter(test, []);
        CORRECT_SETTER = test instanceof Array;
      } catch (error) {
      }
      return function setPrototypeOf(O2, proto) {
        requireObjectCoercible(O2);
        aPossiblePrototype(proto);
        if (!isObject(O2))
          return O2;
        if (CORRECT_SETTER)
          setter(O2, proto);
        else
          O2.__proto__ = proto;
        return O2;
      };
    }() : void 0);
  }
});

// node_modules/core-js-pure/internals/iterator-define.js
var require_iterator_define = __commonJS({
  "node_modules/core-js-pure/internals/iterator-define.js"(exports, module) {
    "use strict";
    var $2 = require_export();
    var call = require_function_call();
    var IS_PURE = require_is_pure();
    var FunctionName = require_function_name();
    var isCallable = require_is_callable();
    var createIteratorConstructor = require_iterator_create_constructor();
    var getPrototypeOf = require_object_get_prototype_of();
    var setPrototypeOf = require_object_set_prototype_of();
    var setToStringTag = require_set_to_string_tag();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var defineBuiltIn = require_define_built_in();
    var wellKnownSymbol = require_well_known_symbol();
    var Iterators = require_iterators();
    var IteratorsCore = require_iterators_core();
    var PROPER_FUNCTION_NAME = FunctionName.PROPER;
    var CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;
    var IteratorPrototype = IteratorsCore.IteratorPrototype;
    var BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;
    var ITERATOR = wellKnownSymbol("iterator");
    var KEYS = "keys";
    var VALUES = "values";
    var ENTRIES = "entries";
    var returnThis = function() {
      return this;
    };
    module.exports = function(Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {
      createIteratorConstructor(IteratorConstructor, NAME, next);
      var getIterationMethod = function(KIND) {
        if (KIND === DEFAULT && defaultIterator)
          return defaultIterator;
        if (!BUGGY_SAFARI_ITERATORS && KIND && KIND in IterablePrototype)
          return IterablePrototype[KIND];
        switch (KIND) {
          case KEYS:
            return function keys() {
              return new IteratorConstructor(this, KIND);
            };
          case VALUES:
            return function values() {
              return new IteratorConstructor(this, KIND);
            };
          case ENTRIES:
            return function entries() {
              return new IteratorConstructor(this, KIND);
            };
        }
        return function() {
          return new IteratorConstructor(this);
        };
      };
      var TO_STRING_TAG = NAME + " Iterator";
      var INCORRECT_VALUES_NAME = false;
      var IterablePrototype = Iterable.prototype;
      var nativeIterator = IterablePrototype[ITERATOR] || IterablePrototype["@@iterator"] || DEFAULT && IterablePrototype[DEFAULT];
      var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);
      var anyNativeIterator = NAME === "Array" ? IterablePrototype.entries || nativeIterator : nativeIterator;
      var CurrentIteratorPrototype, methods, KEY;
      if (anyNativeIterator) {
        CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));
        if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {
          if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {
            if (setPrototypeOf) {
              setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);
            } else if (!isCallable(CurrentIteratorPrototype[ITERATOR])) {
              defineBuiltIn(CurrentIteratorPrototype, ITERATOR, returnThis);
            }
          }
          setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);
          if (IS_PURE)
            Iterators[TO_STRING_TAG] = returnThis;
        }
      }
      if (PROPER_FUNCTION_NAME && DEFAULT === VALUES && nativeIterator && nativeIterator.name !== VALUES) {
        if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {
          createNonEnumerableProperty(IterablePrototype, "name", VALUES);
        } else {
          INCORRECT_VALUES_NAME = true;
          defaultIterator = function values() {
            return call(nativeIterator, this);
          };
        }
      }
      if (DEFAULT) {
        methods = {
          values: getIterationMethod(VALUES),
          keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),
          entries: getIterationMethod(ENTRIES)
        };
        if (FORCED)
          for (KEY in methods) {
            if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {
              defineBuiltIn(IterablePrototype, KEY, methods[KEY]);
            }
          }
        else
          $2({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);
      }
      if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {
        defineBuiltIn(IterablePrototype, ITERATOR, defaultIterator, { name: DEFAULT });
      }
      Iterators[NAME] = defaultIterator;
      return methods;
    };
  }
});

// node_modules/core-js-pure/internals/create-iter-result-object.js
var require_create_iter_result_object = __commonJS({
  "node_modules/core-js-pure/internals/create-iter-result-object.js"(exports, module) {
    "use strict";
    module.exports = function(value, done) {
      return { value, done };
    };
  }
});

// node_modules/core-js-pure/modules/es.array.iterator.js
var require_es_array_iterator = __commonJS({
  "node_modules/core-js-pure/modules/es.array.iterator.js"(exports, module) {
    "use strict";
    var toIndexedObject = require_to_indexed_object();
    var addToUnscopables = require_add_to_unscopables();
    var Iterators = require_iterators();
    var InternalStateModule = require_internal_state();
    var defineProperty = require_object_define_property().f;
    var defineIterator = require_iterator_define();
    var createIterResultObject = require_create_iter_result_object();
    var IS_PURE = require_is_pure();
    var DESCRIPTORS = require_descriptors();
    var ARRAY_ITERATOR = "Array Iterator";
    var setInternalState = InternalStateModule.set;
    var getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);
    module.exports = defineIterator(Array, "Array", function(iterated, kind) {
      setInternalState(this, {
        type: ARRAY_ITERATOR,
        target: toIndexedObject(iterated),
        // target
        index: 0,
        // next index
        kind
        // kind
      });
    }, function() {
      var state = getInternalState(this);
      var target = state.target;
      var index = state.index++;
      if (!target || index >= target.length) {
        state.target = null;
        return createIterResultObject(void 0, true);
      }
      switch (state.kind) {
        case "keys":
          return createIterResultObject(index, false);
        case "values":
          return createIterResultObject(target[index], false);
      }
      return createIterResultObject([index, target[index]], false);
    }, "values");
    var values = Iterators.Arguments = Iterators.Array;
    addToUnscopables("keys");
    addToUnscopables("values");
    addToUnscopables("entries");
    if (!IS_PURE && DESCRIPTORS && values.name !== "values")
      try {
        defineProperty(values, "name", { value: "values" });
      } catch (error) {
      }
  }
});

// node_modules/core-js-pure/internals/dom-iterables.js
var require_dom_iterables = __commonJS({
  "node_modules/core-js-pure/internals/dom-iterables.js"(exports, module) {
    "use strict";
    module.exports = {
      CSSRuleList: 0,
      CSSStyleDeclaration: 0,
      CSSValueList: 0,
      ClientRectList: 0,
      DOMRectList: 0,
      DOMStringList: 0,
      DOMTokenList: 1,
      DataTransferItemList: 0,
      FileList: 0,
      HTMLAllCollection: 0,
      HTMLCollection: 0,
      HTMLFormElement: 0,
      HTMLSelectElement: 0,
      MediaList: 0,
      MimeTypeArray: 0,
      NamedNodeMap: 0,
      NodeList: 1,
      PaintRequestList: 0,
      Plugin: 0,
      PluginArray: 0,
      SVGLengthList: 0,
      SVGNumberList: 0,
      SVGPathSegList: 0,
      SVGPointList: 0,
      SVGStringList: 0,
      SVGTransformList: 0,
      SourceBufferList: 0,
      StyleSheetList: 0,
      TextTrackCueList: 0,
      TextTrackList: 0,
      TouchList: 0
    };
  }
});

// node_modules/core-js-pure/modules/web.dom-collections.iterator.js
var require_web_dom_collections_iterator = __commonJS({
  "node_modules/core-js-pure/modules/web.dom-collections.iterator.js"() {
    "use strict";
    require_es_array_iterator();
    var DOMIterables = require_dom_iterables();
    var globalThis2 = require_global_this();
    var setToStringTag = require_set_to_string_tag();
    var Iterators = require_iterators();
    for (COLLECTION_NAME in DOMIterables) {
      setToStringTag(globalThis2[COLLECTION_NAME], COLLECTION_NAME);
      Iterators[COLLECTION_NAME] = Iterators.Array;
    }
    var COLLECTION_NAME;
  }
});

// node_modules/core-js-pure/stable/symbol/index.js
var require_symbol2 = __commonJS({
  "node_modules/core-js-pure/stable/symbol/index.js"(exports, module) {
    "use strict";
    var parent = require_symbol();
    require_web_dom_collections_iterator();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/modules/esnext.function.metadata.js
var require_esnext_function_metadata = __commonJS({
  "node_modules/core-js-pure/modules/esnext.function.metadata.js"() {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    var defineProperty = require_object_define_property().f;
    var METADATA = wellKnownSymbol("metadata");
    var FunctionPrototype = Function.prototype;
    if (FunctionPrototype[METADATA] === void 0) {
      defineProperty(FunctionPrototype, METADATA, {
        value: null
      });
    }
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.async-dispose.js
var require_esnext_symbol_async_dispose = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.async-dispose.js"() {
    "use strict";
    require_es_symbol_async_dispose();
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.dispose.js
var require_esnext_symbol_dispose = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.dispose.js"() {
    "use strict";
    require_es_symbol_dispose();
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.metadata.js
var require_esnext_symbol_metadata = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.metadata.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("metadata");
  }
});

// node_modules/core-js-pure/actual/symbol/index.js
var require_symbol3 = __commonJS({
  "node_modules/core-js-pure/actual/symbol/index.js"(exports, module) {
    "use strict";
    var parent = require_symbol2();
    require_esnext_function_metadata();
    require_esnext_symbol_async_dispose();
    require_esnext_symbol_dispose();
    require_esnext_symbol_metadata();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/internals/symbol-is-registered.js
var require_symbol_is_registered = __commonJS({
  "node_modules/core-js-pure/internals/symbol-is-registered.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var uncurryThis = require_function_uncurry_this();
    var Symbol2 = getBuiltIn("Symbol");
    var keyFor = Symbol2.keyFor;
    var thisSymbolValue = uncurryThis(Symbol2.prototype.valueOf);
    module.exports = Symbol2.isRegisteredSymbol || function isRegisteredSymbol(value) {
      try {
        return keyFor(thisSymbolValue(value)) !== void 0;
      } catch (error) {
        return false;
      }
    };
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.is-registered-symbol.js
var require_esnext_symbol_is_registered_symbol = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.is-registered-symbol.js"() {
    "use strict";
    var $2 = require_export();
    var isRegisteredSymbol = require_symbol_is_registered();
    $2({ target: "Symbol", stat: true }, {
      isRegisteredSymbol
    });
  }
});

// node_modules/core-js-pure/internals/symbol-is-well-known.js
var require_symbol_is_well_known = __commonJS({
  "node_modules/core-js-pure/internals/symbol-is-well-known.js"(exports, module) {
    "use strict";
    var shared = require_shared();
    var getBuiltIn = require_get_built_in();
    var uncurryThis = require_function_uncurry_this();
    var isSymbol = require_is_symbol();
    var wellKnownSymbol = require_well_known_symbol();
    var Symbol2 = getBuiltIn("Symbol");
    var $isWellKnownSymbol = Symbol2.isWellKnownSymbol;
    var getOwnPropertyNames = getBuiltIn("Object", "getOwnPropertyNames");
    var thisSymbolValue = uncurryThis(Symbol2.prototype.valueOf);
    var WellKnownSymbolsStore = shared("wks");
    for (i2 = 0, symbolKeys = getOwnPropertyNames(Symbol2), symbolKeysLength = symbolKeys.length; i2 < symbolKeysLength; i2++) {
      try {
        symbolKey = symbolKeys[i2];
        if (isSymbol(Symbol2[symbolKey]))
          wellKnownSymbol(symbolKey);
      } catch (error) {
      }
    }
    var symbolKey;
    var i2;
    var symbolKeys;
    var symbolKeysLength;
    module.exports = function isWellKnownSymbol(value) {
      if ($isWellKnownSymbol && $isWellKnownSymbol(value))
        return true;
      try {
        var symbol = thisSymbolValue(value);
        for (var j = 0, keys = getOwnPropertyNames(WellKnownSymbolsStore), keysLength = keys.length; j < keysLength; j++) {
          if (WellKnownSymbolsStore[keys[j]] == symbol)
            return true;
        }
      } catch (error) {
      }
      return false;
    };
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.is-well-known-symbol.js
var require_esnext_symbol_is_well_known_symbol = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.is-well-known-symbol.js"() {
    "use strict";
    var $2 = require_export();
    var isWellKnownSymbol = require_symbol_is_well_known();
    $2({ target: "Symbol", stat: true, forced: true }, {
      isWellKnownSymbol
    });
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.custom-matcher.js
var require_esnext_symbol_custom_matcher = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.custom-matcher.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("customMatcher");
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.observable.js
var require_esnext_symbol_observable = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.observable.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("observable");
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.is-registered.js
var require_esnext_symbol_is_registered = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.is-registered.js"() {
    "use strict";
    var $2 = require_export();
    var isRegisteredSymbol = require_symbol_is_registered();
    $2({ target: "Symbol", stat: true, name: "isRegisteredSymbol" }, {
      isRegistered: isRegisteredSymbol
    });
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.is-well-known.js
var require_esnext_symbol_is_well_known = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.is-well-known.js"() {
    "use strict";
    var $2 = require_export();
    var isWellKnownSymbol = require_symbol_is_well_known();
    $2({ target: "Symbol", stat: true, name: "isWellKnownSymbol", forced: true }, {
      isWellKnown: isWellKnownSymbol
    });
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.matcher.js
var require_esnext_symbol_matcher = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.matcher.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("matcher");
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.metadata-key.js
var require_esnext_symbol_metadata_key = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.metadata-key.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("metadataKey");
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.pattern-match.js
var require_esnext_symbol_pattern_match = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.pattern-match.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("patternMatch");
  }
});

// node_modules/core-js-pure/modules/esnext.symbol.replace-all.js
var require_esnext_symbol_replace_all = __commonJS({
  "node_modules/core-js-pure/modules/esnext.symbol.replace-all.js"() {
    "use strict";
    var defineWellKnownSymbol = require_well_known_symbol_define();
    defineWellKnownSymbol("replaceAll");
  }
});

// node_modules/core-js-pure/full/symbol/index.js
var require_symbol4 = __commonJS({
  "node_modules/core-js-pure/full/symbol/index.js"(exports, module) {
    "use strict";
    var parent = require_symbol3();
    require_esnext_symbol_is_registered_symbol();
    require_esnext_symbol_is_well_known_symbol();
    require_esnext_symbol_custom_matcher();
    require_esnext_symbol_observable();
    require_esnext_symbol_is_registered();
    require_esnext_symbol_is_well_known();
    require_esnext_symbol_matcher();
    require_esnext_symbol_metadata_key();
    require_esnext_symbol_pattern_match();
    require_esnext_symbol_replace_all();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/symbol/index.js
var require_symbol5 = __commonJS({
  "node_modules/core-js-pure/features/symbol/index.js"(exports, module) {
    "use strict";
    module.exports = require_symbol4();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/symbol.js
var require_symbol6 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/symbol.js"(exports, module) {
    module.exports = require_symbol5();
  }
});

// node_modules/core-js-pure/internals/string-multibyte.js
var require_string_multibyte = __commonJS({
  "node_modules/core-js-pure/internals/string-multibyte.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var toString = require_to_string();
    var requireObjectCoercible = require_require_object_coercible();
    var charAt = uncurryThis("".charAt);
    var charCodeAt = uncurryThis("".charCodeAt);
    var stringSlice = uncurryThis("".slice);
    var createMethod = function(CONVERT_TO_STRING) {
      return function($this, pos) {
        var S2 = toString(requireObjectCoercible($this));
        var position = toIntegerOrInfinity(pos);
        var size = S2.length;
        var first, second;
        if (position < 0 || position >= size)
          return CONVERT_TO_STRING ? "" : void 0;
        first = charCodeAt(S2, position);
        return first < 55296 || first > 56319 || position + 1 === size || (second = charCodeAt(S2, position + 1)) < 56320 || second > 57343 ? CONVERT_TO_STRING ? charAt(S2, position) : first : CONVERT_TO_STRING ? stringSlice(S2, position, position + 2) : (first - 55296 << 10) + (second - 56320) + 65536;
      };
    };
    module.exports = {
      // `String.prototype.codePointAt` method
      // https://tc39.es/ecma262/#sec-string.prototype.codepointat
      codeAt: createMethod(false),
      // `String.prototype.at` method
      // https://github.com/mathiasbynens/String.prototype.at
      charAt: createMethod(true)
    };
  }
});

// node_modules/core-js-pure/modules/es.string.iterator.js
var require_es_string_iterator = __commonJS({
  "node_modules/core-js-pure/modules/es.string.iterator.js"() {
    "use strict";
    var charAt = require_string_multibyte().charAt;
    var toString = require_to_string();
    var InternalStateModule = require_internal_state();
    var defineIterator = require_iterator_define();
    var createIterResultObject = require_create_iter_result_object();
    var STRING_ITERATOR = "String Iterator";
    var setInternalState = InternalStateModule.set;
    var getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);
    defineIterator(String, "String", function(iterated) {
      setInternalState(this, {
        type: STRING_ITERATOR,
        string: toString(iterated),
        index: 0
      });
    }, function next() {
      var state = getInternalState(this);
      var string = state.string;
      var index = state.index;
      var point;
      if (index >= string.length)
        return createIterResultObject(void 0, true);
      point = charAt(string, index);
      state.index += point.length;
      return createIterResultObject(point, false);
    });
  }
});

// node_modules/core-js-pure/internals/get-iterator-method.js
var require_get_iterator_method = __commonJS({
  "node_modules/core-js-pure/internals/get-iterator-method.js"(exports, module) {
    "use strict";
    var classof = require_classof();
    var getMethod = require_get_method();
    var isNullOrUndefined = require_is_null_or_undefined();
    var Iterators = require_iterators();
    var wellKnownSymbol = require_well_known_symbol();
    var ITERATOR = wellKnownSymbol("iterator");
    module.exports = function(it) {
      if (!isNullOrUndefined(it))
        return getMethod(it, ITERATOR) || getMethod(it, "@@iterator") || Iterators[classof(it)];
    };
  }
});

// node_modules/core-js-pure/es/get-iterator-method.js
var require_get_iterator_method2 = __commonJS({
  "node_modules/core-js-pure/es/get-iterator-method.js"(exports, module) {
    "use strict";
    require_es_array_iterator();
    require_es_string_iterator();
    var getIteratorMethod = require_get_iterator_method();
    module.exports = getIteratorMethod;
  }
});

// node_modules/core-js-pure/stable/get-iterator-method.js
var require_get_iterator_method3 = __commonJS({
  "node_modules/core-js-pure/stable/get-iterator-method.js"(exports, module) {
    "use strict";
    var parent = require_get_iterator_method2();
    require_web_dom_collections_iterator();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/get-iterator-method.js
var require_get_iterator_method4 = __commonJS({
  "node_modules/core-js-pure/actual/get-iterator-method.js"(exports, module) {
    "use strict";
    var parent = require_get_iterator_method3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/get-iterator-method.js
var require_get_iterator_method5 = __commonJS({
  "node_modules/core-js-pure/full/get-iterator-method.js"(exports, module) {
    "use strict";
    var parent = require_get_iterator_method4();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/get-iterator-method.js
var require_get_iterator_method6 = __commonJS({
  "node_modules/core-js-pure/features/get-iterator-method.js"(exports, module) {
    "use strict";
    module.exports = require_get_iterator_method5();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/get-iterator-method.js
var require_get_iterator_method7 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/get-iterator-method.js"(exports, module) {
    module.exports = require_get_iterator_method6();
  }
});

// node_modules/core-js-pure/internals/iterator-close.js
var require_iterator_close = __commonJS({
  "node_modules/core-js-pure/internals/iterator-close.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    var anObject = require_an_object();
    var getMethod = require_get_method();
    module.exports = function(iterator, kind, value) {
      var innerResult, innerError;
      anObject(iterator);
      try {
        innerResult = getMethod(iterator, "return");
        if (!innerResult) {
          if (kind === "throw")
            throw value;
          return value;
        }
        innerResult = call(innerResult, iterator);
      } catch (error) {
        innerError = true;
        innerResult = error;
      }
      if (kind === "throw")
        throw value;
      if (innerError)
        throw innerResult;
      anObject(innerResult);
      return value;
    };
  }
});

// node_modules/core-js-pure/internals/call-with-safe-iteration-closing.js
var require_call_with_safe_iteration_closing = __commonJS({
  "node_modules/core-js-pure/internals/call-with-safe-iteration-closing.js"(exports, module) {
    "use strict";
    var anObject = require_an_object();
    var iteratorClose = require_iterator_close();
    module.exports = function(iterator, fn, value, ENTRIES) {
      try {
        return ENTRIES ? fn(anObject(value)[0], value[1]) : fn(value);
      } catch (error) {
        iteratorClose(iterator, "throw", error);
      }
    };
  }
});

// node_modules/core-js-pure/internals/is-array-iterator-method.js
var require_is_array_iterator_method = __commonJS({
  "node_modules/core-js-pure/internals/is-array-iterator-method.js"(exports, module) {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    var Iterators = require_iterators();
    var ITERATOR = wellKnownSymbol("iterator");
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      return it !== void 0 && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);
    };
  }
});

// node_modules/core-js-pure/internals/get-iterator.js
var require_get_iterator = __commonJS({
  "node_modules/core-js-pure/internals/get-iterator.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    var aCallable = require_a_callable();
    var anObject = require_an_object();
    var tryToString = require_try_to_string();
    var getIteratorMethod = require_get_iterator_method();
    var $TypeError = TypeError;
    module.exports = function(argument, usingIterator) {
      var iteratorMethod = arguments.length < 2 ? getIteratorMethod(argument) : usingIterator;
      if (aCallable(iteratorMethod))
        return anObject(call(iteratorMethod, argument));
      throw new $TypeError(tryToString(argument) + " is not iterable");
    };
  }
});

// node_modules/core-js-pure/internals/array-from.js
var require_array_from = __commonJS({
  "node_modules/core-js-pure/internals/array-from.js"(exports, module) {
    "use strict";
    var bind = require_function_bind_context();
    var call = require_function_call();
    var toObject = require_to_object();
    var callWithSafeIterationClosing = require_call_with_safe_iteration_closing();
    var isArrayIteratorMethod = require_is_array_iterator_method();
    var isConstructor = require_is_constructor();
    var lengthOfArrayLike = require_length_of_array_like();
    var createProperty = require_create_property();
    var getIterator = require_get_iterator();
    var getIteratorMethod = require_get_iterator_method();
    var $Array = Array;
    module.exports = function from(arrayLike) {
      var O2 = toObject(arrayLike);
      var IS_CONSTRUCTOR = isConstructor(this);
      var argumentsLength = arguments.length;
      var mapfn = argumentsLength > 1 ? arguments[1] : void 0;
      var mapping = mapfn !== void 0;
      if (mapping)
        mapfn = bind(mapfn, argumentsLength > 2 ? arguments[2] : void 0);
      var iteratorMethod = getIteratorMethod(O2);
      var index = 0;
      var length, result, step, iterator, next, value;
      if (iteratorMethod && !(this === $Array && isArrayIteratorMethod(iteratorMethod))) {
        result = IS_CONSTRUCTOR ? new this() : [];
        iterator = getIterator(O2, iteratorMethod);
        next = iterator.next;
        for (; !(step = call(next, iterator)).done; index++) {
          value = mapping ? callWithSafeIterationClosing(iterator, mapfn, [step.value, index], true) : step.value;
          createProperty(result, index, value);
        }
      } else {
        length = lengthOfArrayLike(O2);
        result = IS_CONSTRUCTOR ? new this(length) : $Array(length);
        for (; length > index; index++) {
          value = mapping ? mapfn(O2[index], index) : O2[index];
          createProperty(result, index, value);
        }
      }
      result.length = index;
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/check-correctness-of-iteration.js
var require_check_correctness_of_iteration = __commonJS({
  "node_modules/core-js-pure/internals/check-correctness-of-iteration.js"(exports, module) {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    var ITERATOR = wellKnownSymbol("iterator");
    var SAFE_CLOSING = false;
    try {
      called = 0;
      iteratorWithReturn = {
        next: function() {
          return { done: !!called++ };
        },
        "return": function() {
          SAFE_CLOSING = true;
        }
      };
      iteratorWithReturn[ITERATOR] = function() {
        return this;
      };
      Array.from(iteratorWithReturn, function() {
        throw 2;
      });
    } catch (error) {
    }
    var called;
    var iteratorWithReturn;
    module.exports = function(exec, SKIP_CLOSING) {
      try {
        if (!SKIP_CLOSING && !SAFE_CLOSING)
          return false;
      } catch (error) {
        return false;
      }
      var ITERATION_SUPPORT = false;
      try {
        var object = {};
        object[ITERATOR] = function() {
          return {
            next: function() {
              return { done: ITERATION_SUPPORT = true };
            }
          };
        };
        exec(object);
      } catch (error) {
      }
      return ITERATION_SUPPORT;
    };
  }
});

// node_modules/core-js-pure/modules/es.array.from.js
var require_es_array_from = __commonJS({
  "node_modules/core-js-pure/modules/es.array.from.js"() {
    "use strict";
    var $2 = require_export();
    var from = require_array_from();
    var checkCorrectnessOfIteration = require_check_correctness_of_iteration();
    var INCORRECT_ITERATION = !checkCorrectnessOfIteration(function(iterable) {
      Array.from(iterable);
    });
    $2({ target: "Array", stat: true, forced: INCORRECT_ITERATION }, {
      from
    });
  }
});

// node_modules/core-js-pure/es/array/from.js
var require_from = __commonJS({
  "node_modules/core-js-pure/es/array/from.js"(exports, module) {
    "use strict";
    require_es_string_iterator();
    require_es_array_from();
    var path = require_path();
    module.exports = path.Array.from;
  }
});

// node_modules/core-js-pure/stable/array/from.js
var require_from2 = __commonJS({
  "node_modules/core-js-pure/stable/array/from.js"(exports, module) {
    "use strict";
    var parent = require_from();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/array/from.js
var require_from3 = __commonJS({
  "node_modules/core-js-pure/actual/array/from.js"(exports, module) {
    "use strict";
    var parent = require_from2();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/array/from.js
var require_from4 = __commonJS({
  "node_modules/core-js-pure/full/array/from.js"(exports, module) {
    "use strict";
    var parent = require_from3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/array/from.js
var require_from5 = __commonJS({
  "node_modules/core-js-pure/features/array/from.js"(exports, module) {
    "use strict";
    module.exports = require_from4();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/array/from.js
var require_from6 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/array/from.js"(exports, module) {
    module.exports = require_from5();
  }
});

// node_modules/core-js-pure/modules/es.array.slice.js
var require_es_array_slice = __commonJS({
  "node_modules/core-js-pure/modules/es.array.slice.js"() {
    "use strict";
    var $2 = require_export();
    var isArray = require_is_array();
    var isConstructor = require_is_constructor();
    var isObject = require_is_object();
    var toAbsoluteIndex = require_to_absolute_index();
    var lengthOfArrayLike = require_length_of_array_like();
    var toIndexedObject = require_to_indexed_object();
    var createProperty = require_create_property();
    var wellKnownSymbol = require_well_known_symbol();
    var arrayMethodHasSpeciesSupport = require_array_method_has_species_support();
    var nativeSlice = require_array_slice();
    var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport("slice");
    var SPECIES = wellKnownSymbol("species");
    var $Array = Array;
    var max = Math.max;
    $2({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
      slice: function slice(start, end) {
        var O2 = toIndexedObject(this);
        var length = lengthOfArrayLike(O2);
        var k2 = toAbsoluteIndex(start, length);
        var fin = toAbsoluteIndex(end === void 0 ? length : end, length);
        var Constructor, result, n;
        if (isArray(O2)) {
          Constructor = O2.constructor;
          if (isConstructor(Constructor) && (Constructor === $Array || isArray(Constructor.prototype))) {
            Constructor = void 0;
          } else if (isObject(Constructor)) {
            Constructor = Constructor[SPECIES];
            if (Constructor === null)
              Constructor = void 0;
          }
          if (Constructor === $Array || Constructor === void 0) {
            return nativeSlice(O2, k2, fin);
          }
        }
        result = new (Constructor === void 0 ? $Array : Constructor)(max(fin - k2, 0));
        for (n = 0; k2 < fin; k2++, n++)
          if (k2 in O2)
            createProperty(result, n, O2[k2]);
        result.length = n;
        return result;
      }
    });
  }
});

// node_modules/core-js-pure/internals/get-built-in-prototype-method.js
var require_get_built_in_prototype_method = __commonJS({
  "node_modules/core-js-pure/internals/get-built-in-prototype-method.js"(exports, module) {
    "use strict";
    var globalThis2 = require_global_this();
    var path = require_path();
    module.exports = function(CONSTRUCTOR, METHOD) {
      var Namespace = path[CONSTRUCTOR + "Prototype"];
      var pureMethod = Namespace && Namespace[METHOD];
      if (pureMethod)
        return pureMethod;
      var NativeConstructor = globalThis2[CONSTRUCTOR];
      var NativePrototype = NativeConstructor && NativeConstructor.prototype;
      return NativePrototype && NativePrototype[METHOD];
    };
  }
});

// node_modules/core-js-pure/es/array/virtual/slice.js
var require_slice = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/slice.js"(exports, module) {
    "use strict";
    require_es_array_slice();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "slice");
  }
});

// node_modules/core-js-pure/es/instance/slice.js
var require_slice2 = __commonJS({
  "node_modules/core-js-pure/es/instance/slice.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_slice();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.slice;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.slice ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/slice.js
var require_slice3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/slice.js"(exports, module) {
    "use strict";
    var parent = require_slice2();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/instance/slice.js
var require_slice4 = __commonJS({
  "node_modules/core-js-pure/actual/instance/slice.js"(exports, module) {
    "use strict";
    var parent = require_slice3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/instance/slice.js
var require_slice5 = __commonJS({
  "node_modules/core-js-pure/full/instance/slice.js"(exports, module) {
    "use strict";
    var parent = require_slice4();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/instance/slice.js
var require_slice6 = __commonJS({
  "node_modules/core-js-pure/features/instance/slice.js"(exports, module) {
    "use strict";
    module.exports = require_slice5();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/instance/slice.js
var require_slice7 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/instance/slice.js"(exports, module) {
    module.exports = require_slice6();
  }
});

// node_modules/core-js-pure/modules/es.object.define-property.js
var require_es_object_define_property = __commonJS({
  "node_modules/core-js-pure/modules/es.object.define-property.js"() {
    "use strict";
    var $2 = require_export();
    var DESCRIPTORS = require_descriptors();
    var defineProperty = require_object_define_property().f;
    $2({ target: "Object", stat: true, forced: Object.defineProperty !== defineProperty, sham: !DESCRIPTORS }, {
      defineProperty
    });
  }
});

// node_modules/core-js-pure/es/object/define-property.js
var require_define_property = __commonJS({
  "node_modules/core-js-pure/es/object/define-property.js"(exports, module) {
    "use strict";
    require_es_object_define_property();
    var path = require_path();
    var Object2 = path.Object;
    var defineProperty = module.exports = function defineProperty2(it, key, desc) {
      return Object2.defineProperty(it, key, desc);
    };
    if (Object2.defineProperty.sham)
      defineProperty.sham = true;
  }
});

// node_modules/core-js-pure/stable/object/define-property.js
var require_define_property2 = __commonJS({
  "node_modules/core-js-pure/stable/object/define-property.js"(exports, module) {
    "use strict";
    var parent = require_define_property();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/object/define-property.js
var require_define_property3 = __commonJS({
  "node_modules/core-js-pure/actual/object/define-property.js"(exports, module) {
    "use strict";
    var parent = require_define_property2();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/object/define-property.js
var require_define_property4 = __commonJS({
  "node_modules/core-js-pure/full/object/define-property.js"(exports, module) {
    "use strict";
    var parent = require_define_property3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/object/define-property.js
var require_define_property5 = __commonJS({
  "node_modules/core-js-pure/features/object/define-property.js"(exports, module) {
    "use strict";
    module.exports = require_define_property4();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/object/define-property.js
var require_define_property6 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/object/define-property.js"(exports, module) {
    module.exports = require_define_property5();
  }
});

// node_modules/core-js-pure/modules/es.array.map.js
var require_es_array_map = __commonJS({
  "node_modules/core-js-pure/modules/es.array.map.js"() {
    "use strict";
    var $2 = require_export();
    var $map = require_array_iteration().map;
    var arrayMethodHasSpeciesSupport = require_array_method_has_species_support();
    var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport("map");
    $2({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
      map: function map(callbackfn) {
        return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
      }
    });
  }
});

// node_modules/core-js-pure/es/array/virtual/map.js
var require_map = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/map.js"(exports, module) {
    "use strict";
    require_es_array_map();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "map");
  }
});

// node_modules/core-js-pure/es/instance/map.js
var require_map2 = __commonJS({
  "node_modules/core-js-pure/es/instance/map.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_map();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.map;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.map ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/map.js
var require_map3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/map.js"(exports, module) {
    "use strict";
    var parent = require_map2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/map.js
var require_map4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/map.js"(exports, module) {
    module.exports = require_map3();
  }
});

// node_modules/core-js-pure/modules/es.array.includes.js
var require_es_array_includes = __commonJS({
  "node_modules/core-js-pure/modules/es.array.includes.js"() {
    "use strict";
    var $2 = require_export();
    var $includes = require_array_includes().includes;
    var fails = require_fails();
    var addToUnscopables = require_add_to_unscopables();
    var BROKEN_ON_SPARSE = fails(function() {
      return !Array(1).includes();
    });
    $2({ target: "Array", proto: true, forced: BROKEN_ON_SPARSE }, {
      includes: function includes(el) {
        return $includes(this, el, arguments.length > 1 ? arguments[1] : void 0);
      }
    });
    addToUnscopables("includes");
  }
});

// node_modules/core-js-pure/es/array/virtual/includes.js
var require_includes = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/includes.js"(exports, module) {
    "use strict";
    require_es_array_includes();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "includes");
  }
});

// node_modules/core-js-pure/internals/is-regexp.js
var require_is_regexp = __commonJS({
  "node_modules/core-js-pure/internals/is-regexp.js"(exports, module) {
    "use strict";
    var isObject = require_is_object();
    var classof = require_classof_raw();
    var wellKnownSymbol = require_well_known_symbol();
    var MATCH = wellKnownSymbol("match");
    module.exports = function(it) {
      var isRegExp;
      return isObject(it) && ((isRegExp = it[MATCH]) !== void 0 ? !!isRegExp : classof(it) === "RegExp");
    };
  }
});

// node_modules/core-js-pure/internals/not-a-regexp.js
var require_not_a_regexp = __commonJS({
  "node_modules/core-js-pure/internals/not-a-regexp.js"(exports, module) {
    "use strict";
    var isRegExp = require_is_regexp();
    var $TypeError = TypeError;
    module.exports = function(it) {
      if (isRegExp(it)) {
        throw new $TypeError("The method doesn't accept regular expressions");
      }
      return it;
    };
  }
});

// node_modules/core-js-pure/internals/correct-is-regexp-logic.js
var require_correct_is_regexp_logic = __commonJS({
  "node_modules/core-js-pure/internals/correct-is-regexp-logic.js"(exports, module) {
    "use strict";
    var wellKnownSymbol = require_well_known_symbol();
    var MATCH = wellKnownSymbol("match");
    module.exports = function(METHOD_NAME) {
      var regexp = /./;
      try {
        "/./"[METHOD_NAME](regexp);
      } catch (error1) {
        try {
          regexp[MATCH] = false;
          return "/./"[METHOD_NAME](regexp);
        } catch (error2) {
        }
      }
      return false;
    };
  }
});

// node_modules/core-js-pure/modules/es.string.includes.js
var require_es_string_includes = __commonJS({
  "node_modules/core-js-pure/modules/es.string.includes.js"() {
    "use strict";
    var $2 = require_export();
    var uncurryThis = require_function_uncurry_this();
    var notARegExp = require_not_a_regexp();
    var requireObjectCoercible = require_require_object_coercible();
    var toString = require_to_string();
    var correctIsRegExpLogic = require_correct_is_regexp_logic();
    var stringIndexOf = uncurryThis("".indexOf);
    $2({ target: "String", proto: true, forced: !correctIsRegExpLogic("includes") }, {
      includes: function includes(searchString) {
        return !!~stringIndexOf(
          toString(requireObjectCoercible(this)),
          toString(notARegExp(searchString)),
          arguments.length > 1 ? arguments[1] : void 0
        );
      }
    });
  }
});

// node_modules/core-js-pure/es/string/virtual/includes.js
var require_includes2 = __commonJS({
  "node_modules/core-js-pure/es/string/virtual/includes.js"(exports, module) {
    "use strict";
    require_es_string_includes();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("String", "includes");
  }
});

// node_modules/core-js-pure/es/instance/includes.js
var require_includes3 = __commonJS({
  "node_modules/core-js-pure/es/instance/includes.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var arrayMethod = require_includes();
    var stringMethod = require_includes2();
    var ArrayPrototype = Array.prototype;
    var StringPrototype = String.prototype;
    module.exports = function(it) {
      var own = it.includes;
      if (it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.includes)
        return arrayMethod;
      if (typeof it == "string" || it === StringPrototype || isPrototypeOf(StringPrototype, it) && own === StringPrototype.includes) {
        return stringMethod;
      }
      return own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/includes.js
var require_includes4 = __commonJS({
  "node_modules/core-js-pure/stable/instance/includes.js"(exports, module) {
    "use strict";
    var parent = require_includes3();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/includes.js
var require_includes5 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/includes.js"(exports, module) {
    module.exports = require_includes4();
  }
});

// node_modules/chroma-js/chroma.js
var require_chroma = __commonJS({
  "node_modules/chroma-js/chroma.js"(exports, module) {
    (function(global2, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? module.exports = factory() : typeof define === "function" && define.amd ? define(factory) : global2.chroma = factory();
    })(exports, function() {
      "use strict";
      var limit = function(x2, min2, max2) {
        if (min2 === void 0)
          min2 = 0;
        if (max2 === void 0)
          max2 = 1;
        return x2 < min2 ? min2 : x2 > max2 ? max2 : x2;
      };
      var clip_rgb = function(rgb) {
        rgb._clipped = false;
        rgb._unclipped = rgb.slice(0);
        for (var i3 = 0; i3 <= 3; i3++) {
          if (i3 < 3) {
            if (rgb[i3] < 0 || rgb[i3] > 255) {
              rgb._clipped = true;
            }
            rgb[i3] = limit(rgb[i3], 0, 255);
          } else if (i3 === 3) {
            rgb[i3] = limit(rgb[i3], 0, 1);
          }
        }
        return rgb;
      };
      var classToType = {};
      for (var i2 = 0, list = ["Boolean", "Number", "String", "Function", "Array", "Date", "RegExp", "Undefined", "Null"]; i2 < list.length; i2 += 1) {
        var name = list[i2];
        classToType["[object " + name + "]"] = name.toLowerCase();
      }
      var type = function(obj) {
        return classToType[Object.prototype.toString.call(obj)] || "object";
      };
      var unpack = function(args, keyOrder) {
        if (keyOrder === void 0)
          keyOrder = null;
        if (args.length >= 3) {
          return Array.prototype.slice.call(args);
        }
        if (type(args[0]) == "object" && keyOrder) {
          return keyOrder.split("").filter(function(k2) {
            return args[0][k2] !== void 0;
          }).map(function(k2) {
            return args[0][k2];
          });
        }
        return args[0];
      };
      var last = function(args) {
        if (args.length < 2) {
          return null;
        }
        var l2 = args.length - 1;
        if (type(args[l2]) == "string") {
          return args[l2].toLowerCase();
        }
        return null;
      };
      var PI = Math.PI;
      var utils = {
        clip_rgb,
        limit,
        type,
        unpack,
        last,
        PI,
        TWOPI: PI * 2,
        PITHIRD: PI / 3,
        DEG2RAD: PI / 180,
        RAD2DEG: 180 / PI
      };
      var input = {
        format: {},
        autodetect: []
      };
      var last$1 = utils.last;
      var clip_rgb$1 = utils.clip_rgb;
      var type$1 = utils.type;
      var Color = function Color2() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var me = this;
        if (type$1(args[0]) === "object" && args[0].constructor && args[0].constructor === this.constructor) {
          return args[0];
        }
        var mode = last$1(args);
        var autodetect = false;
        if (!mode) {
          autodetect = true;
          if (!input.sorted) {
            input.autodetect = input.autodetect.sort(function(a2, b2) {
              return b2.p - a2.p;
            });
            input.sorted = true;
          }
          for (var i3 = 0, list2 = input.autodetect; i3 < list2.length; i3 += 1) {
            var chk = list2[i3];
            mode = chk.test.apply(chk, args);
            if (mode) {
              break;
            }
          }
        }
        if (input.format[mode]) {
          var rgb = input.format[mode].apply(null, autodetect ? args : args.slice(0, -1));
          me._rgb = clip_rgb$1(rgb);
        } else {
          throw new Error("unknown format: " + args);
        }
        if (me._rgb.length === 3) {
          me._rgb.push(1);
        }
      };
      Color.prototype.toString = function toString() {
        if (type$1(this.hex) == "function") {
          return this.hex();
        }
        return "[" + this._rgb.join(",") + "]";
      };
      var Color_1 = Color;
      var chroma = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(chroma.Color, [null].concat(args)))();
      };
      chroma.Color = Color_1;
      chroma.version = "2.1.2";
      var chroma_1 = chroma;
      var unpack$1 = utils.unpack;
      var max = Math.max;
      var rgb2cmyk = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$1(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        r = r / 255;
        g2 = g2 / 255;
        b2 = b2 / 255;
        var k2 = 1 - max(r, max(g2, b2));
        var f = k2 < 1 ? 1 / (1 - k2) : 0;
        var c2 = (1 - r - k2) * f;
        var m2 = (1 - g2 - k2) * f;
        var y2 = (1 - b2 - k2) * f;
        return [c2, m2, y2, k2];
      };
      var rgb2cmyk_1 = rgb2cmyk;
      var unpack$2 = utils.unpack;
      var cmyk2rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$2(args, "cmyk");
        var c2 = args[0];
        var m2 = args[1];
        var y2 = args[2];
        var k2 = args[3];
        var alpha = args.length > 4 ? args[4] : 1;
        if (k2 === 1) {
          return [0, 0, 0, alpha];
        }
        return [
          c2 >= 1 ? 0 : 255 * (1 - c2) * (1 - k2),
          // r
          m2 >= 1 ? 0 : 255 * (1 - m2) * (1 - k2),
          // g
          y2 >= 1 ? 0 : 255 * (1 - y2) * (1 - k2),
          // b
          alpha
        ];
      };
      var cmyk2rgb_1 = cmyk2rgb;
      var unpack$3 = utils.unpack;
      var type$2 = utils.type;
      Color_1.prototype.cmyk = function() {
        return rgb2cmyk_1(this._rgb);
      };
      chroma_1.cmyk = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["cmyk"])))();
      };
      input.format.cmyk = cmyk2rgb_1;
      input.autodetect.push({
        p: 2,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$3(args, "cmyk");
          if (type$2(args) === "array" && args.length === 4) {
            return "cmyk";
          }
        }
      });
      var unpack$4 = utils.unpack;
      var last$2 = utils.last;
      var rnd = function(a2) {
        return Math.round(a2 * 100) / 100;
      };
      var hsl2css = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var hsla = unpack$4(args, "hsla");
        var mode = last$2(args) || "lsa";
        hsla[0] = rnd(hsla[0] || 0);
        hsla[1] = rnd(hsla[1] * 100) + "%";
        hsla[2] = rnd(hsla[2] * 100) + "%";
        if (mode === "hsla" || hsla.length > 3 && hsla[3] < 1) {
          hsla[3] = hsla.length > 3 ? hsla[3] : 1;
          mode = "hsla";
        } else {
          hsla.length = 3;
        }
        return mode + "(" + hsla.join(",") + ")";
      };
      var hsl2css_1 = hsl2css;
      var unpack$5 = utils.unpack;
      var rgb2hsl = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$5(args, "rgba");
        var r = args[0];
        var g2 = args[1];
        var b2 = args[2];
        r /= 255;
        g2 /= 255;
        b2 /= 255;
        var min2 = Math.min(r, g2, b2);
        var max2 = Math.max(r, g2, b2);
        var l2 = (max2 + min2) / 2;
        var s2, h2;
        if (max2 === min2) {
          s2 = 0;
          h2 = Number.NaN;
        } else {
          s2 = l2 < 0.5 ? (max2 - min2) / (max2 + min2) : (max2 - min2) / (2 - max2 - min2);
        }
        if (r == max2) {
          h2 = (g2 - b2) / (max2 - min2);
        } else if (g2 == max2) {
          h2 = 2 + (b2 - r) / (max2 - min2);
        } else if (b2 == max2) {
          h2 = 4 + (r - g2) / (max2 - min2);
        }
        h2 *= 60;
        if (h2 < 0) {
          h2 += 360;
        }
        if (args.length > 3 && args[3] !== void 0) {
          return [h2, s2, l2, args[3]];
        }
        return [h2, s2, l2];
      };
      var rgb2hsl_1 = rgb2hsl;
      var unpack$6 = utils.unpack;
      var last$3 = utils.last;
      var round = Math.round;
      var rgb2css = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var rgba = unpack$6(args, "rgba");
        var mode = last$3(args) || "rgb";
        if (mode.substr(0, 3) == "hsl") {
          return hsl2css_1(rgb2hsl_1(rgba), mode);
        }
        rgba[0] = round(rgba[0]);
        rgba[1] = round(rgba[1]);
        rgba[2] = round(rgba[2]);
        if (mode === "rgba" || rgba.length > 3 && rgba[3] < 1) {
          rgba[3] = rgba.length > 3 ? rgba[3] : 1;
          mode = "rgba";
        }
        return mode + "(" + rgba.slice(0, mode === "rgb" ? 3 : 4).join(",") + ")";
      };
      var rgb2css_1 = rgb2css;
      var unpack$7 = utils.unpack;
      var round$1 = Math.round;
      var hsl2rgb = function() {
        var assign;
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$7(args, "hsl");
        var h2 = args[0];
        var s2 = args[1];
        var l2 = args[2];
        var r, g2, b2;
        if (s2 === 0) {
          r = g2 = b2 = l2 * 255;
        } else {
          var t3 = [0, 0, 0];
          var c2 = [0, 0, 0];
          var t2 = l2 < 0.5 ? l2 * (1 + s2) : l2 + s2 - l2 * s2;
          var t1 = 2 * l2 - t2;
          var h_ = h2 / 360;
          t3[0] = h_ + 1 / 3;
          t3[1] = h_;
          t3[2] = h_ - 1 / 3;
          for (var i3 = 0; i3 < 3; i3++) {
            if (t3[i3] < 0) {
              t3[i3] += 1;
            }
            if (t3[i3] > 1) {
              t3[i3] -= 1;
            }
            if (6 * t3[i3] < 1) {
              c2[i3] = t1 + (t2 - t1) * 6 * t3[i3];
            } else if (2 * t3[i3] < 1) {
              c2[i3] = t2;
            } else if (3 * t3[i3] < 2) {
              c2[i3] = t1 + (t2 - t1) * (2 / 3 - t3[i3]) * 6;
            } else {
              c2[i3] = t1;
            }
          }
          assign = [round$1(c2[0] * 255), round$1(c2[1] * 255), round$1(c2[2] * 255)], r = assign[0], g2 = assign[1], b2 = assign[2];
        }
        if (args.length > 3) {
          return [r, g2, b2, args[3]];
        }
        return [r, g2, b2, 1];
      };
      var hsl2rgb_1 = hsl2rgb;
      var RE_RGB = /^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/;
      var RE_RGBA = /^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/;
      var RE_RGB_PCT = /^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/;
      var RE_RGBA_PCT = /^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/;
      var RE_HSL = /^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/;
      var RE_HSLA = /^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/;
      var round$2 = Math.round;
      var css2rgb = function(css) {
        css = css.toLowerCase().trim();
        var m2;
        if (input.format.named) {
          try {
            return input.format.named(css);
          } catch (e) {
          }
        }
        if (m2 = css.match(RE_RGB)) {
          var rgb = m2.slice(1, 4);
          for (var i3 = 0; i3 < 3; i3++) {
            rgb[i3] = +rgb[i3];
          }
          rgb[3] = 1;
          return rgb;
        }
        if (m2 = css.match(RE_RGBA)) {
          var rgb$12 = m2.slice(1, 5);
          for (var i$12 = 0; i$12 < 4; i$12++) {
            rgb$12[i$12] = +rgb$12[i$12];
          }
          return rgb$12;
        }
        if (m2 = css.match(RE_RGB_PCT)) {
          var rgb$2 = m2.slice(1, 4);
          for (var i$2 = 0; i$2 < 3; i$2++) {
            rgb$2[i$2] = round$2(rgb$2[i$2] * 2.55);
          }
          rgb$2[3] = 1;
          return rgb$2;
        }
        if (m2 = css.match(RE_RGBA_PCT)) {
          var rgb$3 = m2.slice(1, 5);
          for (var i$3 = 0; i$3 < 3; i$3++) {
            rgb$3[i$3] = round$2(rgb$3[i$3] * 2.55);
          }
          rgb$3[3] = +rgb$3[3];
          return rgb$3;
        }
        if (m2 = css.match(RE_HSL)) {
          var hsl = m2.slice(1, 4);
          hsl[1] *= 0.01;
          hsl[2] *= 0.01;
          var rgb$4 = hsl2rgb_1(hsl);
          rgb$4[3] = 1;
          return rgb$4;
        }
        if (m2 = css.match(RE_HSLA)) {
          var hsl$12 = m2.slice(1, 4);
          hsl$12[1] *= 0.01;
          hsl$12[2] *= 0.01;
          var rgb$5 = hsl2rgb_1(hsl$12);
          rgb$5[3] = +m2[4];
          return rgb$5;
        }
      };
      css2rgb.test = function(s2) {
        return RE_RGB.test(s2) || RE_RGBA.test(s2) || RE_RGB_PCT.test(s2) || RE_RGBA_PCT.test(s2) || RE_HSL.test(s2) || RE_HSLA.test(s2);
      };
      var css2rgb_1 = css2rgb;
      var type$3 = utils.type;
      Color_1.prototype.css = function(mode) {
        return rgb2css_1(this._rgb, mode);
      };
      chroma_1.css = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["css"])))();
      };
      input.format.css = css2rgb_1;
      input.autodetect.push({
        p: 5,
        test: function(h2) {
          var rest = [], len = arguments.length - 1;
          while (len-- > 0)
            rest[len] = arguments[len + 1];
          if (!rest.length && type$3(h2) === "string" && css2rgb_1.test(h2)) {
            return "css";
          }
        }
      });
      var unpack$8 = utils.unpack;
      input.format.gl = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var rgb = unpack$8(args, "rgba");
        rgb[0] *= 255;
        rgb[1] *= 255;
        rgb[2] *= 255;
        return rgb;
      };
      chroma_1.gl = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["gl"])))();
      };
      Color_1.prototype.gl = function() {
        var rgb = this._rgb;
        return [rgb[0] / 255, rgb[1] / 255, rgb[2] / 255, rgb[3]];
      };
      var unpack$9 = utils.unpack;
      var rgb2hcg = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$9(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        var min2 = Math.min(r, g2, b2);
        var max2 = Math.max(r, g2, b2);
        var delta = max2 - min2;
        var c2 = delta * 100 / 255;
        var _g = min2 / (255 - delta) * 100;
        var h2;
        if (delta === 0) {
          h2 = Number.NaN;
        } else {
          if (r === max2) {
            h2 = (g2 - b2) / delta;
          }
          if (g2 === max2) {
            h2 = 2 + (b2 - r) / delta;
          }
          if (b2 === max2) {
            h2 = 4 + (r - g2) / delta;
          }
          h2 *= 60;
          if (h2 < 0) {
            h2 += 360;
          }
        }
        return [h2, c2, _g];
      };
      var rgb2hcg_1 = rgb2hcg;
      var unpack$a = utils.unpack;
      var floor = Math.floor;
      var hcg2rgb = function() {
        var assign, assign$1, assign$2, assign$3, assign$4, assign$5;
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$a(args, "hcg");
        var h2 = args[0];
        var c2 = args[1];
        var _g = args[2];
        var r, g2, b2;
        _g = _g * 255;
        var _c = c2 * 255;
        if (c2 === 0) {
          r = g2 = b2 = _g;
        } else {
          if (h2 === 360) {
            h2 = 0;
          }
          if (h2 > 360) {
            h2 -= 360;
          }
          if (h2 < 0) {
            h2 += 360;
          }
          h2 /= 60;
          var i3 = floor(h2);
          var f = h2 - i3;
          var p2 = _g * (1 - c2);
          var q2 = p2 + _c * (1 - f);
          var t = p2 + _c * f;
          var v2 = p2 + _c;
          switch (i3) {
            case 0:
              assign = [v2, t, p2], r = assign[0], g2 = assign[1], b2 = assign[2];
              break;
            case 1:
              assign$1 = [q2, v2, p2], r = assign$1[0], g2 = assign$1[1], b2 = assign$1[2];
              break;
            case 2:
              assign$2 = [p2, v2, t], r = assign$2[0], g2 = assign$2[1], b2 = assign$2[2];
              break;
            case 3:
              assign$3 = [p2, q2, v2], r = assign$3[0], g2 = assign$3[1], b2 = assign$3[2];
              break;
            case 4:
              assign$4 = [t, p2, v2], r = assign$4[0], g2 = assign$4[1], b2 = assign$4[2];
              break;
            case 5:
              assign$5 = [v2, p2, q2], r = assign$5[0], g2 = assign$5[1], b2 = assign$5[2];
              break;
          }
        }
        return [r, g2, b2, args.length > 3 ? args[3] : 1];
      };
      var hcg2rgb_1 = hcg2rgb;
      var unpack$b = utils.unpack;
      var type$4 = utils.type;
      Color_1.prototype.hcg = function() {
        return rgb2hcg_1(this._rgb);
      };
      chroma_1.hcg = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hcg"])))();
      };
      input.format.hcg = hcg2rgb_1;
      input.autodetect.push({
        p: 1,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$b(args, "hcg");
          if (type$4(args) === "array" && args.length === 3) {
            return "hcg";
          }
        }
      });
      var unpack$c = utils.unpack;
      var last$4 = utils.last;
      var round$3 = Math.round;
      var rgb2hex = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$c(args, "rgba");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        var a2 = ref[3];
        var mode = last$4(args) || "auto";
        if (a2 === void 0) {
          a2 = 1;
        }
        if (mode === "auto") {
          mode = a2 < 1 ? "rgba" : "rgb";
        }
        r = round$3(r);
        g2 = round$3(g2);
        b2 = round$3(b2);
        var u2 = r << 16 | g2 << 8 | b2;
        var str = "000000" + u2.toString(16);
        str = str.substr(str.length - 6);
        var hxa = "0" + round$3(a2 * 255).toString(16);
        hxa = hxa.substr(hxa.length - 2);
        switch (mode.toLowerCase()) {
          case "rgba":
            return "#" + str + hxa;
          case "argb":
            return "#" + hxa + str;
          default:
            return "#" + str;
        }
      };
      var rgb2hex_1 = rgb2hex;
      var RE_HEX = /^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      var RE_HEXA = /^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/;
      var hex2rgb = function(hex) {
        if (hex.match(RE_HEX)) {
          if (hex.length === 4 || hex.length === 7) {
            hex = hex.substr(1);
          }
          if (hex.length === 3) {
            hex = hex.split("");
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
          }
          var u2 = parseInt(hex, 16);
          var r = u2 >> 16;
          var g2 = u2 >> 8 & 255;
          var b2 = u2 & 255;
          return [r, g2, b2, 1];
        }
        if (hex.match(RE_HEXA)) {
          if (hex.length === 5 || hex.length === 9) {
            hex = hex.substr(1);
          }
          if (hex.length === 4) {
            hex = hex.split("");
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2] + hex[3] + hex[3];
          }
          var u$1 = parseInt(hex, 16);
          var r$1 = u$1 >> 24 & 255;
          var g$1 = u$1 >> 16 & 255;
          var b$1 = u$1 >> 8 & 255;
          var a2 = Math.round((u$1 & 255) / 255 * 100) / 100;
          return [r$1, g$1, b$1, a2];
        }
        throw new Error("unknown hex color: " + hex);
      };
      var hex2rgb_1 = hex2rgb;
      var type$5 = utils.type;
      Color_1.prototype.hex = function(mode) {
        return rgb2hex_1(this._rgb, mode);
      };
      chroma_1.hex = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hex"])))();
      };
      input.format.hex = hex2rgb_1;
      input.autodetect.push({
        p: 4,
        test: function(h2) {
          var rest = [], len = arguments.length - 1;
          while (len-- > 0)
            rest[len] = arguments[len + 1];
          if (!rest.length && type$5(h2) === "string" && [3, 4, 5, 6, 7, 8, 9].indexOf(h2.length) >= 0) {
            return "hex";
          }
        }
      });
      var unpack$d = utils.unpack;
      var TWOPI = utils.TWOPI;
      var min = Math.min;
      var sqrt = Math.sqrt;
      var acos = Math.acos;
      var rgb2hsi = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$d(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        r /= 255;
        g2 /= 255;
        b2 /= 255;
        var h2;
        var min_ = min(r, g2, b2);
        var i3 = (r + g2 + b2) / 3;
        var s2 = i3 > 0 ? 1 - min_ / i3 : 0;
        if (s2 === 0) {
          h2 = NaN;
        } else {
          h2 = (r - g2 + (r - b2)) / 2;
          h2 /= sqrt((r - g2) * (r - g2) + (r - b2) * (g2 - b2));
          h2 = acos(h2);
          if (b2 > g2) {
            h2 = TWOPI - h2;
          }
          h2 /= TWOPI;
        }
        return [h2 * 360, s2, i3];
      };
      var rgb2hsi_1 = rgb2hsi;
      var unpack$e = utils.unpack;
      var limit$1 = utils.limit;
      var TWOPI$1 = utils.TWOPI;
      var PITHIRD = utils.PITHIRD;
      var cos = Math.cos;
      var hsi2rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$e(args, "hsi");
        var h2 = args[0];
        var s2 = args[1];
        var i3 = args[2];
        var r, g2, b2;
        if (isNaN(h2)) {
          h2 = 0;
        }
        if (isNaN(s2)) {
          s2 = 0;
        }
        if (h2 > 360) {
          h2 -= 360;
        }
        if (h2 < 0) {
          h2 += 360;
        }
        h2 /= 360;
        if (h2 < 1 / 3) {
          b2 = (1 - s2) / 3;
          r = (1 + s2 * cos(TWOPI$1 * h2) / cos(PITHIRD - TWOPI$1 * h2)) / 3;
          g2 = 1 - (b2 + r);
        } else if (h2 < 2 / 3) {
          h2 -= 1 / 3;
          r = (1 - s2) / 3;
          g2 = (1 + s2 * cos(TWOPI$1 * h2) / cos(PITHIRD - TWOPI$1 * h2)) / 3;
          b2 = 1 - (r + g2);
        } else {
          h2 -= 2 / 3;
          g2 = (1 - s2) / 3;
          b2 = (1 + s2 * cos(TWOPI$1 * h2) / cos(PITHIRD - TWOPI$1 * h2)) / 3;
          r = 1 - (g2 + b2);
        }
        r = limit$1(i3 * r * 3);
        g2 = limit$1(i3 * g2 * 3);
        b2 = limit$1(i3 * b2 * 3);
        return [r * 255, g2 * 255, b2 * 255, args.length > 3 ? args[3] : 1];
      };
      var hsi2rgb_1 = hsi2rgb;
      var unpack$f = utils.unpack;
      var type$6 = utils.type;
      Color_1.prototype.hsi = function() {
        return rgb2hsi_1(this._rgb);
      };
      chroma_1.hsi = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hsi"])))();
      };
      input.format.hsi = hsi2rgb_1;
      input.autodetect.push({
        p: 2,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$f(args, "hsi");
          if (type$6(args) === "array" && args.length === 3) {
            return "hsi";
          }
        }
      });
      var unpack$g = utils.unpack;
      var type$7 = utils.type;
      Color_1.prototype.hsl = function() {
        return rgb2hsl_1(this._rgb);
      };
      chroma_1.hsl = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hsl"])))();
      };
      input.format.hsl = hsl2rgb_1;
      input.autodetect.push({
        p: 2,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$g(args, "hsl");
          if (type$7(args) === "array" && args.length === 3) {
            return "hsl";
          }
        }
      });
      var unpack$h = utils.unpack;
      var min$1 = Math.min;
      var max$1 = Math.max;
      var rgb2hsl$1 = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$h(args, "rgb");
        var r = args[0];
        var g2 = args[1];
        var b2 = args[2];
        var min_ = min$1(r, g2, b2);
        var max_ = max$1(r, g2, b2);
        var delta = max_ - min_;
        var h2, s2, v2;
        v2 = max_ / 255;
        if (max_ === 0) {
          h2 = Number.NaN;
          s2 = 0;
        } else {
          s2 = delta / max_;
          if (r === max_) {
            h2 = (g2 - b2) / delta;
          }
          if (g2 === max_) {
            h2 = 2 + (b2 - r) / delta;
          }
          if (b2 === max_) {
            h2 = 4 + (r - g2) / delta;
          }
          h2 *= 60;
          if (h2 < 0) {
            h2 += 360;
          }
        }
        return [h2, s2, v2];
      };
      var rgb2hsv = rgb2hsl$1;
      var unpack$i = utils.unpack;
      var floor$1 = Math.floor;
      var hsv2rgb = function() {
        var assign, assign$1, assign$2, assign$3, assign$4, assign$5;
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$i(args, "hsv");
        var h2 = args[0];
        var s2 = args[1];
        var v2 = args[2];
        var r, g2, b2;
        v2 *= 255;
        if (s2 === 0) {
          r = g2 = b2 = v2;
        } else {
          if (h2 === 360) {
            h2 = 0;
          }
          if (h2 > 360) {
            h2 -= 360;
          }
          if (h2 < 0) {
            h2 += 360;
          }
          h2 /= 60;
          var i3 = floor$1(h2);
          var f = h2 - i3;
          var p2 = v2 * (1 - s2);
          var q2 = v2 * (1 - s2 * f);
          var t = v2 * (1 - s2 * (1 - f));
          switch (i3) {
            case 0:
              assign = [v2, t, p2], r = assign[0], g2 = assign[1], b2 = assign[2];
              break;
            case 1:
              assign$1 = [q2, v2, p2], r = assign$1[0], g2 = assign$1[1], b2 = assign$1[2];
              break;
            case 2:
              assign$2 = [p2, v2, t], r = assign$2[0], g2 = assign$2[1], b2 = assign$2[2];
              break;
            case 3:
              assign$3 = [p2, q2, v2], r = assign$3[0], g2 = assign$3[1], b2 = assign$3[2];
              break;
            case 4:
              assign$4 = [t, p2, v2], r = assign$4[0], g2 = assign$4[1], b2 = assign$4[2];
              break;
            case 5:
              assign$5 = [v2, p2, q2], r = assign$5[0], g2 = assign$5[1], b2 = assign$5[2];
              break;
          }
        }
        return [r, g2, b2, args.length > 3 ? args[3] : 1];
      };
      var hsv2rgb_1 = hsv2rgb;
      var unpack$j = utils.unpack;
      var type$8 = utils.type;
      Color_1.prototype.hsv = function() {
        return rgb2hsv(this._rgb);
      };
      chroma_1.hsv = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hsv"])))();
      };
      input.format.hsv = hsv2rgb_1;
      input.autodetect.push({
        p: 2,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$j(args, "hsv");
          if (type$8(args) === "array" && args.length === 3) {
            return "hsv";
          }
        }
      });
      var labConstants = {
        // Corresponds roughly to RGB brighter/darker
        Kn: 18,
        // D65 standard referent
        Xn: 0.95047,
        Yn: 1,
        Zn: 1.08883,
        t0: 0.137931034,
        // 4 / 29
        t1: 0.206896552,
        // 6 / 29
        t2: 0.12841855,
        // 3 * t1 * t1
        t3: 8856452e-9
        // t1 * t1 * t1
      };
      var unpack$k = utils.unpack;
      var pow = Math.pow;
      var rgb2lab = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$k(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        var ref$1 = rgb2xyz(r, g2, b2);
        var x2 = ref$1[0];
        var y2 = ref$1[1];
        var z2 = ref$1[2];
        var l2 = 116 * y2 - 16;
        return [l2 < 0 ? 0 : l2, 500 * (x2 - y2), 200 * (y2 - z2)];
      };
      var rgb_xyz = function(r) {
        if ((r /= 255) <= 0.04045) {
          return r / 12.92;
        }
        return pow((r + 0.055) / 1.055, 2.4);
      };
      var xyz_lab = function(t) {
        if (t > labConstants.t3) {
          return pow(t, 1 / 3);
        }
        return t / labConstants.t2 + labConstants.t0;
      };
      var rgb2xyz = function(r, g2, b2) {
        r = rgb_xyz(r);
        g2 = rgb_xyz(g2);
        b2 = rgb_xyz(b2);
        var x2 = xyz_lab((0.4124564 * r + 0.3575761 * g2 + 0.1804375 * b2) / labConstants.Xn);
        var y2 = xyz_lab((0.2126729 * r + 0.7151522 * g2 + 0.072175 * b2) / labConstants.Yn);
        var z2 = xyz_lab((0.0193339 * r + 0.119192 * g2 + 0.9503041 * b2) / labConstants.Zn);
        return [x2, y2, z2];
      };
      var rgb2lab_1 = rgb2lab;
      var unpack$l = utils.unpack;
      var pow$1 = Math.pow;
      var lab2rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$l(args, "lab");
        var l2 = args[0];
        var a2 = args[1];
        var b2 = args[2];
        var x2, y2, z2, r, g2, b_;
        y2 = (l2 + 16) / 116;
        x2 = isNaN(a2) ? y2 : y2 + a2 / 500;
        z2 = isNaN(b2) ? y2 : y2 - b2 / 200;
        y2 = labConstants.Yn * lab_xyz(y2);
        x2 = labConstants.Xn * lab_xyz(x2);
        z2 = labConstants.Zn * lab_xyz(z2);
        r = xyz_rgb(3.2404542 * x2 - 1.5371385 * y2 - 0.4985314 * z2);
        g2 = xyz_rgb(-0.969266 * x2 + 1.8760108 * y2 + 0.041556 * z2);
        b_ = xyz_rgb(0.0556434 * x2 - 0.2040259 * y2 + 1.0572252 * z2);
        return [r, g2, b_, args.length > 3 ? args[3] : 1];
      };
      var xyz_rgb = function(r) {
        return 255 * (r <= 304e-5 ? 12.92 * r : 1.055 * pow$1(r, 1 / 2.4) - 0.055);
      };
      var lab_xyz = function(t) {
        return t > labConstants.t1 ? t * t * t : labConstants.t2 * (t - labConstants.t0);
      };
      var lab2rgb_1 = lab2rgb;
      var unpack$m = utils.unpack;
      var type$9 = utils.type;
      Color_1.prototype.lab = function() {
        return rgb2lab_1(this._rgb);
      };
      chroma_1.lab = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["lab"])))();
      };
      input.format.lab = lab2rgb_1;
      input.autodetect.push({
        p: 2,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$m(args, "lab");
          if (type$9(args) === "array" && args.length === 3) {
            return "lab";
          }
        }
      });
      var unpack$n = utils.unpack;
      var RAD2DEG = utils.RAD2DEG;
      var sqrt$1 = Math.sqrt;
      var atan2 = Math.atan2;
      var round$4 = Math.round;
      var lab2lch = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$n(args, "lab");
        var l2 = ref[0];
        var a2 = ref[1];
        var b2 = ref[2];
        var c2 = sqrt$1(a2 * a2 + b2 * b2);
        var h2 = (atan2(b2, a2) * RAD2DEG + 360) % 360;
        if (round$4(c2 * 1e4) === 0) {
          h2 = Number.NaN;
        }
        return [l2, c2, h2];
      };
      var lab2lch_1 = lab2lch;
      var unpack$o = utils.unpack;
      var rgb2lch = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$o(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        var ref$1 = rgb2lab_1(r, g2, b2);
        var l2 = ref$1[0];
        var a2 = ref$1[1];
        var b_ = ref$1[2];
        return lab2lch_1(l2, a2, b_);
      };
      var rgb2lch_1 = rgb2lch;
      var unpack$p = utils.unpack;
      var DEG2RAD = utils.DEG2RAD;
      var sin = Math.sin;
      var cos$1 = Math.cos;
      var lch2lab = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$p(args, "lch");
        var l2 = ref[0];
        var c2 = ref[1];
        var h2 = ref[2];
        if (isNaN(h2)) {
          h2 = 0;
        }
        h2 = h2 * DEG2RAD;
        return [l2, cos$1(h2) * c2, sin(h2) * c2];
      };
      var lch2lab_1 = lch2lab;
      var unpack$q = utils.unpack;
      var lch2rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        args = unpack$q(args, "lch");
        var l2 = args[0];
        var c2 = args[1];
        var h2 = args[2];
        var ref = lch2lab_1(l2, c2, h2);
        var L2 = ref[0];
        var a2 = ref[1];
        var b_ = ref[2];
        var ref$1 = lab2rgb_1(L2, a2, b_);
        var r = ref$1[0];
        var g2 = ref$1[1];
        var b2 = ref$1[2];
        return [r, g2, b2, args.length > 3 ? args[3] : 1];
      };
      var lch2rgb_1 = lch2rgb;
      var unpack$r = utils.unpack;
      var hcl2rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var hcl = unpack$r(args, "hcl").reverse();
        return lch2rgb_1.apply(void 0, hcl);
      };
      var hcl2rgb_1 = hcl2rgb;
      var unpack$s = utils.unpack;
      var type$a = utils.type;
      Color_1.prototype.lch = function() {
        return rgb2lch_1(this._rgb);
      };
      Color_1.prototype.hcl = function() {
        return rgb2lch_1(this._rgb).reverse();
      };
      chroma_1.lch = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["lch"])))();
      };
      chroma_1.hcl = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["hcl"])))();
      };
      input.format.lch = lch2rgb_1;
      input.format.hcl = hcl2rgb_1;
      ["lch", "hcl"].forEach(function(m2) {
        return input.autodetect.push({
          p: 2,
          test: function() {
            var args = [], len = arguments.length;
            while (len--)
              args[len] = arguments[len];
            args = unpack$s(args, m2);
            if (type$a(args) === "array" && args.length === 3) {
              return m2;
            }
          }
        });
      });
      var w3cx11 = {
        aliceblue: "#f0f8ff",
        antiquewhite: "#faebd7",
        aqua: "#00ffff",
        aquamarine: "#7fffd4",
        azure: "#f0ffff",
        beige: "#f5f5dc",
        bisque: "#ffe4c4",
        black: "#000000",
        blanchedalmond: "#ffebcd",
        blue: "#0000ff",
        blueviolet: "#8a2be2",
        brown: "#a52a2a",
        burlywood: "#deb887",
        cadetblue: "#5f9ea0",
        chartreuse: "#7fff00",
        chocolate: "#d2691e",
        coral: "#ff7f50",
        cornflower: "#6495ed",
        cornflowerblue: "#6495ed",
        cornsilk: "#fff8dc",
        crimson: "#dc143c",
        cyan: "#00ffff",
        darkblue: "#00008b",
        darkcyan: "#008b8b",
        darkgoldenrod: "#b8860b",
        darkgray: "#a9a9a9",
        darkgreen: "#006400",
        darkgrey: "#a9a9a9",
        darkkhaki: "#bdb76b",
        darkmagenta: "#8b008b",
        darkolivegreen: "#556b2f",
        darkorange: "#ff8c00",
        darkorchid: "#9932cc",
        darkred: "#8b0000",
        darksalmon: "#e9967a",
        darkseagreen: "#8fbc8f",
        darkslateblue: "#483d8b",
        darkslategray: "#2f4f4f",
        darkslategrey: "#2f4f4f",
        darkturquoise: "#00ced1",
        darkviolet: "#9400d3",
        deeppink: "#ff1493",
        deepskyblue: "#00bfff",
        dimgray: "#696969",
        dimgrey: "#696969",
        dodgerblue: "#1e90ff",
        firebrick: "#b22222",
        floralwhite: "#fffaf0",
        forestgreen: "#228b22",
        fuchsia: "#ff00ff",
        gainsboro: "#dcdcdc",
        ghostwhite: "#f8f8ff",
        gold: "#ffd700",
        goldenrod: "#daa520",
        gray: "#808080",
        green: "#008000",
        greenyellow: "#adff2f",
        grey: "#808080",
        honeydew: "#f0fff0",
        hotpink: "#ff69b4",
        indianred: "#cd5c5c",
        indigo: "#4b0082",
        ivory: "#fffff0",
        khaki: "#f0e68c",
        laserlemon: "#ffff54",
        lavender: "#e6e6fa",
        lavenderblush: "#fff0f5",
        lawngreen: "#7cfc00",
        lemonchiffon: "#fffacd",
        lightblue: "#add8e6",
        lightcoral: "#f08080",
        lightcyan: "#e0ffff",
        lightgoldenrod: "#fafad2",
        lightgoldenrodyellow: "#fafad2",
        lightgray: "#d3d3d3",
        lightgreen: "#90ee90",
        lightgrey: "#d3d3d3",
        lightpink: "#ffb6c1",
        lightsalmon: "#ffa07a",
        lightseagreen: "#20b2aa",
        lightskyblue: "#87cefa",
        lightslategray: "#778899",
        lightslategrey: "#778899",
        lightsteelblue: "#b0c4de",
        lightyellow: "#ffffe0",
        lime: "#00ff00",
        limegreen: "#32cd32",
        linen: "#faf0e6",
        magenta: "#ff00ff",
        maroon: "#800000",
        maroon2: "#7f0000",
        maroon3: "#b03060",
        mediumaquamarine: "#66cdaa",
        mediumblue: "#0000cd",
        mediumorchid: "#ba55d3",
        mediumpurple: "#9370db",
        mediumseagreen: "#3cb371",
        mediumslateblue: "#7b68ee",
        mediumspringgreen: "#00fa9a",
        mediumturquoise: "#48d1cc",
        mediumvioletred: "#c71585",
        midnightblue: "#191970",
        mintcream: "#f5fffa",
        mistyrose: "#ffe4e1",
        moccasin: "#ffe4b5",
        navajowhite: "#ffdead",
        navy: "#000080",
        oldlace: "#fdf5e6",
        olive: "#808000",
        olivedrab: "#6b8e23",
        orange: "#ffa500",
        orangered: "#ff4500",
        orchid: "#da70d6",
        palegoldenrod: "#eee8aa",
        palegreen: "#98fb98",
        paleturquoise: "#afeeee",
        palevioletred: "#db7093",
        papayawhip: "#ffefd5",
        peachpuff: "#ffdab9",
        peru: "#cd853f",
        pink: "#ffc0cb",
        plum: "#dda0dd",
        powderblue: "#b0e0e6",
        purple: "#800080",
        purple2: "#7f007f",
        purple3: "#a020f0",
        rebeccapurple: "#663399",
        red: "#ff0000",
        rosybrown: "#bc8f8f",
        royalblue: "#4169e1",
        saddlebrown: "#8b4513",
        salmon: "#fa8072",
        sandybrown: "#f4a460",
        seagreen: "#2e8b57",
        seashell: "#fff5ee",
        sienna: "#a0522d",
        silver: "#c0c0c0",
        skyblue: "#87ceeb",
        slateblue: "#6a5acd",
        slategray: "#708090",
        slategrey: "#708090",
        snow: "#fffafa",
        springgreen: "#00ff7f",
        steelblue: "#4682b4",
        tan: "#d2b48c",
        teal: "#008080",
        thistle: "#d8bfd8",
        tomato: "#ff6347",
        turquoise: "#40e0d0",
        violet: "#ee82ee",
        wheat: "#f5deb3",
        white: "#ffffff",
        whitesmoke: "#f5f5f5",
        yellow: "#ffff00",
        yellowgreen: "#9acd32"
      };
      var w3cx11_1 = w3cx11;
      var type$b = utils.type;
      Color_1.prototype.name = function() {
        var hex = rgb2hex_1(this._rgb, "rgb");
        for (var i3 = 0, list2 = Object.keys(w3cx11_1); i3 < list2.length; i3 += 1) {
          var n = list2[i3];
          if (w3cx11_1[n] === hex) {
            return n.toLowerCase();
          }
        }
        return hex;
      };
      input.format.named = function(name2) {
        name2 = name2.toLowerCase();
        if (w3cx11_1[name2]) {
          return hex2rgb_1(w3cx11_1[name2]);
        }
        throw new Error("unknown color name: " + name2);
      };
      input.autodetect.push({
        p: 5,
        test: function(h2) {
          var rest = [], len = arguments.length - 1;
          while (len-- > 0)
            rest[len] = arguments[len + 1];
          if (!rest.length && type$b(h2) === "string" && w3cx11_1[h2.toLowerCase()]) {
            return "named";
          }
        }
      });
      var unpack$t = utils.unpack;
      var rgb2num = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var ref = unpack$t(args, "rgb");
        var r = ref[0];
        var g2 = ref[1];
        var b2 = ref[2];
        return (r << 16) + (g2 << 8) + b2;
      };
      var rgb2num_1 = rgb2num;
      var type$c = utils.type;
      var num2rgb = function(num) {
        if (type$c(num) == "number" && num >= 0 && num <= 16777215) {
          var r = num >> 16;
          var g2 = num >> 8 & 255;
          var b2 = num & 255;
          return [r, g2, b2, 1];
        }
        throw new Error("unknown num color: " + num);
      };
      var num2rgb_1 = num2rgb;
      var type$d = utils.type;
      Color_1.prototype.num = function() {
        return rgb2num_1(this._rgb);
      };
      chroma_1.num = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["num"])))();
      };
      input.format.num = num2rgb_1;
      input.autodetect.push({
        p: 5,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          if (args.length === 1 && type$d(args[0]) === "number" && args[0] >= 0 && args[0] <= 16777215) {
            return "num";
          }
        }
      });
      var unpack$u = utils.unpack;
      var type$e = utils.type;
      var round$5 = Math.round;
      Color_1.prototype.rgb = function(rnd2) {
        if (rnd2 === void 0)
          rnd2 = true;
        if (rnd2 === false) {
          return this._rgb.slice(0, 3);
        }
        return this._rgb.slice(0, 3).map(round$5);
      };
      Color_1.prototype.rgba = function(rnd2) {
        if (rnd2 === void 0)
          rnd2 = true;
        return this._rgb.slice(0, 4).map(function(v2, i3) {
          return i3 < 3 ? rnd2 === false ? v2 : round$5(v2) : v2;
        });
      };
      chroma_1.rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["rgb"])))();
      };
      input.format.rgb = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var rgba = unpack$u(args, "rgba");
        if (rgba[3] === void 0) {
          rgba[3] = 1;
        }
        return rgba;
      };
      input.autodetect.push({
        p: 3,
        test: function() {
          var args = [], len = arguments.length;
          while (len--)
            args[len] = arguments[len];
          args = unpack$u(args, "rgba");
          if (type$e(args) === "array" && (args.length === 3 || args.length === 4 && type$e(args[3]) == "number" && args[3] >= 0 && args[3] <= 1)) {
            return "rgb";
          }
        }
      });
      var log = Math.log;
      var temperature2rgb = function(kelvin) {
        var temp = kelvin / 100;
        var r, g2, b2;
        if (temp < 66) {
          r = 255;
          g2 = -155.25485562709179 - 0.44596950469579133 * (g2 = temp - 2) + 104.49216199393888 * log(g2);
          b2 = temp < 20 ? 0 : -254.76935184120902 + 0.8274096064007395 * (b2 = temp - 10) + 115.67994401066147 * log(b2);
        } else {
          r = 351.97690566805693 + 0.114206453784165 * (r = temp - 55) - 40.25366309332127 * log(r);
          g2 = 325.4494125711974 + 0.07943456536662342 * (g2 = temp - 50) - 28.0852963507957 * log(g2);
          b2 = 255;
        }
        return [r, g2, b2, 1];
      };
      var temperature2rgb_1 = temperature2rgb;
      var unpack$v = utils.unpack;
      var round$6 = Math.round;
      var rgb2temperature = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        var rgb = unpack$v(args, "rgb");
        var r = rgb[0], b2 = rgb[2];
        var minTemp = 1e3;
        var maxTemp = 4e4;
        var eps = 0.4;
        var temp;
        while (maxTemp - minTemp > eps) {
          temp = (maxTemp + minTemp) * 0.5;
          var rgb$12 = temperature2rgb_1(temp);
          if (rgb$12[2] / rgb$12[0] >= b2 / r) {
            maxTemp = temp;
          } else {
            minTemp = temp;
          }
        }
        return round$6(temp);
      };
      var rgb2temperature_1 = rgb2temperature;
      Color_1.prototype.temp = Color_1.prototype.kelvin = Color_1.prototype.temperature = function() {
        return rgb2temperature_1(this._rgb);
      };
      chroma_1.temp = chroma_1.kelvin = chroma_1.temperature = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        return new (Function.prototype.bind.apply(Color_1, [null].concat(args, ["temp"])))();
      };
      input.format.temp = input.format.kelvin = input.format.temperature = temperature2rgb_1;
      var type$f = utils.type;
      Color_1.prototype.alpha = function(a2, mutate) {
        if (mutate === void 0)
          mutate = false;
        if (a2 !== void 0 && type$f(a2) === "number") {
          if (mutate) {
            this._rgb[3] = a2;
            return this;
          }
          return new Color_1([this._rgb[0], this._rgb[1], this._rgb[2], a2], "rgb");
        }
        return this._rgb[3];
      };
      Color_1.prototype.clipped = function() {
        return this._rgb._clipped || false;
      };
      Color_1.prototype.darken = function(amount) {
        if (amount === void 0)
          amount = 1;
        var me = this;
        var lab = me.lab();
        lab[0] -= labConstants.Kn * amount;
        return new Color_1(lab, "lab").alpha(me.alpha(), true);
      };
      Color_1.prototype.brighten = function(amount) {
        if (amount === void 0)
          amount = 1;
        return this.darken(-amount);
      };
      Color_1.prototype.darker = Color_1.prototype.darken;
      Color_1.prototype.brighter = Color_1.prototype.brighten;
      Color_1.prototype.get = function(mc) {
        var ref = mc.split(".");
        var mode = ref[0];
        var channel = ref[1];
        var src = this[mode]();
        if (channel) {
          var i3 = mode.indexOf(channel);
          if (i3 > -1) {
            return src[i3];
          }
          throw new Error("unknown channel " + channel + " in mode " + mode);
        } else {
          return src;
        }
      };
      var type$g = utils.type;
      var pow$2 = Math.pow;
      var EPS = 1e-7;
      var MAX_ITER = 20;
      Color_1.prototype.luminance = function(lum) {
        if (lum !== void 0 && type$g(lum) === "number") {
          if (lum === 0) {
            return new Color_1([0, 0, 0, this._rgb[3]], "rgb");
          }
          if (lum === 1) {
            return new Color_1([255, 255, 255, this._rgb[3]], "rgb");
          }
          var cur_lum = this.luminance();
          var mode = "rgb";
          var max_iter = MAX_ITER;
          var test = function(low, high) {
            var mid = low.interpolate(high, 0.5, mode);
            var lm = mid.luminance();
            if (Math.abs(lum - lm) < EPS || !max_iter--) {
              return mid;
            }
            return lm > lum ? test(low, mid) : test(mid, high);
          };
          var rgb = (cur_lum > lum ? test(new Color_1([0, 0, 0]), this) : test(this, new Color_1([255, 255, 255]))).rgb();
          return new Color_1(rgb.concat([this._rgb[3]]));
        }
        return rgb2luminance.apply(void 0, this._rgb.slice(0, 3));
      };
      var rgb2luminance = function(r, g2, b2) {
        r = luminance_x(r);
        g2 = luminance_x(g2);
        b2 = luminance_x(b2);
        return 0.2126 * r + 0.7152 * g2 + 0.0722 * b2;
      };
      var luminance_x = function(x2) {
        x2 /= 255;
        return x2 <= 0.03928 ? x2 / 12.92 : pow$2((x2 + 0.055) / 1.055, 2.4);
      };
      var interpolator = {};
      var type$h = utils.type;
      var mix = function(col1, col2, f) {
        if (f === void 0)
          f = 0.5;
        var rest = [], len = arguments.length - 3;
        while (len-- > 0)
          rest[len] = arguments[len + 3];
        var mode = rest[0] || "lrgb";
        if (!interpolator[mode] && !rest.length) {
          mode = Object.keys(interpolator)[0];
        }
        if (!interpolator[mode]) {
          throw new Error("interpolation mode " + mode + " is not defined");
        }
        if (type$h(col1) !== "object") {
          col1 = new Color_1(col1);
        }
        if (type$h(col2) !== "object") {
          col2 = new Color_1(col2);
        }
        return interpolator[mode](col1, col2, f).alpha(col1.alpha() + f * (col2.alpha() - col1.alpha()));
      };
      Color_1.prototype.mix = Color_1.prototype.interpolate = function(col2, f) {
        if (f === void 0)
          f = 0.5;
        var rest = [], len = arguments.length - 2;
        while (len-- > 0)
          rest[len] = arguments[len + 2];
        return mix.apply(void 0, [this, col2, f].concat(rest));
      };
      Color_1.prototype.premultiply = function(mutate) {
        if (mutate === void 0)
          mutate = false;
        var rgb = this._rgb;
        var a2 = rgb[3];
        if (mutate) {
          this._rgb = [rgb[0] * a2, rgb[1] * a2, rgb[2] * a2, a2];
          return this;
        } else {
          return new Color_1([rgb[0] * a2, rgb[1] * a2, rgb[2] * a2, a2], "rgb");
        }
      };
      Color_1.prototype.saturate = function(amount) {
        if (amount === void 0)
          amount = 1;
        var me = this;
        var lch = me.lch();
        lch[1] += labConstants.Kn * amount;
        if (lch[1] < 0) {
          lch[1] = 0;
        }
        return new Color_1(lch, "lch").alpha(me.alpha(), true);
      };
      Color_1.prototype.desaturate = function(amount) {
        if (amount === void 0)
          amount = 1;
        return this.saturate(-amount);
      };
      var type$i = utils.type;
      Color_1.prototype.set = function(mc, value, mutate) {
        if (mutate === void 0)
          mutate = false;
        var ref = mc.split(".");
        var mode = ref[0];
        var channel = ref[1];
        var src = this[mode]();
        if (channel) {
          var i3 = mode.indexOf(channel);
          if (i3 > -1) {
            if (type$i(value) == "string") {
              switch (value.charAt(0)) {
                case "+":
                  src[i3] += +value;
                  break;
                case "-":
                  src[i3] += +value;
                  break;
                case "*":
                  src[i3] *= +value.substr(1);
                  break;
                case "/":
                  src[i3] /= +value.substr(1);
                  break;
                default:
                  src[i3] = +value;
              }
            } else if (type$i(value) === "number") {
              src[i3] = value;
            } else {
              throw new Error("unsupported value for Color.set");
            }
            var out = new Color_1(src, mode);
            if (mutate) {
              this._rgb = out._rgb;
              return this;
            }
            return out;
          }
          throw new Error("unknown channel " + channel + " in mode " + mode);
        } else {
          return src;
        }
      };
      var rgb$1 = function(col1, col2, f) {
        var xyz0 = col1._rgb;
        var xyz1 = col2._rgb;
        return new Color_1(
          xyz0[0] + f * (xyz1[0] - xyz0[0]),
          xyz0[1] + f * (xyz1[1] - xyz0[1]),
          xyz0[2] + f * (xyz1[2] - xyz0[2]),
          "rgb"
        );
      };
      interpolator.rgb = rgb$1;
      var sqrt$2 = Math.sqrt;
      var pow$3 = Math.pow;
      var lrgb = function(col1, col2, f) {
        var ref = col1._rgb;
        var x1 = ref[0];
        var y1 = ref[1];
        var z1 = ref[2];
        var ref$1 = col2._rgb;
        var x2 = ref$1[0];
        var y2 = ref$1[1];
        var z2 = ref$1[2];
        return new Color_1(
          sqrt$2(pow$3(x1, 2) * (1 - f) + pow$3(x2, 2) * f),
          sqrt$2(pow$3(y1, 2) * (1 - f) + pow$3(y2, 2) * f),
          sqrt$2(pow$3(z1, 2) * (1 - f) + pow$3(z2, 2) * f),
          "rgb"
        );
      };
      interpolator.lrgb = lrgb;
      var lab$1 = function(col1, col2, f) {
        var xyz0 = col1.lab();
        var xyz1 = col2.lab();
        return new Color_1(
          xyz0[0] + f * (xyz1[0] - xyz0[0]),
          xyz0[1] + f * (xyz1[1] - xyz0[1]),
          xyz0[2] + f * (xyz1[2] - xyz0[2]),
          "lab"
        );
      };
      interpolator.lab = lab$1;
      var _hsx = function(col1, col2, f, m2) {
        var assign, assign$1;
        var xyz0, xyz1;
        if (m2 === "hsl") {
          xyz0 = col1.hsl();
          xyz1 = col2.hsl();
        } else if (m2 === "hsv") {
          xyz0 = col1.hsv();
          xyz1 = col2.hsv();
        } else if (m2 === "hcg") {
          xyz0 = col1.hcg();
          xyz1 = col2.hcg();
        } else if (m2 === "hsi") {
          xyz0 = col1.hsi();
          xyz1 = col2.hsi();
        } else if (m2 === "lch" || m2 === "hcl") {
          m2 = "hcl";
          xyz0 = col1.hcl();
          xyz1 = col2.hcl();
        }
        var hue0, hue1, sat0, sat1, lbv0, lbv1;
        if (m2.substr(0, 1) === "h") {
          assign = xyz0, hue0 = assign[0], sat0 = assign[1], lbv0 = assign[2];
          assign$1 = xyz1, hue1 = assign$1[0], sat1 = assign$1[1], lbv1 = assign$1[2];
        }
        var sat, hue, lbv, dh;
        if (!isNaN(hue0) && !isNaN(hue1)) {
          if (hue1 > hue0 && hue1 - hue0 > 180) {
            dh = hue1 - (hue0 + 360);
          } else if (hue1 < hue0 && hue0 - hue1 > 180) {
            dh = hue1 + 360 - hue0;
          } else {
            dh = hue1 - hue0;
          }
          hue = hue0 + f * dh;
        } else if (!isNaN(hue0)) {
          hue = hue0;
          if ((lbv1 == 1 || lbv1 == 0) && m2 != "hsv") {
            sat = sat0;
          }
        } else if (!isNaN(hue1)) {
          hue = hue1;
          if ((lbv0 == 1 || lbv0 == 0) && m2 != "hsv") {
            sat = sat1;
          }
        } else {
          hue = Number.NaN;
        }
        if (sat === void 0) {
          sat = sat0 + f * (sat1 - sat0);
        }
        lbv = lbv0 + f * (lbv1 - lbv0);
        return new Color_1([hue, sat, lbv], m2);
      };
      var lch$1 = function(col1, col2, f) {
        return _hsx(col1, col2, f, "lch");
      };
      interpolator.lch = lch$1;
      interpolator.hcl = lch$1;
      var num$1 = function(col1, col2, f) {
        var c1 = col1.num();
        var c2 = col2.num();
        return new Color_1(c1 + f * (c2 - c1), "num");
      };
      interpolator.num = num$1;
      var hcg$1 = function(col1, col2, f) {
        return _hsx(col1, col2, f, "hcg");
      };
      interpolator.hcg = hcg$1;
      var hsi$1 = function(col1, col2, f) {
        return _hsx(col1, col2, f, "hsi");
      };
      interpolator.hsi = hsi$1;
      var hsl$1 = function(col1, col2, f) {
        return _hsx(col1, col2, f, "hsl");
      };
      interpolator.hsl = hsl$1;
      var hsv$1 = function(col1, col2, f) {
        return _hsx(col1, col2, f, "hsv");
      };
      interpolator.hsv = hsv$1;
      var clip_rgb$2 = utils.clip_rgb;
      var pow$4 = Math.pow;
      var sqrt$3 = Math.sqrt;
      var PI$1 = Math.PI;
      var cos$2 = Math.cos;
      var sin$1 = Math.sin;
      var atan2$1 = Math.atan2;
      var average = function(colors, mode, weights) {
        if (mode === void 0)
          mode = "lrgb";
        if (weights === void 0)
          weights = null;
        var l2 = colors.length;
        if (!weights) {
          weights = Array.from(new Array(l2)).map(function() {
            return 1;
          });
        }
        var k2 = l2 / weights.reduce(function(a2, b2) {
          return a2 + b2;
        });
        weights.forEach(function(w, i4) {
          weights[i4] *= k2;
        });
        colors = colors.map(function(c2) {
          return new Color_1(c2);
        });
        if (mode === "lrgb") {
          return _average_lrgb(colors, weights);
        }
        var first = colors.shift();
        var xyz = first.get(mode);
        var cnt = [];
        var dx = 0;
        var dy = 0;
        for (var i3 = 0; i3 < xyz.length; i3++) {
          xyz[i3] = (xyz[i3] || 0) * weights[0];
          cnt.push(isNaN(xyz[i3]) ? 0 : weights[0]);
          if (mode.charAt(i3) === "h" && !isNaN(xyz[i3])) {
            var A = xyz[i3] / 180 * PI$1;
            dx += cos$2(A) * weights[0];
            dy += sin$1(A) * weights[0];
          }
        }
        var alpha = first.alpha() * weights[0];
        colors.forEach(function(c2, ci) {
          var xyz2 = c2.get(mode);
          alpha += c2.alpha() * weights[ci + 1];
          for (var i4 = 0; i4 < xyz.length; i4++) {
            if (!isNaN(xyz2[i4])) {
              cnt[i4] += weights[ci + 1];
              if (mode.charAt(i4) === "h") {
                var A2 = xyz2[i4] / 180 * PI$1;
                dx += cos$2(A2) * weights[ci + 1];
                dy += sin$1(A2) * weights[ci + 1];
              } else {
                xyz[i4] += xyz2[i4] * weights[ci + 1];
              }
            }
          }
        });
        for (var i$12 = 0; i$12 < xyz.length; i$12++) {
          if (mode.charAt(i$12) === "h") {
            var A$1 = atan2$1(dy / cnt[i$12], dx / cnt[i$12]) / PI$1 * 180;
            while (A$1 < 0) {
              A$1 += 360;
            }
            while (A$1 >= 360) {
              A$1 -= 360;
            }
            xyz[i$12] = A$1;
          } else {
            xyz[i$12] = xyz[i$12] / cnt[i$12];
          }
        }
        alpha /= l2;
        return new Color_1(xyz, mode).alpha(alpha > 0.99999 ? 1 : alpha, true);
      };
      var _average_lrgb = function(colors, weights) {
        var l2 = colors.length;
        var xyz = [0, 0, 0, 0];
        for (var i3 = 0; i3 < colors.length; i3++) {
          var col = colors[i3];
          var f = weights[i3] / l2;
          var rgb = col._rgb;
          xyz[0] += pow$4(rgb[0], 2) * f;
          xyz[1] += pow$4(rgb[1], 2) * f;
          xyz[2] += pow$4(rgb[2], 2) * f;
          xyz[3] += rgb[3] * f;
        }
        xyz[0] = sqrt$3(xyz[0]);
        xyz[1] = sqrt$3(xyz[1]);
        xyz[2] = sqrt$3(xyz[2]);
        if (xyz[3] > 0.9999999) {
          xyz[3] = 1;
        }
        return new Color_1(clip_rgb$2(xyz));
      };
      var type$j = utils.type;
      var pow$5 = Math.pow;
      var scale = function(colors) {
        var _mode = "rgb";
        var _nacol = chroma_1("#ccc");
        var _spread = 0;
        var _domain = [0, 1];
        var _pos = [];
        var _padding = [0, 0];
        var _classes = false;
        var _colors = [];
        var _out = false;
        var _min = 0;
        var _max = 1;
        var _correctLightness = false;
        var _colorCache = {};
        var _useCache = true;
        var _gamma = 1;
        var setColors = function(colors2) {
          colors2 = colors2 || ["#fff", "#000"];
          if (colors2 && type$j(colors2) === "string" && chroma_1.brewer && chroma_1.brewer[colors2.toLowerCase()]) {
            colors2 = chroma_1.brewer[colors2.toLowerCase()];
          }
          if (type$j(colors2) === "array") {
            if (colors2.length === 1) {
              colors2 = [colors2[0], colors2[0]];
            }
            colors2 = colors2.slice(0);
            for (var c2 = 0; c2 < colors2.length; c2++) {
              colors2[c2] = chroma_1(colors2[c2]);
            }
            _pos.length = 0;
            for (var c$1 = 0; c$1 < colors2.length; c$1++) {
              _pos.push(c$1 / (colors2.length - 1));
            }
          }
          resetCache();
          return _colors = colors2;
        };
        var getClass = function(value) {
          if (_classes != null) {
            var n = _classes.length - 1;
            var i3 = 0;
            while (i3 < n && value >= _classes[i3]) {
              i3++;
            }
            return i3 - 1;
          }
          return 0;
        };
        var tMapLightness = function(t) {
          return t;
        };
        var tMapDomain = function(t) {
          return t;
        };
        var getColor = function(val, bypassMap) {
          var col, t;
          if (bypassMap == null) {
            bypassMap = false;
          }
          if (isNaN(val) || val === null) {
            return _nacol;
          }
          if (!bypassMap) {
            if (_classes && _classes.length > 2) {
              var c2 = getClass(val);
              t = c2 / (_classes.length - 2);
            } else if (_max !== _min) {
              t = (val - _min) / (_max - _min);
            } else {
              t = 1;
            }
          } else {
            t = val;
          }
          t = tMapDomain(t);
          if (!bypassMap) {
            t = tMapLightness(t);
          }
          if (_gamma !== 1) {
            t = pow$5(t, _gamma);
          }
          t = _padding[0] + t * (1 - _padding[0] - _padding[1]);
          t = Math.min(1, Math.max(0, t));
          var k2 = Math.floor(t * 1e4);
          if (_useCache && _colorCache[k2]) {
            col = _colorCache[k2];
          } else {
            if (type$j(_colors) === "array") {
              for (var i3 = 0; i3 < _pos.length; i3++) {
                var p2 = _pos[i3];
                if (t <= p2) {
                  col = _colors[i3];
                  break;
                }
                if (t >= p2 && i3 === _pos.length - 1) {
                  col = _colors[i3];
                  break;
                }
                if (t > p2 && t < _pos[i3 + 1]) {
                  t = (t - p2) / (_pos[i3 + 1] - p2);
                  col = chroma_1.interpolate(_colors[i3], _colors[i3 + 1], t, _mode);
                  break;
                }
              }
            } else if (type$j(_colors) === "function") {
              col = _colors(t);
            }
            if (_useCache) {
              _colorCache[k2] = col;
            }
          }
          return col;
        };
        var resetCache = function() {
          return _colorCache = {};
        };
        setColors(colors);
        var f = function(v2) {
          var c2 = chroma_1(getColor(v2));
          if (_out && c2[_out]) {
            return c2[_out]();
          } else {
            return c2;
          }
        };
        f.classes = function(classes) {
          if (classes != null) {
            if (type$j(classes) === "array") {
              _classes = classes;
              _domain = [classes[0], classes[classes.length - 1]];
            } else {
              var d2 = chroma_1.analyze(_domain);
              if (classes === 0) {
                _classes = [d2.min, d2.max];
              } else {
                _classes = chroma_1.limits(d2, "e", classes);
              }
            }
            return f;
          }
          return _classes;
        };
        f.domain = function(domain) {
          if (!arguments.length) {
            return _domain;
          }
          _min = domain[0];
          _max = domain[domain.length - 1];
          _pos = [];
          var k2 = _colors.length;
          if (domain.length === k2 && _min !== _max) {
            for (var i3 = 0, list2 = Array.from(domain); i3 < list2.length; i3 += 1) {
              var d2 = list2[i3];
              _pos.push((d2 - _min) / (_max - _min));
            }
          } else {
            for (var c2 = 0; c2 < k2; c2++) {
              _pos.push(c2 / (k2 - 1));
            }
            if (domain.length > 2) {
              var tOut = domain.map(function(d3, i4) {
                return i4 / (domain.length - 1);
              });
              var tBreaks = domain.map(function(d3) {
                return (d3 - _min) / (_max - _min);
              });
              if (!tBreaks.every(function(val, i4) {
                return tOut[i4] === val;
              })) {
                tMapDomain = function(t) {
                  if (t <= 0 || t >= 1) {
                    return t;
                  }
                  var i4 = 0;
                  while (t >= tBreaks[i4 + 1]) {
                    i4++;
                  }
                  var f2 = (t - tBreaks[i4]) / (tBreaks[i4 + 1] - tBreaks[i4]);
                  var out = tOut[i4] + f2 * (tOut[i4 + 1] - tOut[i4]);
                  return out;
                };
              }
            }
          }
          _domain = [_min, _max];
          return f;
        };
        f.mode = function(_m) {
          if (!arguments.length) {
            return _mode;
          }
          _mode = _m;
          resetCache();
          return f;
        };
        f.range = function(colors2, _pos2) {
          setColors(colors2, _pos2);
          return f;
        };
        f.out = function(_o) {
          _out = _o;
          return f;
        };
        f.spread = function(val) {
          if (!arguments.length) {
            return _spread;
          }
          _spread = val;
          return f;
        };
        f.correctLightness = function(v2) {
          if (v2 == null) {
            v2 = true;
          }
          _correctLightness = v2;
          resetCache();
          if (_correctLightness) {
            tMapLightness = function(t) {
              var L0 = getColor(0, true).lab()[0];
              var L1 = getColor(1, true).lab()[0];
              var pol = L0 > L1;
              var L_actual = getColor(t, true).lab()[0];
              var L_ideal = L0 + (L1 - L0) * t;
              var L_diff = L_actual - L_ideal;
              var t0 = 0;
              var t1 = 1;
              var max_iter = 20;
              while (Math.abs(L_diff) > 0.01 && max_iter-- > 0) {
                (function() {
                  if (pol) {
                    L_diff *= -1;
                  }
                  if (L_diff < 0) {
                    t0 = t;
                    t += (t1 - t) * 0.5;
                  } else {
                    t1 = t;
                    t += (t0 - t) * 0.5;
                  }
                  L_actual = getColor(t, true).lab()[0];
                  return L_diff = L_actual - L_ideal;
                })();
              }
              return t;
            };
          } else {
            tMapLightness = function(t) {
              return t;
            };
          }
          return f;
        };
        f.padding = function(p2) {
          if (p2 != null) {
            if (type$j(p2) === "number") {
              p2 = [p2, p2];
            }
            _padding = p2;
            return f;
          } else {
            return _padding;
          }
        };
        f.colors = function(numColors, out) {
          if (arguments.length < 2) {
            out = "hex";
          }
          var result = [];
          if (arguments.length === 0) {
            result = _colors.slice(0);
          } else if (numColors === 1) {
            result = [f(0.5)];
          } else if (numColors > 1) {
            var dm = _domain[0];
            var dd = _domain[1] - dm;
            result = __range__(0, numColors, false).map(function(i4) {
              return f(dm + i4 / (numColors - 1) * dd);
            });
          } else {
            colors = [];
            var samples = [];
            if (_classes && _classes.length > 2) {
              for (var i3 = 1, end = _classes.length, asc = 1 <= end; asc ? i3 < end : i3 > end; asc ? i3++ : i3--) {
                samples.push((_classes[i3 - 1] + _classes[i3]) * 0.5);
              }
            } else {
              samples = _domain;
            }
            result = samples.map(function(v2) {
              return f(v2);
            });
          }
          if (chroma_1[out]) {
            result = result.map(function(c2) {
              return c2[out]();
            });
          }
          return result;
        };
        f.cache = function(c2) {
          if (c2 != null) {
            _useCache = c2;
            return f;
          } else {
            return _useCache;
          }
        };
        f.gamma = function(g2) {
          if (g2 != null) {
            _gamma = g2;
            return f;
          } else {
            return _gamma;
          }
        };
        f.nodata = function(d2) {
          if (d2 != null) {
            _nacol = chroma_1(d2);
            return f;
          } else {
            return _nacol;
          }
        };
        return f;
      };
      function __range__(left, right, inclusive) {
        var range = [];
        var ascending = left < right;
        var end = !inclusive ? right : ascending ? right + 1 : right - 1;
        for (var i3 = left; ascending ? i3 < end : i3 > end; ascending ? i3++ : i3--) {
          range.push(i3);
        }
        return range;
      }
      var bezier = function(colors) {
        var assign, assign$1, assign$2;
        var I2, lab0, lab1, lab2;
        colors = colors.map(function(c2) {
          return new Color_1(c2);
        });
        if (colors.length === 2) {
          assign = colors.map(function(c2) {
            return c2.lab();
          }), lab0 = assign[0], lab1 = assign[1];
          I2 = function(t) {
            var lab = [0, 1, 2].map(function(i3) {
              return lab0[i3] + t * (lab1[i3] - lab0[i3]);
            });
            return new Color_1(lab, "lab");
          };
        } else if (colors.length === 3) {
          assign$1 = colors.map(function(c2) {
            return c2.lab();
          }), lab0 = assign$1[0], lab1 = assign$1[1], lab2 = assign$1[2];
          I2 = function(t) {
            var lab = [0, 1, 2].map(function(i3) {
              return (1 - t) * (1 - t) * lab0[i3] + 2 * (1 - t) * t * lab1[i3] + t * t * lab2[i3];
            });
            return new Color_1(lab, "lab");
          };
        } else if (colors.length === 4) {
          var lab3;
          assign$2 = colors.map(function(c2) {
            return c2.lab();
          }), lab0 = assign$2[0], lab1 = assign$2[1], lab2 = assign$2[2], lab3 = assign$2[3];
          I2 = function(t) {
            var lab = [0, 1, 2].map(function(i3) {
              return (1 - t) * (1 - t) * (1 - t) * lab0[i3] + 3 * (1 - t) * (1 - t) * t * lab1[i3] + 3 * (1 - t) * t * t * lab2[i3] + t * t * t * lab3[i3];
            });
            return new Color_1(lab, "lab");
          };
        } else if (colors.length === 5) {
          var I0 = bezier(colors.slice(0, 3));
          var I1 = bezier(colors.slice(2, 5));
          I2 = function(t) {
            if (t < 0.5) {
              return I0(t * 2);
            } else {
              return I1((t - 0.5) * 2);
            }
          };
        }
        return I2;
      };
      var bezier_1 = function(colors) {
        var f = bezier(colors);
        f.scale = function() {
          return scale(f);
        };
        return f;
      };
      var blend = function(bottom, top, mode) {
        if (!blend[mode]) {
          throw new Error("unknown blend mode " + mode);
        }
        return blend[mode](bottom, top);
      };
      var blend_f = function(f) {
        return function(bottom, top) {
          var c0 = chroma_1(top).rgb();
          var c1 = chroma_1(bottom).rgb();
          return chroma_1.rgb(f(c0, c1));
        };
      };
      var each = function(f) {
        return function(c0, c1) {
          var out = [];
          out[0] = f(c0[0], c1[0]);
          out[1] = f(c0[1], c1[1]);
          out[2] = f(c0[2], c1[2]);
          return out;
        };
      };
      var normal = function(a2) {
        return a2;
      };
      var multiply = function(a2, b2) {
        return a2 * b2 / 255;
      };
      var darken$1 = function(a2, b2) {
        return a2 > b2 ? b2 : a2;
      };
      var lighten = function(a2, b2) {
        return a2 > b2 ? a2 : b2;
      };
      var screen = function(a2, b2) {
        return 255 * (1 - (1 - a2 / 255) * (1 - b2 / 255));
      };
      var overlay = function(a2, b2) {
        return b2 < 128 ? 2 * a2 * b2 / 255 : 255 * (1 - 2 * (1 - a2 / 255) * (1 - b2 / 255));
      };
      var burn = function(a2, b2) {
        return 255 * (1 - (1 - b2 / 255) / (a2 / 255));
      };
      var dodge = function(a2, b2) {
        if (a2 === 255) {
          return 255;
        }
        a2 = 255 * (b2 / 255) / (1 - a2 / 255);
        return a2 > 255 ? 255 : a2;
      };
      blend.normal = blend_f(each(normal));
      blend.multiply = blend_f(each(multiply));
      blend.screen = blend_f(each(screen));
      blend.overlay = blend_f(each(overlay));
      blend.darken = blend_f(each(darken$1));
      blend.lighten = blend_f(each(lighten));
      blend.dodge = blend_f(each(dodge));
      blend.burn = blend_f(each(burn));
      var blend_1 = blend;
      var type$k = utils.type;
      var clip_rgb$3 = utils.clip_rgb;
      var TWOPI$2 = utils.TWOPI;
      var pow$6 = Math.pow;
      var sin$2 = Math.sin;
      var cos$3 = Math.cos;
      var cubehelix = function(start, rotations, hue, gamma, lightness) {
        if (start === void 0)
          start = 300;
        if (rotations === void 0)
          rotations = -1.5;
        if (hue === void 0)
          hue = 1;
        if (gamma === void 0)
          gamma = 1;
        if (lightness === void 0)
          lightness = [0, 1];
        var dh = 0, dl;
        if (type$k(lightness) === "array") {
          dl = lightness[1] - lightness[0];
        } else {
          dl = 0;
          lightness = [lightness, lightness];
        }
        var f = function(fract) {
          var a2 = TWOPI$2 * ((start + 120) / 360 + rotations * fract);
          var l2 = pow$6(lightness[0] + dl * fract, gamma);
          var h2 = dh !== 0 ? hue[0] + fract * dh : hue;
          var amp = h2 * l2 * (1 - l2) / 2;
          var cos_a = cos$3(a2);
          var sin_a = sin$2(a2);
          var r = l2 + amp * (-0.14861 * cos_a + 1.78277 * sin_a);
          var g2 = l2 + amp * (-0.29227 * cos_a - 0.90649 * sin_a);
          var b2 = l2 + amp * (1.97294 * cos_a);
          return chroma_1(clip_rgb$3([r * 255, g2 * 255, b2 * 255, 1]));
        };
        f.start = function(s2) {
          if (s2 == null) {
            return start;
          }
          start = s2;
          return f;
        };
        f.rotations = function(r) {
          if (r == null) {
            return rotations;
          }
          rotations = r;
          return f;
        };
        f.gamma = function(g2) {
          if (g2 == null) {
            return gamma;
          }
          gamma = g2;
          return f;
        };
        f.hue = function(h2) {
          if (h2 == null) {
            return hue;
          }
          hue = h2;
          if (type$k(hue) === "array") {
            dh = hue[1] - hue[0];
            if (dh === 0) {
              hue = hue[1];
            }
          } else {
            dh = 0;
          }
          return f;
        };
        f.lightness = function(h2) {
          if (h2 == null) {
            return lightness;
          }
          if (type$k(h2) === "array") {
            lightness = h2;
            dl = h2[1] - h2[0];
          } else {
            lightness = [h2, h2];
            dl = 0;
          }
          return f;
        };
        f.scale = function() {
          return chroma_1.scale(f);
        };
        f.hue(hue);
        return f;
      };
      var digits = "0123456789abcdef";
      var floor$2 = Math.floor;
      var random = Math.random;
      var random_1 = function() {
        var code = "#";
        for (var i3 = 0; i3 < 6; i3++) {
          code += digits.charAt(floor$2(random() * 16));
        }
        return new Color_1(code, "hex");
      };
      var log$1 = Math.log;
      var pow$7 = Math.pow;
      var floor$3 = Math.floor;
      var abs = Math.abs;
      var analyze = function(data, key2) {
        if (key2 === void 0)
          key2 = null;
        var r = {
          min: Number.MAX_VALUE,
          max: Number.MAX_VALUE * -1,
          sum: 0,
          values: [],
          count: 0
        };
        if (type(data) === "object") {
          data = Object.values(data);
        }
        data.forEach(function(val) {
          if (key2 && type(val) === "object") {
            val = val[key2];
          }
          if (val !== void 0 && val !== null && !isNaN(val)) {
            r.values.push(val);
            r.sum += val;
            if (val < r.min) {
              r.min = val;
            }
            if (val > r.max) {
              r.max = val;
            }
            r.count += 1;
          }
        });
        r.domain = [r.min, r.max];
        r.limits = function(mode, num) {
          return limits(r, mode, num);
        };
        return r;
      };
      var limits = function(data, mode, num) {
        if (mode === void 0)
          mode = "equal";
        if (num === void 0)
          num = 7;
        if (type(data) == "array") {
          data = analyze(data);
        }
        var min2 = data.min;
        var max2 = data.max;
        var values = data.values.sort(function(a2, b2) {
          return a2 - b2;
        });
        if (num === 1) {
          return [min2, max2];
        }
        var limits2 = [];
        if (mode.substr(0, 1) === "c") {
          limits2.push(min2);
          limits2.push(max2);
        }
        if (mode.substr(0, 1) === "e") {
          limits2.push(min2);
          for (var i3 = 1; i3 < num; i3++) {
            limits2.push(min2 + i3 / num * (max2 - min2));
          }
          limits2.push(max2);
        } else if (mode.substr(0, 1) === "l") {
          if (min2 <= 0) {
            throw new Error("Logarithmic scales are only possible for values > 0");
          }
          var min_log = Math.LOG10E * log$1(min2);
          var max_log = Math.LOG10E * log$1(max2);
          limits2.push(min2);
          for (var i$12 = 1; i$12 < num; i$12++) {
            limits2.push(pow$7(10, min_log + i$12 / num * (max_log - min_log)));
          }
          limits2.push(max2);
        } else if (mode.substr(0, 1) === "q") {
          limits2.push(min2);
          for (var i$2 = 1; i$2 < num; i$2++) {
            var p2 = (values.length - 1) * i$2 / num;
            var pb = floor$3(p2);
            if (pb === p2) {
              limits2.push(values[pb]);
            } else {
              var pr2 = p2 - pb;
              limits2.push(values[pb] * (1 - pr2) + values[pb + 1] * pr2);
            }
          }
          limits2.push(max2);
        } else if (mode.substr(0, 1) === "k") {
          var cluster;
          var n = values.length;
          var assignments = new Array(n);
          var clusterSizes = new Array(num);
          var repeat = true;
          var nb_iters = 0;
          var centroids = null;
          centroids = [];
          centroids.push(min2);
          for (var i$3 = 1; i$3 < num; i$3++) {
            centroids.push(min2 + i$3 / num * (max2 - min2));
          }
          centroids.push(max2);
          while (repeat) {
            for (var j = 0; j < num; j++) {
              clusterSizes[j] = 0;
            }
            for (var i$4 = 0; i$4 < n; i$4++) {
              var value = values[i$4];
              var mindist = Number.MAX_VALUE;
              var best = void 0;
              for (var j$1 = 0; j$1 < num; j$1++) {
                var dist = abs(centroids[j$1] - value);
                if (dist < mindist) {
                  mindist = dist;
                  best = j$1;
                }
                clusterSizes[best]++;
                assignments[i$4] = best;
              }
            }
            var newCentroids = new Array(num);
            for (var j$2 = 0; j$2 < num; j$2++) {
              newCentroids[j$2] = null;
            }
            for (var i$5 = 0; i$5 < n; i$5++) {
              cluster = assignments[i$5];
              if (newCentroids[cluster] === null) {
                newCentroids[cluster] = values[i$5];
              } else {
                newCentroids[cluster] += values[i$5];
              }
            }
            for (var j$3 = 0; j$3 < num; j$3++) {
              newCentroids[j$3] *= 1 / clusterSizes[j$3];
            }
            repeat = false;
            for (var j$4 = 0; j$4 < num; j$4++) {
              if (newCentroids[j$4] !== centroids[j$4]) {
                repeat = true;
                break;
              }
            }
            centroids = newCentroids;
            nb_iters++;
            if (nb_iters > 200) {
              repeat = false;
            }
          }
          var kClusters = {};
          for (var j$5 = 0; j$5 < num; j$5++) {
            kClusters[j$5] = [];
          }
          for (var i$6 = 0; i$6 < n; i$6++) {
            cluster = assignments[i$6];
            kClusters[cluster].push(values[i$6]);
          }
          var tmpKMeansBreaks = [];
          for (var j$6 = 0; j$6 < num; j$6++) {
            tmpKMeansBreaks.push(kClusters[j$6][0]);
            tmpKMeansBreaks.push(kClusters[j$6][kClusters[j$6].length - 1]);
          }
          tmpKMeansBreaks = tmpKMeansBreaks.sort(function(a2, b2) {
            return a2 - b2;
          });
          limits2.push(tmpKMeansBreaks[0]);
          for (var i$7 = 1; i$7 < tmpKMeansBreaks.length; i$7 += 2) {
            var v2 = tmpKMeansBreaks[i$7];
            if (!isNaN(v2) && limits2.indexOf(v2) === -1) {
              limits2.push(v2);
            }
          }
        }
        return limits2;
      };
      var analyze_1 = { analyze, limits };
      var contrast = function(a2, b2) {
        a2 = new Color_1(a2);
        b2 = new Color_1(b2);
        var l1 = a2.luminance();
        var l2 = b2.luminance();
        return l1 > l2 ? (l1 + 0.05) / (l2 + 0.05) : (l2 + 0.05) / (l1 + 0.05);
      };
      var sqrt$4 = Math.sqrt;
      var atan2$2 = Math.atan2;
      var abs$1 = Math.abs;
      var cos$4 = Math.cos;
      var PI$2 = Math.PI;
      var deltaE = function(a2, b2, L2, C) {
        if (L2 === void 0)
          L2 = 1;
        if (C === void 0)
          C = 1;
        a2 = new Color_1(a2);
        b2 = new Color_1(b2);
        var ref = Array.from(a2.lab());
        var L1 = ref[0];
        var a1 = ref[1];
        var b1 = ref[2];
        var ref$1 = Array.from(b2.lab());
        var L22 = ref$1[0];
        var a22 = ref$1[1];
        var b22 = ref$1[2];
        var c1 = sqrt$4(a1 * a1 + b1 * b1);
        var c2 = sqrt$4(a22 * a22 + b22 * b22);
        var sl = L1 < 16 ? 0.511 : 0.040975 * L1 / (1 + 0.01765 * L1);
        var sc = 0.0638 * c1 / (1 + 0.0131 * c1) + 0.638;
        var h1 = c1 < 1e-6 ? 0 : atan2$2(b1, a1) * 180 / PI$2;
        while (h1 < 0) {
          h1 += 360;
        }
        while (h1 >= 360) {
          h1 -= 360;
        }
        var t = h1 >= 164 && h1 <= 345 ? 0.56 + abs$1(0.2 * cos$4(PI$2 * (h1 + 168) / 180)) : 0.36 + abs$1(0.4 * cos$4(PI$2 * (h1 + 35) / 180));
        var c4 = c1 * c1 * c1 * c1;
        var f = sqrt$4(c4 / (c4 + 1900));
        var sh = sc * (f * t + 1 - f);
        var delL = L1 - L22;
        var delC = c1 - c2;
        var delA = a1 - a22;
        var delB = b1 - b22;
        var dH2 = delA * delA + delB * delB - delC * delC;
        var v1 = delL / (L2 * sl);
        var v2 = delC / (C * sc);
        var v3 = sh;
        return sqrt$4(v1 * v1 + v2 * v2 + dH2 / (v3 * v3));
      };
      var distance = function(a2, b2, mode) {
        if (mode === void 0)
          mode = "lab";
        a2 = new Color_1(a2);
        b2 = new Color_1(b2);
        var l1 = a2.get(mode);
        var l2 = b2.get(mode);
        var sum_sq = 0;
        for (var i3 in l1) {
          var d2 = (l1[i3] || 0) - (l2[i3] || 0);
          sum_sq += d2 * d2;
        }
        return Math.sqrt(sum_sq);
      };
      var valid = function() {
        var args = [], len = arguments.length;
        while (len--)
          args[len] = arguments[len];
        try {
          new (Function.prototype.bind.apply(Color_1, [null].concat(args)))();
          return true;
        } catch (e) {
          return false;
        }
      };
      var scales = {
        cool: function cool() {
          return scale([chroma_1.hsl(180, 1, 0.9), chroma_1.hsl(250, 0.7, 0.4)]);
        },
        hot: function hot() {
          return scale(["#000", "#f00", "#ff0", "#fff"], [0, 0.25, 0.75, 1]).mode("rgb");
        }
      };
      var colorbrewer = {
        // sequential
        OrRd: ["#fff7ec", "#fee8c8", "#fdd49e", "#fdbb84", "#fc8d59", "#ef6548", "#d7301f", "#b30000", "#7f0000"],
        PuBu: ["#fff7fb", "#ece7f2", "#d0d1e6", "#a6bddb", "#74a9cf", "#3690c0", "#0570b0", "#045a8d", "#023858"],
        BuPu: ["#f7fcfd", "#e0ecf4", "#bfd3e6", "#9ebcda", "#8c96c6", "#8c6bb1", "#88419d", "#810f7c", "#4d004b"],
        Oranges: ["#fff5eb", "#fee6ce", "#fdd0a2", "#fdae6b", "#fd8d3c", "#f16913", "#d94801", "#a63603", "#7f2704"],
        BuGn: ["#f7fcfd", "#e5f5f9", "#ccece6", "#99d8c9", "#66c2a4", "#41ae76", "#238b45", "#006d2c", "#00441b"],
        YlOrBr: ["#ffffe5", "#fff7bc", "#fee391", "#fec44f", "#fe9929", "#ec7014", "#cc4c02", "#993404", "#662506"],
        YlGn: ["#ffffe5", "#f7fcb9", "#d9f0a3", "#addd8e", "#78c679", "#41ab5d", "#238443", "#006837", "#004529"],
        Reds: ["#fff5f0", "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#ef3b2c", "#cb181d", "#a50f15", "#67000d"],
        RdPu: ["#fff7f3", "#fde0dd", "#fcc5c0", "#fa9fb5", "#f768a1", "#dd3497", "#ae017e", "#7a0177", "#49006a"],
        Greens: ["#f7fcf5", "#e5f5e0", "#c7e9c0", "#a1d99b", "#74c476", "#41ab5d", "#238b45", "#006d2c", "#00441b"],
        YlGnBu: ["#ffffd9", "#edf8b1", "#c7e9b4", "#7fcdbb", "#41b6c4", "#1d91c0", "#225ea8", "#253494", "#081d58"],
        Purples: ["#fcfbfd", "#efedf5", "#dadaeb", "#bcbddc", "#9e9ac8", "#807dba", "#6a51a3", "#54278f", "#3f007d"],
        GnBu: ["#f7fcf0", "#e0f3db", "#ccebc5", "#a8ddb5", "#7bccc4", "#4eb3d3", "#2b8cbe", "#0868ac", "#084081"],
        Greys: ["#ffffff", "#f0f0f0", "#d9d9d9", "#bdbdbd", "#969696", "#737373", "#525252", "#252525", "#000000"],
        YlOrRd: ["#ffffcc", "#ffeda0", "#fed976", "#feb24c", "#fd8d3c", "#fc4e2a", "#e31a1c", "#bd0026", "#800026"],
        PuRd: ["#f7f4f9", "#e7e1ef", "#d4b9da", "#c994c7", "#df65b0", "#e7298a", "#ce1256", "#980043", "#67001f"],
        Blues: ["#f7fbff", "#deebf7", "#c6dbef", "#9ecae1", "#6baed6", "#4292c6", "#2171b5", "#08519c", "#08306b"],
        PuBuGn: ["#fff7fb", "#ece2f0", "#d0d1e6", "#a6bddb", "#67a9cf", "#3690c0", "#02818a", "#016c59", "#014636"],
        Viridis: ["#440154", "#482777", "#3f4a8a", "#31678e", "#26838f", "#1f9d8a", "#6cce5a", "#b6de2b", "#fee825"],
        // diverging
        Spectral: ["#9e0142", "#d53e4f", "#f46d43", "#fdae61", "#fee08b", "#ffffbf", "#e6f598", "#abdda4", "#66c2a5", "#3288bd", "#5e4fa2"],
        RdYlGn: ["#a50026", "#d73027", "#f46d43", "#fdae61", "#fee08b", "#ffffbf", "#d9ef8b", "#a6d96a", "#66bd63", "#1a9850", "#006837"],
        RdBu: ["#67001f", "#b2182b", "#d6604d", "#f4a582", "#fddbc7", "#f7f7f7", "#d1e5f0", "#92c5de", "#4393c3", "#2166ac", "#053061"],
        PiYG: ["#8e0152", "#c51b7d", "#de77ae", "#f1b6da", "#fde0ef", "#f7f7f7", "#e6f5d0", "#b8e186", "#7fbc41", "#4d9221", "#276419"],
        PRGn: ["#40004b", "#762a83", "#9970ab", "#c2a5cf", "#e7d4e8", "#f7f7f7", "#d9f0d3", "#a6dba0", "#5aae61", "#1b7837", "#00441b"],
        RdYlBu: ["#a50026", "#d73027", "#f46d43", "#fdae61", "#fee090", "#ffffbf", "#e0f3f8", "#abd9e9", "#74add1", "#4575b4", "#313695"],
        BrBG: ["#543005", "#8c510a", "#bf812d", "#dfc27d", "#f6e8c3", "#f5f5f5", "#c7eae5", "#80cdc1", "#35978f", "#01665e", "#003c30"],
        RdGy: ["#67001f", "#b2182b", "#d6604d", "#f4a582", "#fddbc7", "#ffffff", "#e0e0e0", "#bababa", "#878787", "#4d4d4d", "#1a1a1a"],
        PuOr: ["#7f3b08", "#b35806", "#e08214", "#fdb863", "#fee0b6", "#f7f7f7", "#d8daeb", "#b2abd2", "#8073ac", "#542788", "#2d004b"],
        // qualitative
        Set2: ["#66c2a5", "#fc8d62", "#8da0cb", "#e78ac3", "#a6d854", "#ffd92f", "#e5c494", "#b3b3b3"],
        Accent: ["#7fc97f", "#beaed4", "#fdc086", "#ffff99", "#386cb0", "#f0027f", "#bf5b17", "#666666"],
        Set1: ["#e41a1c", "#377eb8", "#4daf4a", "#984ea3", "#ff7f00", "#ffff33", "#a65628", "#f781bf", "#999999"],
        Set3: ["#8dd3c7", "#ffffb3", "#bebada", "#fb8072", "#80b1d3", "#fdb462", "#b3de69", "#fccde5", "#d9d9d9", "#bc80bd", "#ccebc5", "#ffed6f"],
        Dark2: ["#1b9e77", "#d95f02", "#7570b3", "#e7298a", "#66a61e", "#e6ab02", "#a6761d", "#666666"],
        Paired: ["#a6cee3", "#1f78b4", "#b2df8a", "#33a02c", "#fb9a99", "#e31a1c", "#fdbf6f", "#ff7f00", "#cab2d6", "#6a3d9a", "#ffff99", "#b15928"],
        Pastel2: ["#b3e2cd", "#fdcdac", "#cbd5e8", "#f4cae4", "#e6f5c9", "#fff2ae", "#f1e2cc", "#cccccc"],
        Pastel1: ["#fbb4ae", "#b3cde3", "#ccebc5", "#decbe4", "#fed9a6", "#ffffcc", "#e5d8bd", "#fddaec", "#f2f2f2"]
      };
      for (var i$1 = 0, list$1 = Object.keys(colorbrewer); i$1 < list$1.length; i$1 += 1) {
        var key = list$1[i$1];
        colorbrewer[key.toLowerCase()] = colorbrewer[key];
      }
      var colorbrewer_1 = colorbrewer;
      chroma_1.average = average;
      chroma_1.bezier = bezier_1;
      chroma_1.blend = blend_1;
      chroma_1.cubehelix = cubehelix;
      chroma_1.mix = chroma_1.interpolate = mix;
      chroma_1.random = random_1;
      chroma_1.scale = scale;
      chroma_1.analyze = analyze_1.analyze;
      chroma_1.contrast = contrast;
      chroma_1.deltaE = deltaE;
      chroma_1.distance = distance;
      chroma_1.limits = analyze_1.limits;
      chroma_1.valid = valid;
      chroma_1.scales = scales;
      chroma_1.colors = w3cx11_1;
      chroma_1.brewer = colorbrewer_1;
      var chroma_js = chroma_1;
      return chroma_js;
    });
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/array/from.js
var require_from7 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/array/from.js"(exports, module) {
    module.exports = require_from2();
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/symbol.js
var require_symbol7 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/symbol.js"(exports, module) {
    module.exports = require_symbol2();
  }
});

// node_modules/core-js-pure/es/symbol/iterator.js
var require_iterator = __commonJS({
  "node_modules/core-js-pure/es/symbol/iterator.js"(exports, module) {
    "use strict";
    require_es_array_iterator();
    require_es_object_to_string();
    require_es_string_iterator();
    require_es_symbol_iterator();
    var WrappedWellKnownSymbolModule = require_well_known_symbol_wrapped();
    module.exports = WrappedWellKnownSymbolModule.f("iterator");
  }
});

// node_modules/core-js-pure/stable/symbol/iterator.js
var require_iterator2 = __commonJS({
  "node_modules/core-js-pure/stable/symbol/iterator.js"(exports, module) {
    "use strict";
    var parent = require_iterator();
    require_web_dom_collections_iterator();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/actual/symbol/iterator.js
var require_iterator3 = __commonJS({
  "node_modules/core-js-pure/actual/symbol/iterator.js"(exports, module) {
    "use strict";
    var parent = require_iterator2();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/full/symbol/iterator.js
var require_iterator4 = __commonJS({
  "node_modules/core-js-pure/full/symbol/iterator.js"(exports, module) {
    "use strict";
    var parent = require_iterator3();
    module.exports = parent;
  }
});

// node_modules/core-js-pure/features/symbol/iterator.js
var require_iterator5 = __commonJS({
  "node_modules/core-js-pure/features/symbol/iterator.js"(exports, module) {
    "use strict";
    module.exports = require_iterator4();
  }
});

// node_modules/@babel/runtime-corejs3/core-js/symbol/iterator.js
var require_iterator6 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js/symbol/iterator.js"(exports, module) {
    module.exports = require_iterator5();
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/slice.js
var require_slice8 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/slice.js"(exports, module) {
    module.exports = require_slice3();
  }
});

// node_modules/core-js-pure/es/array/virtual/concat.js
var require_concat = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/concat.js"(exports, module) {
    "use strict";
    require_es_array_concat();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "concat");
  }
});

// node_modules/core-js-pure/es/instance/concat.js
var require_concat2 = __commonJS({
  "node_modules/core-js-pure/es/instance/concat.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_concat();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.concat;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.concat ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/concat.js
var require_concat3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/concat.js"(exports, module) {
    "use strict";
    var parent = require_concat2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/concat.js
var require_concat4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/concat.js"(exports, module) {
    module.exports = require_concat3();
  }
});

// node_modules/core-js-pure/internals/array-set-length.js
var require_array_set_length = __commonJS({
  "node_modules/core-js-pure/internals/array-set-length.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var isArray = require_is_array();
    var $TypeError = TypeError;
    var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
    var SILENT_ON_NON_WRITABLE_LENGTH_SET = DESCRIPTORS && !function() {
      if (this !== void 0)
        return true;
      try {
        Object.defineProperty([], "length", { writable: false }).length = 1;
      } catch (error) {
        return error instanceof TypeError;
      }
    }();
    module.exports = SILENT_ON_NON_WRITABLE_LENGTH_SET ? function(O2, length) {
      if (isArray(O2) && !getOwnPropertyDescriptor(O2, "length").writable) {
        throw new $TypeError("Cannot set read only .length");
      }
      return O2.length = length;
    } : function(O2, length) {
      return O2.length = length;
    };
  }
});

// node_modules/core-js-pure/internals/delete-property-or-throw.js
var require_delete_property_or_throw = __commonJS({
  "node_modules/core-js-pure/internals/delete-property-or-throw.js"(exports, module) {
    "use strict";
    var tryToString = require_try_to_string();
    var $TypeError = TypeError;
    module.exports = function(O2, P2) {
      if (!delete O2[P2])
        throw new $TypeError("Cannot delete property " + tryToString(P2) + " of " + tryToString(O2));
    };
  }
});

// node_modules/core-js-pure/modules/es.array.splice.js
var require_es_array_splice = __commonJS({
  "node_modules/core-js-pure/modules/es.array.splice.js"() {
    "use strict";
    var $2 = require_export();
    var toObject = require_to_object();
    var toAbsoluteIndex = require_to_absolute_index();
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var lengthOfArrayLike = require_length_of_array_like();
    var setArrayLength = require_array_set_length();
    var doesNotExceedSafeInteger = require_does_not_exceed_safe_integer();
    var arraySpeciesCreate = require_array_species_create();
    var createProperty = require_create_property();
    var deletePropertyOrThrow = require_delete_property_or_throw();
    var arrayMethodHasSpeciesSupport = require_array_method_has_species_support();
    var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport("splice");
    var max = Math.max;
    var min = Math.min;
    $2({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
      splice: function splice(start, deleteCount) {
        var O2 = toObject(this);
        var len = lengthOfArrayLike(O2);
        var actualStart = toAbsoluteIndex(start, len);
        var argumentsLength = arguments.length;
        var insertCount, actualDeleteCount, A, k2, from, to;
        if (argumentsLength === 0) {
          insertCount = actualDeleteCount = 0;
        } else if (argumentsLength === 1) {
          insertCount = 0;
          actualDeleteCount = len - actualStart;
        } else {
          insertCount = argumentsLength - 2;
          actualDeleteCount = min(max(toIntegerOrInfinity(deleteCount), 0), len - actualStart);
        }
        doesNotExceedSafeInteger(len + insertCount - actualDeleteCount);
        A = arraySpeciesCreate(O2, actualDeleteCount);
        for (k2 = 0; k2 < actualDeleteCount; k2++) {
          from = actualStart + k2;
          if (from in O2)
            createProperty(A, k2, O2[from]);
        }
        A.length = actualDeleteCount;
        if (insertCount < actualDeleteCount) {
          for (k2 = actualStart; k2 < len - actualDeleteCount; k2++) {
            from = k2 + actualDeleteCount;
            to = k2 + insertCount;
            if (from in O2)
              O2[to] = O2[from];
            else
              deletePropertyOrThrow(O2, to);
          }
          for (k2 = len; k2 > len - actualDeleteCount + insertCount; k2--)
            deletePropertyOrThrow(O2, k2 - 1);
        } else if (insertCount > actualDeleteCount) {
          for (k2 = len - actualDeleteCount; k2 > actualStart; k2--) {
            from = k2 + actualDeleteCount - 1;
            to = k2 + insertCount - 1;
            if (from in O2)
              O2[to] = O2[from];
            else
              deletePropertyOrThrow(O2, to);
          }
        }
        for (k2 = 0; k2 < insertCount; k2++) {
          O2[k2 + actualStart] = arguments[k2 + 2];
        }
        setArrayLength(O2, len - actualDeleteCount + insertCount);
        return A;
      }
    });
  }
});

// node_modules/core-js-pure/es/array/virtual/splice.js
var require_splice = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/splice.js"(exports, module) {
    "use strict";
    require_es_array_splice();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "splice");
  }
});

// node_modules/core-js-pure/es/instance/splice.js
var require_splice2 = __commonJS({
  "node_modules/core-js-pure/es/instance/splice.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_splice();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.splice;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.splice ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/splice.js
var require_splice3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/splice.js"(exports, module) {
    "use strict";
    var parent = require_splice2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/splice.js
var require_splice4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/splice.js"(exports, module) {
    module.exports = require_splice3();
  }
});

// node_modules/core-js-pure/modules/es.array.filter.js
var require_es_array_filter = __commonJS({
  "node_modules/core-js-pure/modules/es.array.filter.js"() {
    "use strict";
    var $2 = require_export();
    var $filter = require_array_iteration().filter;
    var arrayMethodHasSpeciesSupport = require_array_method_has_species_support();
    var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport("filter");
    $2({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
      filter: function filter(callbackfn) {
        return $filter(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
      }
    });
  }
});

// node_modules/core-js-pure/es/array/virtual/filter.js
var require_filter = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/filter.js"(exports, module) {
    "use strict";
    require_es_array_filter();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "filter");
  }
});

// node_modules/core-js-pure/es/instance/filter.js
var require_filter2 = __commonJS({
  "node_modules/core-js-pure/es/instance/filter.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_filter();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.filter;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.filter ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/filter.js
var require_filter3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/filter.js"(exports, module) {
    "use strict";
    var parent = require_filter2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/filter.js
var require_filter4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/filter.js"(exports, module) {
    module.exports = require_filter3();
  }
});

// node_modules/core-js-pure/internals/array-sort.js
var require_array_sort = __commonJS({
  "node_modules/core-js-pure/internals/array-sort.js"(exports, module) {
    "use strict";
    var arraySlice = require_array_slice();
    var floor = Math.floor;
    var sort = function(array, comparefn) {
      var length = array.length;
      if (length < 8) {
        var i2 = 1;
        var element, j;
        while (i2 < length) {
          j = i2;
          element = array[i2];
          while (j && comparefn(array[j - 1], element) > 0) {
            array[j] = array[--j];
          }
          if (j !== i2++)
            array[j] = element;
        }
      } else {
        var middle = floor(length / 2);
        var left = sort(arraySlice(array, 0, middle), comparefn);
        var right = sort(arraySlice(array, middle), comparefn);
        var llength = left.length;
        var rlength = right.length;
        var lindex = 0;
        var rindex = 0;
        while (lindex < llength || rindex < rlength) {
          array[lindex + rindex] = lindex < llength && rindex < rlength ? comparefn(left[lindex], right[rindex]) <= 0 ? left[lindex++] : right[rindex++] : lindex < llength ? left[lindex++] : right[rindex++];
        }
      }
      return array;
    };
    module.exports = sort;
  }
});

// node_modules/core-js-pure/internals/array-method-is-strict.js
var require_array_method_is_strict = __commonJS({
  "node_modules/core-js-pure/internals/array-method-is-strict.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = function(METHOD_NAME, argument) {
      var method = [][METHOD_NAME];
      return !!method && fails(function() {
        method.call(null, argument || function() {
          return 1;
        }, 1);
      });
    };
  }
});

// node_modules/core-js-pure/internals/environment-ff-version.js
var require_environment_ff_version = __commonJS({
  "node_modules/core-js-pure/internals/environment-ff-version.js"(exports, module) {
    "use strict";
    var userAgent = require_environment_user_agent();
    var firefox = userAgent.match(/firefox\/(\d+)/i);
    module.exports = !!firefox && +firefox[1];
  }
});

// node_modules/core-js-pure/internals/environment-is-ie-or-edge.js
var require_environment_is_ie_or_edge = __commonJS({
  "node_modules/core-js-pure/internals/environment-is-ie-or-edge.js"(exports, module) {
    "use strict";
    var UA = require_environment_user_agent();
    module.exports = /MSIE|Trident/.test(UA);
  }
});

// node_modules/core-js-pure/internals/environment-webkit-version.js
var require_environment_webkit_version = __commonJS({
  "node_modules/core-js-pure/internals/environment-webkit-version.js"(exports, module) {
    "use strict";
    var userAgent = require_environment_user_agent();
    var webkit = userAgent.match(/AppleWebKit\/(\d+)\./);
    module.exports = !!webkit && +webkit[1];
  }
});

// node_modules/core-js-pure/modules/es.array.sort.js
var require_es_array_sort = __commonJS({
  "node_modules/core-js-pure/modules/es.array.sort.js"() {
    "use strict";
    var $2 = require_export();
    var uncurryThis = require_function_uncurry_this();
    var aCallable = require_a_callable();
    var toObject = require_to_object();
    var lengthOfArrayLike = require_length_of_array_like();
    var deletePropertyOrThrow = require_delete_property_or_throw();
    var toString = require_to_string();
    var fails = require_fails();
    var internalSort = require_array_sort();
    var arrayMethodIsStrict = require_array_method_is_strict();
    var FF = require_environment_ff_version();
    var IE_OR_EDGE = require_environment_is_ie_or_edge();
    var V8 = require_environment_v8_version();
    var WEBKIT = require_environment_webkit_version();
    var test = [];
    var nativeSort = uncurryThis(test.sort);
    var push = uncurryThis(test.push);
    var FAILS_ON_UNDEFINED = fails(function() {
      test.sort(void 0);
    });
    var FAILS_ON_NULL = fails(function() {
      test.sort(null);
    });
    var STRICT_METHOD = arrayMethodIsStrict("sort");
    var STABLE_SORT = !fails(function() {
      if (V8)
        return V8 < 70;
      if (FF && FF > 3)
        return;
      if (IE_OR_EDGE)
        return true;
      if (WEBKIT)
        return WEBKIT < 603;
      var result = "";
      var code, chr, value, index;
      for (code = 65; code < 76; code++) {
        chr = String.fromCharCode(code);
        switch (code) {
          case 66:
          case 69:
          case 70:
          case 72:
            value = 3;
            break;
          case 68:
          case 71:
            value = 4;
            break;
          default:
            value = 2;
        }
        for (index = 0; index < 47; index++) {
          test.push({ k: chr + index, v: value });
        }
      }
      test.sort(function(a2, b2) {
        return b2.v - a2.v;
      });
      for (index = 0; index < test.length; index++) {
        chr = test[index].k.charAt(0);
        if (result.charAt(result.length - 1) !== chr)
          result += chr;
      }
      return result !== "DGBEFHACIJK";
    });
    var FORCED = FAILS_ON_UNDEFINED || !FAILS_ON_NULL || !STRICT_METHOD || !STABLE_SORT;
    var getSortCompare = function(comparefn) {
      return function(x2, y2) {
        if (y2 === void 0)
          return -1;
        if (x2 === void 0)
          return 1;
        if (comparefn !== void 0)
          return +comparefn(x2, y2) || 0;
        return toString(x2) > toString(y2) ? 1 : -1;
      };
    };
    $2({ target: "Array", proto: true, forced: FORCED }, {
      sort: function sort(comparefn) {
        if (comparefn !== void 0)
          aCallable(comparefn);
        var array = toObject(this);
        if (STABLE_SORT)
          return comparefn === void 0 ? nativeSort(array) : nativeSort(array, comparefn);
        var items = [];
        var arrayLength = lengthOfArrayLike(array);
        var itemsLength, index;
        for (index = 0; index < arrayLength; index++) {
          if (index in array)
            push(items, array[index]);
        }
        internalSort(items, getSortCompare(comparefn));
        itemsLength = lengthOfArrayLike(items);
        index = 0;
        while (index < itemsLength)
          array[index] = items[index++];
        while (index < arrayLength)
          deletePropertyOrThrow(array, index++);
        return array;
      }
    });
  }
});

// node_modules/core-js-pure/es/array/virtual/sort.js
var require_sort = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/sort.js"(exports, module) {
    "use strict";
    require_es_array_sort();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "sort");
  }
});

// node_modules/core-js-pure/es/instance/sort.js
var require_sort2 = __commonJS({
  "node_modules/core-js-pure/es/instance/sort.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_sort();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.sort;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.sort ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/sort.js
var require_sort3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/sort.js"(exports, module) {
    "use strict";
    var parent = require_sort2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/sort.js
var require_sort4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/sort.js"(exports, module) {
    module.exports = require_sort3();
  }
});

// node_modules/core-js-pure/internals/string-repeat.js
var require_string_repeat = __commonJS({
  "node_modules/core-js-pure/internals/string-repeat.js"(exports, module) {
    "use strict";
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var toString = require_to_string();
    var requireObjectCoercible = require_require_object_coercible();
    var $RangeError = RangeError;
    module.exports = function repeat(count) {
      var str = toString(requireObjectCoercible(this));
      var result = "";
      var n = toIntegerOrInfinity(count);
      if (n < 0 || n === Infinity)
        throw new $RangeError("Wrong number of repetitions");
      for (; n > 0; (n >>>= 1) && (str += str))
        if (n & 1)
          result += str;
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/string-pad.js
var require_string_pad = __commonJS({
  "node_modules/core-js-pure/internals/string-pad.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var toLength = require_to_length();
    var toString = require_to_string();
    var $repeat = require_string_repeat();
    var requireObjectCoercible = require_require_object_coercible();
    var repeat = uncurryThis($repeat);
    var stringSlice = uncurryThis("".slice);
    var ceil = Math.ceil;
    var createMethod = function(IS_END) {
      return function($this, maxLength, fillString) {
        var S2 = toString(requireObjectCoercible($this));
        var intMaxLength = toLength(maxLength);
        var stringLength = S2.length;
        var fillStr = fillString === void 0 ? " " : toString(fillString);
        var fillLen, stringFiller;
        if (intMaxLength <= stringLength || fillStr === "")
          return S2;
        fillLen = intMaxLength - stringLength;
        stringFiller = repeat(fillStr, ceil(fillLen / fillStr.length));
        if (stringFiller.length > fillLen)
          stringFiller = stringSlice(stringFiller, 0, fillLen);
        return IS_END ? S2 + stringFiller : stringFiller + S2;
      };
    };
    module.exports = {
      // `String.prototype.padStart` method
      // https://tc39.es/ecma262/#sec-string.prototype.padstart
      start: createMethod(false),
      // `String.prototype.padEnd` method
      // https://tc39.es/ecma262/#sec-string.prototype.padend
      end: createMethod(true)
    };
  }
});

// node_modules/core-js-pure/internals/date-to-iso-string.js
var require_date_to_iso_string = __commonJS({
  "node_modules/core-js-pure/internals/date-to-iso-string.js"(exports, module) {
    "use strict";
    var uncurryThis = require_function_uncurry_this();
    var fails = require_fails();
    var padStart = require_string_pad().start;
    var $RangeError = RangeError;
    var $isFinite = isFinite;
    var abs = Math.abs;
    var DatePrototype = Date.prototype;
    var nativeDateToISOString = DatePrototype.toISOString;
    var thisTimeValue = uncurryThis(DatePrototype.getTime);
    var getUTCDate = uncurryThis(DatePrototype.getUTCDate);
    var getUTCFullYear = uncurryThis(DatePrototype.getUTCFullYear);
    var getUTCHours = uncurryThis(DatePrototype.getUTCHours);
    var getUTCMilliseconds = uncurryThis(DatePrototype.getUTCMilliseconds);
    var getUTCMinutes = uncurryThis(DatePrototype.getUTCMinutes);
    var getUTCMonth = uncurryThis(DatePrototype.getUTCMonth);
    var getUTCSeconds = uncurryThis(DatePrototype.getUTCSeconds);
    module.exports = fails(function() {
      return nativeDateToISOString.call(new Date(-5e13 - 1)) !== "0385-07-25T07:06:39.999Z";
    }) || !fails(function() {
      nativeDateToISOString.call(/* @__PURE__ */ new Date(NaN));
    }) ? function toISOString() {
      if (!$isFinite(thisTimeValue(this)))
        throw new $RangeError("Invalid time value");
      var date = this;
      var year = getUTCFullYear(date);
      var milliseconds = getUTCMilliseconds(date);
      var sign = year < 0 ? "-" : year > 9999 ? "+" : "";
      return sign + padStart(abs(year), sign ? 6 : 4, 0) + "-" + padStart(getUTCMonth(date) + 1, 2, 0) + "-" + padStart(getUTCDate(date), 2, 0) + "T" + padStart(getUTCHours(date), 2, 0) + ":" + padStart(getUTCMinutes(date), 2, 0) + ":" + padStart(getUTCSeconds(date), 2, 0) + "." + padStart(milliseconds, 3, 0) + "Z";
    } : nativeDateToISOString;
  }
});

// node_modules/core-js-pure/modules/es.date.to-json.js
var require_es_date_to_json = __commonJS({
  "node_modules/core-js-pure/modules/es.date.to-json.js"() {
    "use strict";
    var $2 = require_export();
    var call = require_function_call();
    var toObject = require_to_object();
    var toPrimitive = require_to_primitive();
    var toISOString = require_date_to_iso_string();
    var classof = require_classof_raw();
    var fails = require_fails();
    var FORCED = fails(function() {
      return (/* @__PURE__ */ new Date(NaN)).toJSON() !== null || call(Date.prototype.toJSON, { toISOString: function() {
        return 1;
      } }) !== 1;
    });
    $2({ target: "Date", proto: true, forced: FORCED }, {
      // eslint-disable-next-line no-unused-vars -- required for `.length`
      toJSON: function toJSON(key) {
        var O2 = toObject(this);
        var pv = toPrimitive(O2, "number");
        return typeof pv == "number" && !isFinite(pv) ? null : !("toISOString" in O2) && classof(O2) === "Date" ? call(toISOString, O2) : O2.toISOString();
      }
    });
  }
});

// node_modules/core-js-pure/es/json/stringify.js
var require_stringify = __commonJS({
  "node_modules/core-js-pure/es/json/stringify.js"(exports, module) {
    "use strict";
    require_es_date_to_json();
    require_es_json_stringify();
    var path = require_path();
    var apply = require_function_apply();
    if (!path.JSON)
      path.JSON = { stringify: JSON.stringify };
    module.exports = function stringify(it, replacer, space) {
      return apply(path.JSON.stringify, null, arguments);
    };
  }
});

// node_modules/core-js-pure/stable/json/stringify.js
var require_stringify2 = __commonJS({
  "node_modules/core-js-pure/stable/json/stringify.js"(exports, module) {
    "use strict";
    var parent = require_stringify();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/json/stringify.js
var require_stringify3 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/json/stringify.js"(exports, module) {
    module.exports = require_stringify2();
  }
});

// node_modules/core-js-pure/internals/array-buffer-non-extensible.js
var require_array_buffer_non_extensible = __commonJS({
  "node_modules/core-js-pure/internals/array-buffer-non-extensible.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = fails(function() {
      if (typeof ArrayBuffer == "function") {
        var buffer = new ArrayBuffer(8);
        if (Object.isExtensible(buffer))
          Object.defineProperty(buffer, "a", { value: 8 });
      }
    });
  }
});

// node_modules/core-js-pure/internals/object-is-extensible.js
var require_object_is_extensible = __commonJS({
  "node_modules/core-js-pure/internals/object-is-extensible.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    var isObject = require_is_object();
    var classof = require_classof_raw();
    var ARRAY_BUFFER_NON_EXTENSIBLE = require_array_buffer_non_extensible();
    var $isExtensible = Object.isExtensible;
    var FAILS_ON_PRIMITIVES = fails(function() {
      $isExtensible(1);
    });
    module.exports = FAILS_ON_PRIMITIVES || ARRAY_BUFFER_NON_EXTENSIBLE ? function isExtensible(it) {
      if (!isObject(it))
        return false;
      if (ARRAY_BUFFER_NON_EXTENSIBLE && classof(it) === "ArrayBuffer")
        return false;
      return $isExtensible ? $isExtensible(it) : true;
    } : $isExtensible;
  }
});

// node_modules/core-js-pure/internals/freezing.js
var require_freezing = __commonJS({
  "node_modules/core-js-pure/internals/freezing.js"(exports, module) {
    "use strict";
    var fails = require_fails();
    module.exports = !fails(function() {
      return Object.isExtensible(Object.preventExtensions({}));
    });
  }
});

// node_modules/core-js-pure/internals/internal-metadata.js
var require_internal_metadata = __commonJS({
  "node_modules/core-js-pure/internals/internal-metadata.js"(exports, module) {
    "use strict";
    var $2 = require_export();
    var uncurryThis = require_function_uncurry_this();
    var hiddenKeys = require_hidden_keys();
    var isObject = require_is_object();
    var hasOwn = require_has_own_property();
    var defineProperty = require_object_define_property().f;
    var getOwnPropertyNamesModule = require_object_get_own_property_names();
    var getOwnPropertyNamesExternalModule = require_object_get_own_property_names_external();
    var isExtensible = require_object_is_extensible();
    var uid = require_uid();
    var FREEZING = require_freezing();
    var REQUIRED = false;
    var METADATA = uid("meta");
    var id = 0;
    var setMetadata = function(it) {
      defineProperty(it, METADATA, { value: {
        objectID: "O" + id++,
        // object ID
        weakData: {}
        // weak collections IDs
      } });
    };
    var fastKey = function(it, create) {
      if (!isObject(it))
        return typeof it == "symbol" ? it : (typeof it == "string" ? "S" : "P") + it;
      if (!hasOwn(it, METADATA)) {
        if (!isExtensible(it))
          return "F";
        if (!create)
          return "E";
        setMetadata(it);
      }
      return it[METADATA].objectID;
    };
    var getWeakData = function(it, create) {
      if (!hasOwn(it, METADATA)) {
        if (!isExtensible(it))
          return true;
        if (!create)
          return false;
        setMetadata(it);
      }
      return it[METADATA].weakData;
    };
    var onFreeze = function(it) {
      if (FREEZING && REQUIRED && isExtensible(it) && !hasOwn(it, METADATA))
        setMetadata(it);
      return it;
    };
    var enable = function() {
      meta.enable = function() {
      };
      REQUIRED = true;
      var getOwnPropertyNames = getOwnPropertyNamesModule.f;
      var splice = uncurryThis([].splice);
      var test = {};
      test[METADATA] = 1;
      if (getOwnPropertyNames(test).length) {
        getOwnPropertyNamesModule.f = function(it) {
          var result = getOwnPropertyNames(it);
          for (var i2 = 0, length = result.length; i2 < length; i2++) {
            if (result[i2] === METADATA) {
              splice(result, i2, 1);
              break;
            }
          }
          return result;
        };
        $2({ target: "Object", stat: true, forced: true }, {
          getOwnPropertyNames: getOwnPropertyNamesExternalModule.f
        });
      }
    };
    var meta = module.exports = {
      enable,
      fastKey,
      getWeakData,
      onFreeze
    };
    hiddenKeys[METADATA] = true;
  }
});

// node_modules/core-js-pure/internals/iterate.js
var require_iterate = __commonJS({
  "node_modules/core-js-pure/internals/iterate.js"(exports, module) {
    "use strict";
    var bind = require_function_bind_context();
    var call = require_function_call();
    var anObject = require_an_object();
    var tryToString = require_try_to_string();
    var isArrayIteratorMethod = require_is_array_iterator_method();
    var lengthOfArrayLike = require_length_of_array_like();
    var isPrototypeOf = require_object_is_prototype_of();
    var getIterator = require_get_iterator();
    var getIteratorMethod = require_get_iterator_method();
    var iteratorClose = require_iterator_close();
    var $TypeError = TypeError;
    var Result = function(stopped, result) {
      this.stopped = stopped;
      this.result = result;
    };
    var ResultPrototype = Result.prototype;
    module.exports = function(iterable, unboundFunction, options) {
      var that = options && options.that;
      var AS_ENTRIES = !!(options && options.AS_ENTRIES);
      var IS_RECORD = !!(options && options.IS_RECORD);
      var IS_ITERATOR = !!(options && options.IS_ITERATOR);
      var INTERRUPTED = !!(options && options.INTERRUPTED);
      var fn = bind(unboundFunction, that);
      var iterator, iterFn, index, length, result, next, step;
      var stop = function(condition) {
        if (iterator)
          iteratorClose(iterator, "normal");
        return new Result(true, condition);
      };
      var callFn = function(value) {
        if (AS_ENTRIES) {
          anObject(value);
          return INTERRUPTED ? fn(value[0], value[1], stop) : fn(value[0], value[1]);
        }
        return INTERRUPTED ? fn(value, stop) : fn(value);
      };
      if (IS_RECORD) {
        iterator = iterable.iterator;
      } else if (IS_ITERATOR) {
        iterator = iterable;
      } else {
        iterFn = getIteratorMethod(iterable);
        if (!iterFn)
          throw new $TypeError(tryToString(iterable) + " is not iterable");
        if (isArrayIteratorMethod(iterFn)) {
          for (index = 0, length = lengthOfArrayLike(iterable); length > index; index++) {
            result = callFn(iterable[index]);
            if (result && isPrototypeOf(ResultPrototype, result))
              return result;
          }
          return new Result(false);
        }
        iterator = getIterator(iterable, iterFn);
      }
      next = IS_RECORD ? iterable.next : iterator.next;
      while (!(step = call(next, iterator)).done) {
        try {
          result = callFn(step.value);
        } catch (error) {
          iteratorClose(iterator, "throw", error);
        }
        if (typeof result == "object" && result && isPrototypeOf(ResultPrototype, result))
          return result;
      }
      return new Result(false);
    };
  }
});

// node_modules/core-js-pure/internals/an-instance.js
var require_an_instance = __commonJS({
  "node_modules/core-js-pure/internals/an-instance.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var $TypeError = TypeError;
    module.exports = function(it, Prototype) {
      if (isPrototypeOf(Prototype, it))
        return it;
      throw new $TypeError("Incorrect invocation");
    };
  }
});

// node_modules/core-js-pure/internals/collection.js
var require_collection = __commonJS({
  "node_modules/core-js-pure/internals/collection.js"(exports, module) {
    "use strict";
    var $2 = require_export();
    var globalThis2 = require_global_this();
    var InternalMetadataModule = require_internal_metadata();
    var fails = require_fails();
    var createNonEnumerableProperty = require_create_non_enumerable_property();
    var iterate = require_iterate();
    var anInstance = require_an_instance();
    var isCallable = require_is_callable();
    var isObject = require_is_object();
    var isNullOrUndefined = require_is_null_or_undefined();
    var setToStringTag = require_set_to_string_tag();
    var defineProperty = require_object_define_property().f;
    var forEach = require_array_iteration().forEach;
    var DESCRIPTORS = require_descriptors();
    var InternalStateModule = require_internal_state();
    var setInternalState = InternalStateModule.set;
    var internalStateGetterFor = InternalStateModule.getterFor;
    module.exports = function(CONSTRUCTOR_NAME, wrapper, common) {
      var IS_MAP = CONSTRUCTOR_NAME.indexOf("Map") !== -1;
      var IS_WEAK = CONSTRUCTOR_NAME.indexOf("Weak") !== -1;
      var ADDER = IS_MAP ? "set" : "add";
      var NativeConstructor = globalThis2[CONSTRUCTOR_NAME];
      var NativePrototype = NativeConstructor && NativeConstructor.prototype;
      var exported = {};
      var Constructor;
      if (!DESCRIPTORS || !isCallable(NativeConstructor) || !(IS_WEAK || NativePrototype.forEach && !fails(function() {
        new NativeConstructor().entries().next();
      }))) {
        Constructor = common.getConstructor(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER);
        InternalMetadataModule.enable();
      } else {
        Constructor = wrapper(function(target, iterable) {
          setInternalState(anInstance(target, Prototype), {
            type: CONSTRUCTOR_NAME,
            collection: new NativeConstructor()
          });
          if (!isNullOrUndefined(iterable))
            iterate(iterable, target[ADDER], { that: target, AS_ENTRIES: IS_MAP });
        });
        var Prototype = Constructor.prototype;
        var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);
        forEach(["add", "clear", "delete", "forEach", "get", "has", "set", "keys", "values", "entries"], function(KEY) {
          var IS_ADDER = KEY === "add" || KEY === "set";
          if (KEY in NativePrototype && !(IS_WEAK && KEY === "clear")) {
            createNonEnumerableProperty(Prototype, KEY, function(a2, b2) {
              var collection = getInternalState(this).collection;
              if (!IS_ADDER && IS_WEAK && !isObject(a2))
                return KEY === "get" ? void 0 : false;
              var result = collection[KEY](a2 === 0 ? 0 : a2, b2);
              return IS_ADDER ? this : result;
            });
          }
        });
        IS_WEAK || defineProperty(Prototype, "size", {
          configurable: true,
          get: function() {
            return getInternalState(this).collection.size;
          }
        });
      }
      setToStringTag(Constructor, CONSTRUCTOR_NAME, false, true);
      exported[CONSTRUCTOR_NAME] = Constructor;
      $2({ global: true, forced: true }, exported);
      if (!IS_WEAK)
        common.setStrong(Constructor, CONSTRUCTOR_NAME, IS_MAP);
      return Constructor;
    };
  }
});

// node_modules/core-js-pure/internals/define-built-ins.js
var require_define_built_ins = __commonJS({
  "node_modules/core-js-pure/internals/define-built-ins.js"(exports, module) {
    "use strict";
    var defineBuiltIn = require_define_built_in();
    module.exports = function(target, src, options) {
      for (var key in src) {
        if (options && options.unsafe && target[key])
          target[key] = src[key];
        else
          defineBuiltIn(target, key, src[key], options);
      }
      return target;
    };
  }
});

// node_modules/core-js-pure/internals/set-species.js
var require_set_species = __commonJS({
  "node_modules/core-js-pure/internals/set-species.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var defineBuiltInAccessor = require_define_built_in_accessor();
    var wellKnownSymbol = require_well_known_symbol();
    var DESCRIPTORS = require_descriptors();
    var SPECIES = wellKnownSymbol("species");
    module.exports = function(CONSTRUCTOR_NAME) {
      var Constructor = getBuiltIn(CONSTRUCTOR_NAME);
      if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {
        defineBuiltInAccessor(Constructor, SPECIES, {
          configurable: true,
          get: function() {
            return this;
          }
        });
      }
    };
  }
});

// node_modules/core-js-pure/internals/collection-strong.js
var require_collection_strong = __commonJS({
  "node_modules/core-js-pure/internals/collection-strong.js"(exports, module) {
    "use strict";
    var create = require_object_create();
    var defineBuiltInAccessor = require_define_built_in_accessor();
    var defineBuiltIns = require_define_built_ins();
    var bind = require_function_bind_context();
    var anInstance = require_an_instance();
    var isNullOrUndefined = require_is_null_or_undefined();
    var iterate = require_iterate();
    var defineIterator = require_iterator_define();
    var createIterResultObject = require_create_iter_result_object();
    var setSpecies = require_set_species();
    var DESCRIPTORS = require_descriptors();
    var fastKey = require_internal_metadata().fastKey;
    var InternalStateModule = require_internal_state();
    var setInternalState = InternalStateModule.set;
    var internalStateGetterFor = InternalStateModule.getterFor;
    module.exports = {
      getConstructor: function(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {
        var Constructor = wrapper(function(that, iterable) {
          anInstance(that, Prototype);
          setInternalState(that, {
            type: CONSTRUCTOR_NAME,
            index: create(null),
            first: null,
            last: null,
            size: 0
          });
          if (!DESCRIPTORS)
            that.size = 0;
          if (!isNullOrUndefined(iterable))
            iterate(iterable, that[ADDER], { that, AS_ENTRIES: IS_MAP });
        });
        var Prototype = Constructor.prototype;
        var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);
        var define2 = function(that, key, value) {
          var state = getInternalState(that);
          var entry = getEntry(that, key);
          var previous, index;
          if (entry) {
            entry.value = value;
          } else {
            state.last = entry = {
              index: index = fastKey(key, true),
              key,
              value,
              previous: previous = state.last,
              next: null,
              removed: false
            };
            if (!state.first)
              state.first = entry;
            if (previous)
              previous.next = entry;
            if (DESCRIPTORS)
              state.size++;
            else
              that.size++;
            if (index !== "F")
              state.index[index] = entry;
          }
          return that;
        };
        var getEntry = function(that, key) {
          var state = getInternalState(that);
          var index = fastKey(key);
          var entry;
          if (index !== "F")
            return state.index[index];
          for (entry = state.first; entry; entry = entry.next) {
            if (entry.key === key)
              return entry;
          }
        };
        defineBuiltIns(Prototype, {
          // `{ Map, Set }.prototype.clear()` methods
          // https://tc39.es/ecma262/#sec-map.prototype.clear
          // https://tc39.es/ecma262/#sec-set.prototype.clear
          clear: function clear() {
            var that = this;
            var state = getInternalState(that);
            var entry = state.first;
            while (entry) {
              entry.removed = true;
              if (entry.previous)
                entry.previous = entry.previous.next = null;
              entry = entry.next;
            }
            state.first = state.last = null;
            state.index = create(null);
            if (DESCRIPTORS)
              state.size = 0;
            else
              that.size = 0;
          },
          // `{ Map, Set }.prototype.delete(key)` methods
          // https://tc39.es/ecma262/#sec-map.prototype.delete
          // https://tc39.es/ecma262/#sec-set.prototype.delete
          "delete": function(key) {
            var that = this;
            var state = getInternalState(that);
            var entry = getEntry(that, key);
            if (entry) {
              var next = entry.next;
              var prev = entry.previous;
              delete state.index[entry.index];
              entry.removed = true;
              if (prev)
                prev.next = next;
              if (next)
                next.previous = prev;
              if (state.first === entry)
                state.first = next;
              if (state.last === entry)
                state.last = prev;
              if (DESCRIPTORS)
                state.size--;
              else
                that.size--;
            }
            return !!entry;
          },
          // `{ Map, Set }.prototype.forEach(callbackfn, thisArg = undefined)` methods
          // https://tc39.es/ecma262/#sec-map.prototype.foreach
          // https://tc39.es/ecma262/#sec-set.prototype.foreach
          forEach: function forEach(callbackfn) {
            var state = getInternalState(this);
            var boundFunction = bind(callbackfn, arguments.length > 1 ? arguments[1] : void 0);
            var entry;
            while (entry = entry ? entry.next : state.first) {
              boundFunction(entry.value, entry.key, this);
              while (entry && entry.removed)
                entry = entry.previous;
            }
          },
          // `{ Map, Set}.prototype.has(key)` methods
          // https://tc39.es/ecma262/#sec-map.prototype.has
          // https://tc39.es/ecma262/#sec-set.prototype.has
          has: function has(key) {
            return !!getEntry(this, key);
          }
        });
        defineBuiltIns(Prototype, IS_MAP ? {
          // `Map.prototype.get(key)` method
          // https://tc39.es/ecma262/#sec-map.prototype.get
          get: function get(key) {
            var entry = getEntry(this, key);
            return entry && entry.value;
          },
          // `Map.prototype.set(key, value)` method
          // https://tc39.es/ecma262/#sec-map.prototype.set
          set: function set(key, value) {
            return define2(this, key === 0 ? 0 : key, value);
          }
        } : {
          // `Set.prototype.add(value)` method
          // https://tc39.es/ecma262/#sec-set.prototype.add
          add: function add(value) {
            return define2(this, value = value === 0 ? 0 : value, value);
          }
        });
        if (DESCRIPTORS)
          defineBuiltInAccessor(Prototype, "size", {
            configurable: true,
            get: function() {
              return getInternalState(this).size;
            }
          });
        return Constructor;
      },
      setStrong: function(Constructor, CONSTRUCTOR_NAME, IS_MAP) {
        var ITERATOR_NAME = CONSTRUCTOR_NAME + " Iterator";
        var getInternalCollectionState = internalStateGetterFor(CONSTRUCTOR_NAME);
        var getInternalIteratorState = internalStateGetterFor(ITERATOR_NAME);
        defineIterator(Constructor, CONSTRUCTOR_NAME, function(iterated, kind) {
          setInternalState(this, {
            type: ITERATOR_NAME,
            target: iterated,
            state: getInternalCollectionState(iterated),
            kind,
            last: null
          });
        }, function() {
          var state = getInternalIteratorState(this);
          var kind = state.kind;
          var entry = state.last;
          while (entry && entry.removed)
            entry = entry.previous;
          if (!state.target || !(state.last = entry = entry ? entry.next : state.state.first)) {
            state.target = null;
            return createIterResultObject(void 0, true);
          }
          if (kind === "keys")
            return createIterResultObject(entry.key, false);
          if (kind === "values")
            return createIterResultObject(entry.value, false);
          return createIterResultObject([entry.key, entry.value], false);
        }, IS_MAP ? "entries" : "values", !IS_MAP, true);
        setSpecies(CONSTRUCTOR_NAME);
      }
    };
  }
});

// node_modules/core-js-pure/modules/es.set.constructor.js
var require_es_set_constructor = __commonJS({
  "node_modules/core-js-pure/modules/es.set.constructor.js"() {
    "use strict";
    var collection = require_collection();
    var collectionStrong = require_collection_strong();
    collection("Set", function(init) {
      return function Set2() {
        return init(this, arguments.length ? arguments[0] : void 0);
      };
    }, collectionStrong);
  }
});

// node_modules/core-js-pure/modules/es.set.js
var require_es_set = __commonJS({
  "node_modules/core-js-pure/modules/es.set.js"() {
    "use strict";
    require_es_set_constructor();
  }
});

// node_modules/core-js-pure/internals/a-set.js
var require_a_set = __commonJS({
  "node_modules/core-js-pure/internals/a-set.js"(exports, module) {
    "use strict";
    var tryToString = require_try_to_string();
    var $TypeError = TypeError;
    module.exports = function(it) {
      if (typeof it == "object" && "size" in it && "has" in it && "add" in it && "delete" in it && "keys" in it)
        return it;
      throw new $TypeError(tryToString(it) + " is not a set");
    };
  }
});

// node_modules/core-js-pure/internals/caller.js
var require_caller = __commonJS({
  "node_modules/core-js-pure/internals/caller.js"(exports, module) {
    "use strict";
    module.exports = function(methodName, numArgs) {
      return numArgs === 1 ? function(object, arg) {
        return object[methodName](arg);
      } : function(object, arg1, arg2) {
        return object[methodName](arg1, arg2);
      };
    };
  }
});

// node_modules/core-js-pure/internals/set-helpers.js
var require_set_helpers = __commonJS({
  "node_modules/core-js-pure/internals/set-helpers.js"(exports, module) {
    "use strict";
    var getBuiltIn = require_get_built_in();
    var caller = require_caller();
    var Set2 = getBuiltIn("Set");
    var SetPrototype = Set2.prototype;
    module.exports = {
      Set: Set2,
      add: caller("add", 1),
      has: caller("has", 1),
      remove: caller("delete", 1),
      proto: SetPrototype
    };
  }
});

// node_modules/core-js-pure/internals/iterate-simple.js
var require_iterate_simple = __commonJS({
  "node_modules/core-js-pure/internals/iterate-simple.js"(exports, module) {
    "use strict";
    var call = require_function_call();
    module.exports = function(record, fn, ITERATOR_INSTEAD_OF_RECORD) {
      var iterator = ITERATOR_INSTEAD_OF_RECORD ? record : record.iterator;
      var next = record.next;
      var step, result;
      while (!(step = call(next, iterator)).done) {
        result = fn(step.value);
        if (result !== void 0)
          return result;
      }
    };
  }
});

// node_modules/core-js-pure/internals/set-iterate.js
var require_set_iterate = __commonJS({
  "node_modules/core-js-pure/internals/set-iterate.js"(exports, module) {
    "use strict";
    var iterateSimple = require_iterate_simple();
    module.exports = function(set, fn, interruptible) {
      return interruptible ? iterateSimple(set.keys(), fn, true) : set.forEach(fn);
    };
  }
});

// node_modules/core-js-pure/internals/set-clone.js
var require_set_clone = __commonJS({
  "node_modules/core-js-pure/internals/set-clone.js"(exports, module) {
    "use strict";
    var SetHelpers = require_set_helpers();
    var iterate = require_set_iterate();
    var Set2 = SetHelpers.Set;
    var add = SetHelpers.add;
    module.exports = function(set) {
      var result = new Set2();
      iterate(set, function(it) {
        add(result, it);
      });
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/set-size.js
var require_set_size = __commonJS({
  "node_modules/core-js-pure/internals/set-size.js"(exports, module) {
    "use strict";
    module.exports = function(set) {
      return set.size;
    };
  }
});

// node_modules/core-js-pure/internals/get-iterator-direct.js
var require_get_iterator_direct = __commonJS({
  "node_modules/core-js-pure/internals/get-iterator-direct.js"(exports, module) {
    "use strict";
    module.exports = function(obj) {
      return {
        iterator: obj,
        next: obj.next,
        done: false
      };
    };
  }
});

// node_modules/core-js-pure/internals/get-set-record.js
var require_get_set_record = __commonJS({
  "node_modules/core-js-pure/internals/get-set-record.js"(exports, module) {
    "use strict";
    var aCallable = require_a_callable();
    var anObject = require_an_object();
    var call = require_function_call();
    var toIntegerOrInfinity = require_to_integer_or_infinity();
    var getIteratorDirect = require_get_iterator_direct();
    var INVALID_SIZE = "Invalid size";
    var $RangeError = RangeError;
    var $TypeError = TypeError;
    var max = Math.max;
    var SetRecord = function(set, intSize) {
      this.set = set;
      this.size = max(intSize, 0);
      this.has = aCallable(set.has);
      this.keys = aCallable(set.keys);
    };
    SetRecord.prototype = {
      getIterator: function() {
        return getIteratorDirect(anObject(call(this.keys, this.set)));
      },
      includes: function(it) {
        return call(this.has, this.set, it);
      }
    };
    module.exports = function(obj) {
      anObject(obj);
      var numSize = +obj.size;
      if (numSize !== numSize)
        throw new $TypeError(INVALID_SIZE);
      var intSize = toIntegerOrInfinity(numSize);
      if (intSize < 0)
        throw new $RangeError(INVALID_SIZE);
      return new SetRecord(obj, intSize);
    };
  }
});

// node_modules/core-js-pure/internals/set-difference.js
var require_set_difference = __commonJS({
  "node_modules/core-js-pure/internals/set-difference.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var SetHelpers = require_set_helpers();
    var clone = require_set_clone();
    var size = require_set_size();
    var getSetRecord = require_get_set_record();
    var iterateSet = require_set_iterate();
    var iterateSimple = require_iterate_simple();
    var has = SetHelpers.has;
    var remove = SetHelpers.remove;
    module.exports = function difference(other) {
      var O2 = aSet(this);
      var otherRec = getSetRecord(other);
      var result = clone(O2);
      if (size(O2) <= otherRec.size)
        iterateSet(O2, function(e) {
          if (otherRec.includes(e))
            remove(result, e);
        });
      else
        iterateSimple(otherRec.getIterator(), function(e) {
          if (has(result, e))
            remove(result, e);
        });
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/set-method-accept-set-like.js
var require_set_method_accept_set_like = __commonJS({
  "node_modules/core-js-pure/internals/set-method-accept-set-like.js"(exports, module) {
    "use strict";
    module.exports = function() {
      return false;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.difference.v2.js
var require_es_set_difference_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.difference.v2.js"() {
    "use strict";
    var $2 = require_export();
    var difference = require_set_difference();
    var fails = require_fails();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var SET_LIKE_INCORRECT_BEHAVIOR = !setMethodAcceptSetLike("difference", function(result) {
      return result.size === 0;
    });
    var FORCED = SET_LIKE_INCORRECT_BEHAVIOR || fails(function() {
      var setLike = {
        size: 1,
        has: function() {
          return true;
        },
        keys: function() {
          var index = 0;
          return {
            next: function() {
              var done = index++ > 1;
              if (baseSet.has(1))
                baseSet.clear();
              return { done, value: 2 };
            }
          };
        }
      };
      var baseSet = /* @__PURE__ */ new Set([1, 2, 3, 4]);
      return baseSet.difference(setLike).size !== 3;
    });
    $2({ target: "Set", proto: true, real: true, forced: FORCED }, {
      difference
    });
  }
});

// node_modules/core-js-pure/internals/set-intersection.js
var require_set_intersection = __commonJS({
  "node_modules/core-js-pure/internals/set-intersection.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var SetHelpers = require_set_helpers();
    var size = require_set_size();
    var getSetRecord = require_get_set_record();
    var iterateSet = require_set_iterate();
    var iterateSimple = require_iterate_simple();
    var Set2 = SetHelpers.Set;
    var add = SetHelpers.add;
    var has = SetHelpers.has;
    module.exports = function intersection(other) {
      var O2 = aSet(this);
      var otherRec = getSetRecord(other);
      var result = new Set2();
      if (size(O2) > otherRec.size) {
        iterateSimple(otherRec.getIterator(), function(e) {
          if (has(O2, e))
            add(result, e);
        });
      } else {
        iterateSet(O2, function(e) {
          if (otherRec.includes(e))
            add(result, e);
        });
      }
      return result;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.intersection.v2.js
var require_es_set_intersection_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.intersection.v2.js"() {
    "use strict";
    var $2 = require_export();
    var fails = require_fails();
    var intersection = require_set_intersection();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var INCORRECT = !setMethodAcceptSetLike("intersection", function(result) {
      return result.size === 2 && result.has(1) && result.has(2);
    }) || fails(function() {
      return String(Array.from((/* @__PURE__ */ new Set([1, 2, 3])).intersection(/* @__PURE__ */ new Set([3, 2])))) !== "3,2";
    });
    $2({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
      intersection
    });
  }
});

// node_modules/core-js-pure/internals/set-is-disjoint-from.js
var require_set_is_disjoint_from = __commonJS({
  "node_modules/core-js-pure/internals/set-is-disjoint-from.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var has = require_set_helpers().has;
    var size = require_set_size();
    var getSetRecord = require_get_set_record();
    var iterateSet = require_set_iterate();
    var iterateSimple = require_iterate_simple();
    var iteratorClose = require_iterator_close();
    module.exports = function isDisjointFrom(other) {
      var O2 = aSet(this);
      var otherRec = getSetRecord(other);
      if (size(O2) <= otherRec.size)
        return iterateSet(O2, function(e) {
          if (otherRec.includes(e))
            return false;
        }, true) !== false;
      var iterator = otherRec.getIterator();
      return iterateSimple(iterator, function(e) {
        if (has(O2, e))
          return iteratorClose(iterator, "normal", false);
      }) !== false;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.is-disjoint-from.v2.js
var require_es_set_is_disjoint_from_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.is-disjoint-from.v2.js"() {
    "use strict";
    var $2 = require_export();
    var isDisjointFrom = require_set_is_disjoint_from();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var INCORRECT = !setMethodAcceptSetLike("isDisjointFrom", function(result) {
      return !result;
    });
    $2({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
      isDisjointFrom
    });
  }
});

// node_modules/core-js-pure/internals/set-is-subset-of.js
var require_set_is_subset_of = __commonJS({
  "node_modules/core-js-pure/internals/set-is-subset-of.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var size = require_set_size();
    var iterate = require_set_iterate();
    var getSetRecord = require_get_set_record();
    module.exports = function isSubsetOf(other) {
      var O2 = aSet(this);
      var otherRec = getSetRecord(other);
      if (size(O2) > otherRec.size)
        return false;
      return iterate(O2, function(e) {
        if (!otherRec.includes(e))
          return false;
      }, true) !== false;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.is-subset-of.v2.js
var require_es_set_is_subset_of_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.is-subset-of.v2.js"() {
    "use strict";
    var $2 = require_export();
    var isSubsetOf = require_set_is_subset_of();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var INCORRECT = !setMethodAcceptSetLike("isSubsetOf", function(result) {
      return result;
    });
    $2({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
      isSubsetOf
    });
  }
});

// node_modules/core-js-pure/internals/set-is-superset-of.js
var require_set_is_superset_of = __commonJS({
  "node_modules/core-js-pure/internals/set-is-superset-of.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var has = require_set_helpers().has;
    var size = require_set_size();
    var getSetRecord = require_get_set_record();
    var iterateSimple = require_iterate_simple();
    var iteratorClose = require_iterator_close();
    module.exports = function isSupersetOf(other) {
      var O2 = aSet(this);
      var otherRec = getSetRecord(other);
      if (size(O2) < otherRec.size)
        return false;
      var iterator = otherRec.getIterator();
      return iterateSimple(iterator, function(e) {
        if (!has(O2, e))
          return iteratorClose(iterator, "normal", false);
      }) !== false;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.is-superset-of.v2.js
var require_es_set_is_superset_of_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.is-superset-of.v2.js"() {
    "use strict";
    var $2 = require_export();
    var isSupersetOf = require_set_is_superset_of();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var INCORRECT = !setMethodAcceptSetLike("isSupersetOf", function(result) {
      return !result;
    });
    $2({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
      isSupersetOf
    });
  }
});

// node_modules/core-js-pure/internals/set-symmetric-difference.js
var require_set_symmetric_difference = __commonJS({
  "node_modules/core-js-pure/internals/set-symmetric-difference.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var SetHelpers = require_set_helpers();
    var clone = require_set_clone();
    var getSetRecord = require_get_set_record();
    var iterateSimple = require_iterate_simple();
    var add = SetHelpers.add;
    var has = SetHelpers.has;
    var remove = SetHelpers.remove;
    module.exports = function symmetricDifference(other) {
      var O2 = aSet(this);
      var keysIter = getSetRecord(other).getIterator();
      var result = clone(O2);
      iterateSimple(keysIter, function(e) {
        if (has(O2, e))
          remove(result, e);
        else
          add(result, e);
      });
      return result;
    };
  }
});

// node_modules/core-js-pure/internals/set-method-get-keys-before-cloning-detection.js
var require_set_method_get_keys_before_cloning_detection = __commonJS({
  "node_modules/core-js-pure/internals/set-method-get-keys-before-cloning-detection.js"(exports, module) {
    "use strict";
    module.exports = function(METHOD_NAME) {
      try {
        var baseSet = /* @__PURE__ */ new Set();
        var setLike = {
          size: 0,
          has: function() {
            return true;
          },
          keys: function() {
            return Object.defineProperty({}, "next", {
              get: function() {
                baseSet.clear();
                baseSet.add(4);
                return function() {
                  return { done: true };
                };
              }
            });
          }
        };
        var result = baseSet[METHOD_NAME](setLike);
        return result.size === 1 && result.values().next().value === 4;
      } catch (error) {
        return false;
      }
    };
  }
});

// node_modules/core-js-pure/modules/es.set.symmetric-difference.v2.js
var require_es_set_symmetric_difference_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.symmetric-difference.v2.js"() {
    "use strict";
    var $2 = require_export();
    var symmetricDifference = require_set_symmetric_difference();
    var setMethodGetKeysBeforeCloning = require_set_method_get_keys_before_cloning_detection();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var FORCED = !setMethodAcceptSetLike("symmetricDifference") || !setMethodGetKeysBeforeCloning("symmetricDifference");
    $2({ target: "Set", proto: true, real: true, forced: FORCED }, {
      symmetricDifference
    });
  }
});

// node_modules/core-js-pure/internals/set-union.js
var require_set_union = __commonJS({
  "node_modules/core-js-pure/internals/set-union.js"(exports, module) {
    "use strict";
    var aSet = require_a_set();
    var add = require_set_helpers().add;
    var clone = require_set_clone();
    var getSetRecord = require_get_set_record();
    var iterateSimple = require_iterate_simple();
    module.exports = function union(other) {
      var O2 = aSet(this);
      var keysIter = getSetRecord(other).getIterator();
      var result = clone(O2);
      iterateSimple(keysIter, function(it) {
        add(result, it);
      });
      return result;
    };
  }
});

// node_modules/core-js-pure/modules/es.set.union.v2.js
var require_es_set_union_v2 = __commonJS({
  "node_modules/core-js-pure/modules/es.set.union.v2.js"() {
    "use strict";
    var $2 = require_export();
    var union = require_set_union();
    var setMethodGetKeysBeforeCloning = require_set_method_get_keys_before_cloning_detection();
    var setMethodAcceptSetLike = require_set_method_accept_set_like();
    var FORCED = !setMethodAcceptSetLike("union") || !setMethodGetKeysBeforeCloning("union");
    $2({ target: "Set", proto: true, real: true, forced: FORCED }, {
      union
    });
  }
});

// node_modules/core-js-pure/es/set/index.js
var require_set = __commonJS({
  "node_modules/core-js-pure/es/set/index.js"(exports, module) {
    "use strict";
    require_es_array_iterator();
    require_es_object_to_string();
    require_es_set();
    require_es_set_difference_v2();
    require_es_set_intersection_v2();
    require_es_set_is_disjoint_from_v2();
    require_es_set_is_subset_of_v2();
    require_es_set_is_superset_of_v2();
    require_es_set_symmetric_difference_v2();
    require_es_set_union_v2();
    require_es_string_iterator();
    var path = require_path();
    module.exports = path.Set;
  }
});

// node_modules/core-js-pure/stable/set/index.js
var require_set2 = __commonJS({
  "node_modules/core-js-pure/stable/set/index.js"(exports, module) {
    "use strict";
    var parent = require_set();
    require_web_dom_collections_iterator();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/set.js
var require_set3 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/set.js"(exports, module) {
    module.exports = require_set2();
  }
});

// node_modules/core-js-pure/modules/es.object.keys.js
var require_es_object_keys = __commonJS({
  "node_modules/core-js-pure/modules/es.object.keys.js"() {
    "use strict";
    var $2 = require_export();
    var toObject = require_to_object();
    var nativeKeys = require_object_keys();
    var fails = require_fails();
    var FAILS_ON_PRIMITIVES = fails(function() {
      nativeKeys(1);
    });
    $2({ target: "Object", stat: true, forced: FAILS_ON_PRIMITIVES }, {
      keys: function keys(it) {
        return nativeKeys(toObject(it));
      }
    });
  }
});

// node_modules/core-js-pure/es/object/keys.js
var require_keys = __commonJS({
  "node_modules/core-js-pure/es/object/keys.js"(exports, module) {
    "use strict";
    require_es_object_keys();
    var path = require_path();
    module.exports = path.Object.keys;
  }
});

// node_modules/core-js-pure/stable/object/keys.js
var require_keys2 = __commonJS({
  "node_modules/core-js-pure/stable/object/keys.js"(exports, module) {
    "use strict";
    var parent = require_keys();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/object/keys.js
var require_keys3 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/object/keys.js"(exports, module) {
    module.exports = require_keys2();
  }
});

// node_modules/core-js-pure/internals/object-assign.js
var require_object_assign = __commonJS({
  "node_modules/core-js-pure/internals/object-assign.js"(exports, module) {
    "use strict";
    var DESCRIPTORS = require_descriptors();
    var uncurryThis = require_function_uncurry_this();
    var call = require_function_call();
    var fails = require_fails();
    var objectKeys = require_object_keys();
    var getOwnPropertySymbolsModule = require_object_get_own_property_symbols();
    var propertyIsEnumerableModule = require_object_property_is_enumerable();
    var toObject = require_to_object();
    var IndexedObject = require_indexed_object();
    var $assign = Object.assign;
    var defineProperty = Object.defineProperty;
    var concat = uncurryThis([].concat);
    module.exports = !$assign || fails(function() {
      if (DESCRIPTORS && $assign({ b: 1 }, $assign(defineProperty({}, "a", {
        enumerable: true,
        get: function() {
          defineProperty(this, "b", {
            value: 3,
            enumerable: false
          });
        }
      }), { b: 2 })).b !== 1)
        return true;
      var A = {};
      var B2 = {};
      var symbol = Symbol("assign detection");
      var alphabet = "abcdefghijklmnopqrst";
      A[symbol] = 7;
      alphabet.split("").forEach(function(chr) {
        B2[chr] = chr;
      });
      return $assign({}, A)[symbol] !== 7 || objectKeys($assign({}, B2)).join("") !== alphabet;
    }) ? function assign(target, source) {
      var T2 = toObject(target);
      var argumentsLength = arguments.length;
      var index = 1;
      var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;
      var propertyIsEnumerable = propertyIsEnumerableModule.f;
      while (argumentsLength > index) {
        var S2 = IndexedObject(arguments[index++]);
        var keys = getOwnPropertySymbols ? concat(objectKeys(S2), getOwnPropertySymbols(S2)) : objectKeys(S2);
        var length = keys.length;
        var j = 0;
        var key;
        while (length > j) {
          key = keys[j++];
          if (!DESCRIPTORS || call(propertyIsEnumerable, S2, key))
            T2[key] = S2[key];
        }
      }
      return T2;
    } : $assign;
  }
});

// node_modules/core-js-pure/modules/es.object.assign.js
var require_es_object_assign = __commonJS({
  "node_modules/core-js-pure/modules/es.object.assign.js"() {
    "use strict";
    var $2 = require_export();
    var assign = require_object_assign();
    $2({ target: "Object", stat: true, arity: 2, forced: Object.assign !== assign }, {
      assign
    });
  }
});

// node_modules/core-js-pure/es/object/assign.js
var require_assign = __commonJS({
  "node_modules/core-js-pure/es/object/assign.js"(exports, module) {
    "use strict";
    require_es_object_assign();
    var path = require_path();
    module.exports = path.Object.assign;
  }
});

// node_modules/core-js-pure/stable/object/assign.js
var require_assign2 = __commonJS({
  "node_modules/core-js-pure/stable/object/assign.js"(exports, module) {
    "use strict";
    var parent = require_assign();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/object/assign.js
var require_assign3 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/object/assign.js"(exports, module) {
    module.exports = require_assign2();
  }
});

// node_modules/core-js-pure/internals/array-fill.js
var require_array_fill = __commonJS({
  "node_modules/core-js-pure/internals/array-fill.js"(exports, module) {
    "use strict";
    var toObject = require_to_object();
    var toAbsoluteIndex = require_to_absolute_index();
    var lengthOfArrayLike = require_length_of_array_like();
    module.exports = function fill(value) {
      var O2 = toObject(this);
      var length = lengthOfArrayLike(O2);
      var argumentsLength = arguments.length;
      var index = toAbsoluteIndex(argumentsLength > 1 ? arguments[1] : void 0, length);
      var end = argumentsLength > 2 ? arguments[2] : void 0;
      var endPos = end === void 0 ? length : toAbsoluteIndex(end, length);
      while (endPos > index)
        O2[index++] = value;
      return O2;
    };
  }
});

// node_modules/core-js-pure/modules/es.array.fill.js
var require_es_array_fill = __commonJS({
  "node_modules/core-js-pure/modules/es.array.fill.js"() {
    "use strict";
    var $2 = require_export();
    var fill = require_array_fill();
    var addToUnscopables = require_add_to_unscopables();
    $2({ target: "Array", proto: true }, {
      fill
    });
    addToUnscopables("fill");
  }
});

// node_modules/core-js-pure/es/array/virtual/fill.js
var require_fill = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/fill.js"(exports, module) {
    "use strict";
    require_es_array_fill();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "fill");
  }
});

// node_modules/core-js-pure/es/instance/fill.js
var require_fill2 = __commonJS({
  "node_modules/core-js-pure/es/instance/fill.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_fill();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.fill;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.fill ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/fill.js
var require_fill3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/fill.js"(exports, module) {
    "use strict";
    var parent = require_fill2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/fill.js
var require_fill4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/fill.js"(exports, module) {
    module.exports = require_fill3();
  }
});

// node_modules/core-js-pure/modules/es.array.find.js
var require_es_array_find = __commonJS({
  "node_modules/core-js-pure/modules/es.array.find.js"() {
    "use strict";
    var $2 = require_export();
    var $find = require_array_iteration().find;
    var addToUnscopables = require_add_to_unscopables();
    var FIND = "find";
    var SKIPS_HOLES = true;
    if (FIND in [])
      Array(1)[FIND](function() {
        SKIPS_HOLES = false;
      });
    $2({ target: "Array", proto: true, forced: SKIPS_HOLES }, {
      find: function find(callbackfn) {
        return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
      }
    });
    addToUnscopables(FIND);
  }
});

// node_modules/core-js-pure/es/array/virtual/find.js
var require_find = __commonJS({
  "node_modules/core-js-pure/es/array/virtual/find.js"(exports, module) {
    "use strict";
    require_es_array_find();
    var getBuiltInPrototypeMethod = require_get_built_in_prototype_method();
    module.exports = getBuiltInPrototypeMethod("Array", "find");
  }
});

// node_modules/core-js-pure/es/instance/find.js
var require_find2 = __commonJS({
  "node_modules/core-js-pure/es/instance/find.js"(exports, module) {
    "use strict";
    var isPrototypeOf = require_object_is_prototype_of();
    var method = require_find();
    var ArrayPrototype = Array.prototype;
    module.exports = function(it) {
      var own = it.find;
      return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.find ? method : own;
    };
  }
});

// node_modules/core-js-pure/stable/instance/find.js
var require_find3 = __commonJS({
  "node_modules/core-js-pure/stable/instance/find.js"(exports, module) {
    "use strict";
    var parent = require_find2();
    module.exports = parent;
  }
});

// node_modules/@babel/runtime-corejs3/core-js-stable/instance/find.js
var require_find4 = __commonJS({
  "node_modules/@babel/runtime-corejs3/core-js-stable/instance/find.js"(exports, module) {
    module.exports = require_find3();
  }
});

// node_modules/bezier-easing/src/index.js
var require_src = __commonJS({
  "node_modules/bezier-easing/src/index.js"(exports, module) {
    var NEWTON_ITERATIONS = 4;
    var NEWTON_MIN_SLOPE = 1e-3;
    var SUBDIVISION_PRECISION = 1e-7;
    var SUBDIVISION_MAX_ITERATIONS = 10;
    var kSplineTableSize = 11;
    var kSampleStepSize = 1 / (kSplineTableSize - 1);
    var float32ArraySupported = typeof Float32Array === "function";
    function A(aA1, aA2) {
      return 1 - 3 * aA2 + 3 * aA1;
    }
    function B2(aA1, aA2) {
      return 3 * aA2 - 6 * aA1;
    }
    function C(aA1) {
      return 3 * aA1;
    }
    function calcBezier(aT, aA1, aA2) {
      return ((A(aA1, aA2) * aT + B2(aA1, aA2)) * aT + C(aA1)) * aT;
    }
    function getSlope(aT, aA1, aA2) {
      return 3 * A(aA1, aA2) * aT * aT + 2 * B2(aA1, aA2) * aT + C(aA1);
    }
    function binarySubdivide(aX, aA, aB, mX1, mX2) {
      var currentX, currentT, i2 = 0;
      do {
        currentT = aA + (aB - aA) / 2;
        currentX = calcBezier(currentT, mX1, mX2) - aX;
        if (currentX > 0) {
          aB = currentT;
        } else {
          aA = currentT;
        }
      } while (Math.abs(currentX) > SUBDIVISION_PRECISION && ++i2 < SUBDIVISION_MAX_ITERATIONS);
      return currentT;
    }
    function newtonRaphsonIterate(aX, aGuessT, mX1, mX2) {
      for (var i2 = 0; i2 < NEWTON_ITERATIONS; ++i2) {
        var currentSlope = getSlope(aGuessT, mX1, mX2);
        if (currentSlope === 0) {
          return aGuessT;
        }
        var currentX = calcBezier(aGuessT, mX1, mX2) - aX;
        aGuessT -= currentX / currentSlope;
      }
      return aGuessT;
    }
    function LinearEasing(x2) {
      return x2;
    }
    module.exports = function bezier(mX1, mY1, mX2, mY2) {
      if (!(0 <= mX1 && mX1 <= 1 && 0 <= mX2 && mX2 <= 1)) {
        throw new Error("bezier x values must be in [0, 1] range");
      }
      if (mX1 === mY1 && mX2 === mY2) {
        return LinearEasing;
      }
      var sampleValues = float32ArraySupported ? new Float32Array(kSplineTableSize) : new Array(kSplineTableSize);
      for (var i2 = 0; i2 < kSplineTableSize; ++i2) {
        sampleValues[i2] = calcBezier(i2 * kSampleStepSize, mX1, mX2);
      }
      function getTForX(aX) {
        var intervalStart = 0;
        var currentSample = 1;
        var lastSample = kSplineTableSize - 1;
        for (; currentSample !== lastSample && sampleValues[currentSample] <= aX; ++currentSample) {
          intervalStart += kSampleStepSize;
        }
        --currentSample;
        var dist = (aX - sampleValues[currentSample]) / (sampleValues[currentSample + 1] - sampleValues[currentSample]);
        var guessForT = intervalStart + dist * kSampleStepSize;
        var initialSlope = getSlope(guessForT, mX1, mX2);
        if (initialSlope >= NEWTON_MIN_SLOPE) {
          return newtonRaphsonIterate(aX, guessForT, mX1, mX2);
        } else if (initialSlope === 0) {
          return guessForT;
        } else {
          return binarySubdivide(aX, intervalStart, intervalStart + kSampleStepSize, mX1, mX2);
        }
      }
      return function BezierEasing(x2) {
        if (x2 === 0) {
          return 0;
        }
        if (x2 === 1) {
          return 1;
        }
        return calcBezier(getTForX(x2), mY1, mY2);
      };
    };
  }
});

// node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js
var import_is_array = __toESM(require_is_array7(), 1);

// node_modules/@babel/runtime-corejs3/helpers/esm/arrayLikeToArray.js
function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length)
    len = arr.length;
  for (var i2 = 0, arr2 = new Array(len); i2 < len; i2++) {
    arr2[i2] = arr[i2];
  }
  return arr2;
}

// node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js
function _arrayWithoutHoles(arr) {
  if ((0, import_is_array.default)(arr))
    return _arrayLikeToArray(arr);
}

// node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArray.js
var import_symbol = __toESM(require_symbol6(), 1);
var import_get_iterator_method = __toESM(require_get_iterator_method7(), 1);
var import_from = __toESM(require_from6(), 1);
function _iterableToArray(iter) {
  if (typeof import_symbol.default !== "undefined" && (0, import_get_iterator_method.default)(iter) != null || iter["@@iterator"] != null)
    return (0, import_from.default)(iter);
}

// node_modules/@babel/runtime-corejs3/helpers/esm/unsupportedIterableToArray.js
var import_slice = __toESM(require_slice7(), 1);
var import_from2 = __toESM(require_from6(), 1);
function _unsupportedIterableToArray(o2, minLen) {
  var _context;
  if (!o2)
    return;
  if (typeof o2 === "string")
    return _arrayLikeToArray(o2, minLen);
  var n = (0, import_slice.default)(_context = Object.prototype.toString.call(o2)).call(_context, 8, -1);
  if (n === "Object" && o2.constructor)
    n = o2.constructor.name;
  if (n === "Map" || n === "Set")
    return (0, import_from2.default)(o2);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))
    return _arrayLikeToArray(o2, minLen);
}

// node_modules/@babel/runtime-corejs3/helpers/esm/nonIterableSpread.js
function _nonIterableSpread() {
  throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// node_modules/@babel/runtime-corejs3/helpers/esm/toConsumableArray.js
function _toConsumableArray(arr) {
  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();
}

// node_modules/@babel/runtime-corejs3/helpers/esm/classCallCheck.js
function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError("Cannot call a class as a function");
  }
}

// node_modules/@babel/runtime-corejs3/helpers/esm/createClass.js
var import_define_property = __toESM(require_define_property6());
function _defineProperties(target, props) {
  for (var i2 = 0; i2 < props.length; i2++) {
    var descriptor = props[i2];
    descriptor.enumerable = descriptor.enumerable || false;
    descriptor.configurable = true;
    if ("value" in descriptor)
      descriptor.writable = true;
    (0, import_define_property.default)(target, descriptor.key, descriptor);
  }
}
function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps)
    _defineProperties(Constructor.prototype, protoProps);
  if (staticProps)
    _defineProperties(Constructor, staticProps);
  (0, import_define_property.default)(Constructor, "prototype", {
    writable: false
  });
  return Constructor;
}

// node_modules/@babel/runtime-corejs3/helpers/esm/defineProperty.js
var import_define_property2 = __toESM(require_define_property6());
function _defineProperty(obj, key, value) {
  if (key in obj) {
    (0, import_define_property2.default)(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

// node_modules/tvision-color/dist/esm/index.js
var import_map = __toESM(require_map4());
var import_includes = __toESM(require_includes5());
var import_chroma_js = __toESM(require_chroma());
var import_from3 = __toESM(require_from7());
var import_symbol4 = __toESM(require_symbol7());
var import_get_iterator_method3 = __toESM(require_get_iterator_method7());

// node_modules/@babel/runtime-corejs3/helpers/esm/typeof.js
var import_symbol2 = __toESM(require_symbol6());
var import_iterator = __toESM(require_iterator6());

// node_modules/tvision-color/dist/esm/index.js
var import_slice2 = __toESM(require_slice8());
var import_concat = __toESM(require_concat4());
var import_splice = __toESM(require_splice4());
var import_filter = __toESM(require_filter4());
var import_sort = __toESM(require_sort4());
var import_stringify = __toESM(require_stringify3());
var import_set = __toESM(require_set3());
var import_keys = __toESM(require_keys3());
var import_assign = __toESM(require_assign3());
var import_fill = __toESM(require_fill4());

// node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithHoles.js
var import_is_array2 = __toESM(require_is_array7(), 1);
function _arrayWithHoles(arr) {
  if ((0, import_is_array2.default)(arr))
    return arr;
}

// node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArrayLimit.js
var import_symbol3 = __toESM(require_symbol6(), 1);
var import_get_iterator_method2 = __toESM(require_get_iterator_method7(), 1);
function _iterableToArrayLimit(arr, i2) {
  var _i = arr == null ? null : typeof import_symbol3.default !== "undefined" && (0, import_get_iterator_method2.default)(arr) || arr["@@iterator"];
  if (_i == null)
    return;
  var _arr = [];
  var _n = true;
  var _d = false;
  var _s, _e;
  try {
    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value);
      if (i2 && _arr.length === i2)
        break;
    }
  } catch (err) {
    _d = true;
    _e = err;
  } finally {
    try {
      if (!_n && _i["return"] != null)
        _i["return"]();
    } finally {
      if (_d)
        throw _e;
    }
  }
  return _arr;
}

// node_modules/@babel/runtime-corejs3/helpers/esm/nonIterableRest.js
function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

// node_modules/@babel/runtime-corejs3/helpers/esm/slicedToArray.js
function _slicedToArray(arr, i2) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i2) || _unsupportedIterableToArray(arr, i2) || _nonIterableRest();
}

// node_modules/@material/material-color-utilities/dist/utils/math_utils.js
function signum(num) {
  if (num < 0) {
    return -1;
  } else if (num === 0) {
    return 0;
  } else {
    return 1;
  }
}
function lerp(start, stop, amount) {
  return (1 - amount) * start + amount * stop;
}
function clampInt(min, max, input) {
  if (input < min) {
    return min;
  } else if (input > max) {
    return max;
  }
  return input;
}
function clampDouble(min, max, input) {
  if (input < min) {
    return min;
  } else if (input > max) {
    return max;
  }
  return input;
}
function sanitizeDegreesInt(degrees) {
  degrees = degrees % 360;
  if (degrees < 0) {
    degrees = degrees + 360;
  }
  return degrees;
}
function sanitizeDegreesDouble(degrees) {
  degrees = degrees % 360;
  if (degrees < 0) {
    degrees = degrees + 360;
  }
  return degrees;
}
function differenceDegrees(a2, b2) {
  return 180 - Math.abs(Math.abs(a2 - b2) - 180);
}
function matrixMultiply(row, matrix) {
  const a2 = row[0] * matrix[0][0] + row[1] * matrix[0][1] + row[2] * matrix[0][2];
  const b2 = row[0] * matrix[1][0] + row[1] * matrix[1][1] + row[2] * matrix[1][2];
  const c2 = row[0] * matrix[2][0] + row[1] * matrix[2][1] + row[2] * matrix[2][2];
  return [a2, b2, c2];
}

// node_modules/@material/material-color-utilities/dist/utils/color_utils.js
var SRGB_TO_XYZ = [
  [0.41233895, 0.35762064, 0.18051042],
  [0.2126, 0.7152, 0.0722],
  [0.01932141, 0.11916382, 0.95034478]
];
var XYZ_TO_SRGB = [
  [
    3.2413774792388685,
    -1.5376652402851851,
    -0.49885366846268053
  ],
  [
    -0.9691452513005321,
    1.8758853451067872,
    0.04156585616912061
  ],
  [
    0.05562093689691305,
    -0.20395524564742123,
    1.0571799111220335
  ]
];
var WHITE_POINT_D65 = [95.047, 100, 108.883];
function argbFromRgb(red, green, blue) {
  return (255 << 24 | (red & 255) << 16 | (green & 255) << 8 | blue & 255) >>> 0;
}
function redFromArgb(argb) {
  return argb >> 16 & 255;
}
function greenFromArgb(argb) {
  return argb >> 8 & 255;
}
function blueFromArgb(argb) {
  return argb & 255;
}
function argbFromXyz(x2, y2, z2) {
  const matrix = XYZ_TO_SRGB;
  const linearR = matrix[0][0] * x2 + matrix[0][1] * y2 + matrix[0][2] * z2;
  const linearG = matrix[1][0] * x2 + matrix[1][1] * y2 + matrix[1][2] * z2;
  const linearB = matrix[2][0] * x2 + matrix[2][1] * y2 + matrix[2][2] * z2;
  const r = delinearized(linearR);
  const g2 = delinearized(linearG);
  const b2 = delinearized(linearB);
  return argbFromRgb(r, g2, b2);
}
function xyzFromArgb(argb) {
  const r = linearized(redFromArgb(argb));
  const g2 = linearized(greenFromArgb(argb));
  const b2 = linearized(blueFromArgb(argb));
  return matrixMultiply([r, g2, b2], SRGB_TO_XYZ);
}
function argbFromLstar(lstar) {
  const fy = (lstar + 16) / 116;
  const fz = fy;
  const fx = fy;
  const kappa = 24389 / 27;
  const epsilon = 216 / 24389;
  const lExceedsEpsilonKappa = lstar > 8;
  const y2 = lExceedsEpsilonKappa ? fy * fy * fy : lstar / kappa;
  const cubeExceedEpsilon = fy * fy * fy > epsilon;
  const x2 = cubeExceedEpsilon ? fx * fx * fx : lstar / kappa;
  const z2 = cubeExceedEpsilon ? fz * fz * fz : lstar / kappa;
  const whitePoint = WHITE_POINT_D65;
  return argbFromXyz(x2 * whitePoint[0], y2 * whitePoint[1], z2 * whitePoint[2]);
}
function lstarFromArgb(argb) {
  const y2 = xyzFromArgb(argb)[1] / 100;
  const e = 216 / 24389;
  if (y2 <= e) {
    return 24389 / 27 * y2;
  } else {
    const yIntermediate = Math.pow(y2, 1 / 3);
    return 116 * yIntermediate - 16;
  }
}
function yFromLstar(lstar) {
  const ke = 8;
  if (lstar > ke) {
    return Math.pow((lstar + 16) / 116, 3) * 100;
  } else {
    return lstar / (24389 / 27) * 100;
  }
}
function linearized(rgbComponent) {
  const normalized = rgbComponent / 255;
  if (normalized <= 0.040449936) {
    return normalized / 12.92 * 100;
  } else {
    return Math.pow((normalized + 0.055) / 1.055, 2.4) * 100;
  }
}
function delinearized(rgbComponent) {
  const normalized = rgbComponent / 100;
  let delinearized2 = 0;
  if (normalized <= 31308e-7) {
    delinearized2 = normalized * 12.92;
  } else {
    delinearized2 = 1.055 * Math.pow(normalized, 1 / 2.4) - 0.055;
  }
  return clampInt(0, 255, Math.round(delinearized2 * 255));
}
function whitePointD65() {
  return WHITE_POINT_D65;
}

// node_modules/@material/material-color-utilities/dist/hct/viewing_conditions.js
var ViewingConditions = class _ViewingConditions {
  /**
   * Parameters are intermediate values of the CAM16 conversion process. Their
   * names are shorthand for technical color science terminology, this class
   * would not benefit from documenting them individually. A brief overview
   * is available in the CAM16 specification, and a complete overview requires
   * a color science textbook, such as Fairchild's Color Appearance Models.
   */
  constructor(n, aw, nbb, ncb, c2, nc, rgbD, fl, fLRoot, z2) {
    this.n = n;
    this.aw = aw;
    this.nbb = nbb;
    this.ncb = ncb;
    this.c = c2;
    this.nc = nc;
    this.rgbD = rgbD;
    this.fl = fl;
    this.fLRoot = fLRoot;
    this.z = z2;
  }
  /**
   * Create ViewingConditions from a simple, physically relevant, set of
   * parameters.
   *
   * @param whitePoint White point, measured in the XYZ color space.
   *     default = D65, or sunny day afternoon
   * @param adaptingLuminance The luminance of the adapting field. Informally,
   *     how bright it is in the room where the color is viewed. Can be
   *     calculated from lux by multiplying lux by 0.0586. default = 11.72,
   *     or 200 lux.
   * @param backgroundLstar The lightness of the area surrounding the color.
   *     measured by L* in L*a*b*. default = 50.0
   * @param surround A general description of the lighting surrounding the
   *     color. 0 is pitch dark, like watching a movie in a theater. 1.0 is a
   *     dimly light room, like watching TV at home at night. 2.0 means there
   *     is no difference between the lighting on the color and around it.
   *     default = 2.0
   * @param discountingIlluminant Whether the eye accounts for the tint of the
   *     ambient lighting, such as knowing an apple is still red in green light.
   *     default = false, the eye does not perform this process on
   *       self-luminous objects like displays.
   */
  static make(whitePoint = whitePointD65(), adaptingLuminance = 200 / Math.PI * yFromLstar(50) / 100, backgroundLstar = 50, surround = 2, discountingIlluminant = false) {
    const xyz = whitePoint;
    const rW = xyz[0] * 0.401288 + xyz[1] * 0.650173 + xyz[2] * -0.051461;
    const gW = xyz[0] * -0.250268 + xyz[1] * 1.204414 + xyz[2] * 0.045854;
    const bW = xyz[0] * -2079e-6 + xyz[1] * 0.048952 + xyz[2] * 0.953127;
    const f = 0.8 + surround / 10;
    const c2 = f >= 0.9 ? lerp(0.59, 0.69, (f - 0.9) * 10) : lerp(0.525, 0.59, (f - 0.8) * 10);
    let d2 = discountingIlluminant ? 1 : f * (1 - 1 / 3.6 * Math.exp((-adaptingLuminance - 42) / 92));
    d2 = d2 > 1 ? 1 : d2 < 0 ? 0 : d2;
    const nc = f;
    const rgbD = [
      d2 * (100 / rW) + 1 - d2,
      d2 * (100 / gW) + 1 - d2,
      d2 * (100 / bW) + 1 - d2
    ];
    const k2 = 1 / (5 * adaptingLuminance + 1);
    const k4 = k2 * k2 * k2 * k2;
    const k4F = 1 - k4;
    const fl = k4 * adaptingLuminance + 0.1 * k4F * k4F * Math.cbrt(5 * adaptingLuminance);
    const n = yFromLstar(backgroundLstar) / whitePoint[1];
    const z2 = 1.48 + Math.sqrt(n);
    const nbb = 0.725 / Math.pow(n, 0.2);
    const ncb = nbb;
    const rgbAFactors = [
      Math.pow(fl * rgbD[0] * rW / 100, 0.42),
      Math.pow(fl * rgbD[1] * gW / 100, 0.42),
      Math.pow(fl * rgbD[2] * bW / 100, 0.42)
    ];
    const rgbA = [
      400 * rgbAFactors[0] / (rgbAFactors[0] + 27.13),
      400 * rgbAFactors[1] / (rgbAFactors[1] + 27.13),
      400 * rgbAFactors[2] / (rgbAFactors[2] + 27.13)
    ];
    const aw = (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]) * nbb;
    return new _ViewingConditions(n, aw, nbb, ncb, c2, nc, rgbD, fl, Math.pow(fl, 0.25), z2);
  }
};
ViewingConditions.DEFAULT = ViewingConditions.make();

// node_modules/@material/material-color-utilities/dist/hct/cam16.js
var Cam16 = class _Cam16 {
  /**
   * All of the CAM16 dimensions can be calculated from 3 of the dimensions, in
   * the following combinations:
   *      -  {j or q} and {c, m, or s} and hue
   *      - jstar, astar, bstar
   * Prefer using a static method that constructs from 3 of those dimensions.
   * This constructor is intended for those methods to use to return all
   * possible dimensions.
   *
   * @param hue
   * @param chroma informally, colorfulness / color intensity. like saturation
   *     in HSL, except perceptually accurate.
   * @param j lightness
   * @param q brightness; ratio of lightness to white point's lightness
   * @param m colorfulness
   * @param s saturation; ratio of chroma to white point's chroma
   * @param jstar CAM16-UCS J coordinate
   * @param astar CAM16-UCS a coordinate
   * @param bstar CAM16-UCS b coordinate
   */
  constructor(hue, chroma, j, q2, m2, s2, jstar, astar, bstar) {
    this.hue = hue;
    this.chroma = chroma;
    this.j = j;
    this.q = q2;
    this.m = m2;
    this.s = s2;
    this.jstar = jstar;
    this.astar = astar;
    this.bstar = bstar;
  }
  /**
   * CAM16 instances also have coordinates in the CAM16-UCS space, called J*,
   * a*, b*, or jstar, astar, bstar in code. CAM16-UCS is included in the CAM16
   * specification, and is used to measure distances between colors.
   */
  distance(other) {
    const dJ = this.jstar - other.jstar;
    const dA = this.astar - other.astar;
    const dB = this.bstar - other.bstar;
    const dEPrime = Math.sqrt(dJ * dJ + dA * dA + dB * dB);
    const dE = 1.41 * Math.pow(dEPrime, 0.63);
    return dE;
  }
  /**
   * @param argb ARGB representation of a color.
   * @return CAM16 color, assuming the color was viewed in default viewing
   *     conditions.
   */
  static fromInt(argb) {
    return _Cam16.fromIntInViewingConditions(argb, ViewingConditions.DEFAULT);
  }
  /**
   * @param argb ARGB representation of a color.
   * @param viewingConditions Information about the environment where the color
   *     was observed.
   * @return CAM16 color.
   */
  static fromIntInViewingConditions(argb, viewingConditions) {
    const red = (argb & 16711680) >> 16;
    const green = (argb & 65280) >> 8;
    const blue = argb & 255;
    const redL = linearized(red);
    const greenL = linearized(green);
    const blueL = linearized(blue);
    const x2 = 0.41233895 * redL + 0.35762064 * greenL + 0.18051042 * blueL;
    const y2 = 0.2126 * redL + 0.7152 * greenL + 0.0722 * blueL;
    const z2 = 0.01932141 * redL + 0.11916382 * greenL + 0.95034478 * blueL;
    const rC = 0.401288 * x2 + 0.650173 * y2 - 0.051461 * z2;
    const gC = -0.250268 * x2 + 1.204414 * y2 + 0.045854 * z2;
    const bC = -2079e-6 * x2 + 0.048952 * y2 + 0.953127 * z2;
    const rD = viewingConditions.rgbD[0] * rC;
    const gD = viewingConditions.rgbD[1] * gC;
    const bD = viewingConditions.rgbD[2] * bC;
    const rAF = Math.pow(viewingConditions.fl * Math.abs(rD) / 100, 0.42);
    const gAF = Math.pow(viewingConditions.fl * Math.abs(gD) / 100, 0.42);
    const bAF = Math.pow(viewingConditions.fl * Math.abs(bD) / 100, 0.42);
    const rA = signum(rD) * 400 * rAF / (rAF + 27.13);
    const gA = signum(gD) * 400 * gAF / (gAF + 27.13);
    const bA = signum(bD) * 400 * bAF / (bAF + 27.13);
    const a2 = (11 * rA + -12 * gA + bA) / 11;
    const b2 = (rA + gA - 2 * bA) / 9;
    const u2 = (20 * rA + 20 * gA + 21 * bA) / 20;
    const p2 = (40 * rA + 20 * gA + bA) / 20;
    const atan2 = Math.atan2(b2, a2);
    const atanDegrees = atan2 * 180 / Math.PI;
    const hue = atanDegrees < 0 ? atanDegrees + 360 : atanDegrees >= 360 ? atanDegrees - 360 : atanDegrees;
    const hueRadians = hue * Math.PI / 180;
    const ac = p2 * viewingConditions.nbb;
    const j = 100 * Math.pow(ac / viewingConditions.aw, viewingConditions.c * viewingConditions.z);
    const q2 = 4 / viewingConditions.c * Math.sqrt(j / 100) * (viewingConditions.aw + 4) * viewingConditions.fLRoot;
    const huePrime = hue < 20.14 ? hue + 360 : hue;
    const eHue = 0.25 * (Math.cos(huePrime * Math.PI / 180 + 2) + 3.8);
    const p1 = 5e4 / 13 * eHue * viewingConditions.nc * viewingConditions.ncb;
    const t = p1 * Math.sqrt(a2 * a2 + b2 * b2) / (u2 + 0.305);
    const alpha = Math.pow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73);
    const c2 = alpha * Math.sqrt(j / 100);
    const m2 = c2 * viewingConditions.fLRoot;
    const s2 = 50 * Math.sqrt(alpha * viewingConditions.c / (viewingConditions.aw + 4));
    const jstar = (1 + 100 * 7e-3) * j / (1 + 7e-3 * j);
    const mstar = 1 / 0.0228 * Math.log(1 + 0.0228 * m2);
    const astar = mstar * Math.cos(hueRadians);
    const bstar = mstar * Math.sin(hueRadians);
    return new _Cam16(hue, c2, j, q2, m2, s2, jstar, astar, bstar);
  }
  /**
   * @param j CAM16 lightness
   * @param c CAM16 chroma
   * @param h CAM16 hue
   */
  static fromJch(j, c2, h2) {
    return _Cam16.fromJchInViewingConditions(j, c2, h2, ViewingConditions.DEFAULT);
  }
  /**
   * @param j CAM16 lightness
   * @param c CAM16 chroma
   * @param h CAM16 hue
   * @param viewingConditions Information about the environment where the color
   *     was observed.
   */
  static fromJchInViewingConditions(j, c2, h2, viewingConditions) {
    const q2 = 4 / viewingConditions.c * Math.sqrt(j / 100) * (viewingConditions.aw + 4) * viewingConditions.fLRoot;
    const m2 = c2 * viewingConditions.fLRoot;
    const alpha = c2 / Math.sqrt(j / 100);
    const s2 = 50 * Math.sqrt(alpha * viewingConditions.c / (viewingConditions.aw + 4));
    const hueRadians = h2 * Math.PI / 180;
    const jstar = (1 + 100 * 7e-3) * j / (1 + 7e-3 * j);
    const mstar = 1 / 0.0228 * Math.log(1 + 0.0228 * m2);
    const astar = mstar * Math.cos(hueRadians);
    const bstar = mstar * Math.sin(hueRadians);
    return new _Cam16(h2, c2, j, q2, m2, s2, jstar, astar, bstar);
  }
  /**
   * @param jstar CAM16-UCS lightness.
   * @param astar CAM16-UCS a dimension. Like a* in L*a*b*, it is a Cartesian
   *     coordinate on the Y axis.
   * @param bstar CAM16-UCS b dimension. Like a* in L*a*b*, it is a Cartesian
   *     coordinate on the X axis.
   */
  static fromUcs(jstar, astar, bstar) {
    return _Cam16.fromUcsInViewingConditions(jstar, astar, bstar, ViewingConditions.DEFAULT);
  }
  /**
   * @param jstar CAM16-UCS lightness.
   * @param astar CAM16-UCS a dimension. Like a* in L*a*b*, it is a Cartesian
   *     coordinate on the Y axis.
   * @param bstar CAM16-UCS b dimension. Like a* in L*a*b*, it is a Cartesian
   *     coordinate on the X axis.
   * @param viewingConditions Information about the environment where the color
   *     was observed.
   */
  static fromUcsInViewingConditions(jstar, astar, bstar, viewingConditions) {
    const a2 = astar;
    const b2 = bstar;
    const m2 = Math.sqrt(a2 * a2 + b2 * b2);
    const M2 = (Math.exp(m2 * 0.0228) - 1) / 0.0228;
    const c2 = M2 / viewingConditions.fLRoot;
    let h2 = Math.atan2(b2, a2) * (180 / Math.PI);
    if (h2 < 0) {
      h2 += 360;
    }
    const j = jstar / (1 - (jstar - 100) * 7e-3);
    return _Cam16.fromJchInViewingConditions(j, c2, h2, viewingConditions);
  }
  /**
   *  @return ARGB representation of color, assuming the color was viewed in
   *     default viewing conditions, which are near-identical to the default
   *     viewing conditions for sRGB.
   */
  toInt() {
    return this.viewed(ViewingConditions.DEFAULT);
  }
  /**
   * @param viewingConditions Information about the environment where the color
   *     will be viewed.
   * @return ARGB representation of color
   */
  viewed(viewingConditions) {
    const alpha = this.chroma === 0 || this.j === 0 ? 0 : this.chroma / Math.sqrt(this.j / 100);
    const t = Math.pow(alpha / Math.pow(1.64 - Math.pow(0.29, viewingConditions.n), 0.73), 1 / 0.9);
    const hRad = this.hue * Math.PI / 180;
    const eHue = 0.25 * (Math.cos(hRad + 2) + 3.8);
    const ac = viewingConditions.aw * Math.pow(this.j / 100, 1 / viewingConditions.c / viewingConditions.z);
    const p1 = eHue * (5e4 / 13) * viewingConditions.nc * viewingConditions.ncb;
    const p2 = ac / viewingConditions.nbb;
    const hSin = Math.sin(hRad);
    const hCos = Math.cos(hRad);
    const gamma = 23 * (p2 + 0.305) * t / (23 * p1 + 11 * t * hCos + 108 * t * hSin);
    const a2 = gamma * hCos;
    const b2 = gamma * hSin;
    const rA = (460 * p2 + 451 * a2 + 288 * b2) / 1403;
    const gA = (460 * p2 - 891 * a2 - 261 * b2) / 1403;
    const bA = (460 * p2 - 220 * a2 - 6300 * b2) / 1403;
    const rCBase = Math.max(0, 27.13 * Math.abs(rA) / (400 - Math.abs(rA)));
    const rC = signum(rA) * (100 / viewingConditions.fl) * Math.pow(rCBase, 1 / 0.42);
    const gCBase = Math.max(0, 27.13 * Math.abs(gA) / (400 - Math.abs(gA)));
    const gC = signum(gA) * (100 / viewingConditions.fl) * Math.pow(gCBase, 1 / 0.42);
    const bCBase = Math.max(0, 27.13 * Math.abs(bA) / (400 - Math.abs(bA)));
    const bC = signum(bA) * (100 / viewingConditions.fl) * Math.pow(bCBase, 1 / 0.42);
    const rF = rC / viewingConditions.rgbD[0];
    const gF = gC / viewingConditions.rgbD[1];
    const bF = bC / viewingConditions.rgbD[2];
    const x2 = 1.86206786 * rF - 1.01125463 * gF + 0.14918677 * bF;
    const y2 = 0.38752654 * rF + 0.62144744 * gF - 897398e-8 * bF;
    const z2 = -0.0158415 * rF - 0.03412294 * gF + 1.04996444 * bF;
    const argb = argbFromXyz(x2, y2, z2);
    return argb;
  }
};

// node_modules/@material/material-color-utilities/dist/hct/hct.js
var Hct = class _Hct {
  constructor(internalHue, internalChroma, internalTone) {
    this.internalHue = internalHue;
    this.internalChroma = internalChroma;
    this.internalTone = internalTone;
    this.setInternalState(this.toInt());
  }
  /**
   * @param hue 0 <= hue < 360; invalid values are corrected.
   * @param chroma 0 <= chroma < ?; Informally, colorfulness. The color
   *     returned may be lower than the requested chroma. Chroma has a different
   *     maximum for any given hue and tone.
   * @param tone 0 <= tone <= 100; invalid values are corrected.
   * @return HCT representation of a color in default viewing conditions.
   */
  static from(hue, chroma, tone) {
    return new _Hct(hue, chroma, tone);
  }
  /**
   * @param argb ARGB representation of a color.
   * @return HCT representation of a color in default viewing conditions
   */
  static fromInt(argb) {
    const cam = Cam16.fromInt(argb);
    const tone = lstarFromArgb(argb);
    return new _Hct(cam.hue, cam.chroma, tone);
  }
  toInt() {
    return getInt(this.internalHue, this.internalChroma, this.internalTone);
  }
  /**
   * A number, in degrees, representing ex. red, orange, yellow, etc.
   * Ranges from 0 <= hue < 360.
   */
  get hue() {
    return this.internalHue;
  }
  /**
   * @param newHue 0 <= newHue < 360; invalid values are corrected.
   * Chroma may decrease because chroma has a different maximum for any given
   * hue and tone.
   */
  set hue(newHue) {
    this.setInternalState(getInt(sanitizeDegreesDouble(newHue), this.internalChroma, this.internalTone));
  }
  get chroma() {
    return this.internalChroma;
  }
  /**
   * @param newChroma 0 <= newChroma < ?
   * Chroma may decrease because chroma has a different maximum for any given
   * hue and tone.
   */
  set chroma(newChroma) {
    this.setInternalState(getInt(this.internalHue, newChroma, this.internalTone));
  }
  /** Lightness. Ranges from 0 to 100. */
  get tone() {
    return this.internalTone;
  }
  /**
   * @param newTone 0 <= newTone <= 100; invalid valids are corrected.
   * Chroma may decrease because chroma has a different maximum for any given
   * hue and tone.
   */
  set tone(newTone) {
    this.setInternalState(getInt(this.internalHue, this.internalChroma, newTone));
  }
  setInternalState(argb) {
    const cam = Cam16.fromInt(argb);
    const tone = lstarFromArgb(argb);
    this.internalHue = cam.hue;
    this.internalChroma = cam.chroma;
    this.internalTone = tone;
  }
};
var CHROMA_SEARCH_ENDPOINT = 0.4;
var DE_MAX = 1;
var DL_MAX = 0.2;
var LIGHTNESS_SEARCH_ENDPOINT = 0.01;
function getInt(hue, chroma, tone) {
  return getIntInViewingConditions(sanitizeDegreesDouble(hue), chroma, clampDouble(0, 100, tone), ViewingConditions.DEFAULT);
}
function getIntInViewingConditions(hue, chroma, tone, viewingConditions) {
  if (chroma < 1 || Math.round(tone) <= 0 || Math.round(tone) >= 100) {
    return argbFromLstar(tone);
  }
  hue = sanitizeDegreesDouble(hue);
  let high = chroma;
  let mid = chroma;
  let low = 0;
  let isFirstLoop = true;
  let answer = null;
  while (Math.abs(low - high) >= CHROMA_SEARCH_ENDPOINT) {
    const possibleAnswer = findCamByJ(hue, mid, tone);
    if (isFirstLoop) {
      if (possibleAnswer != null) {
        return possibleAnswer.viewed(viewingConditions);
      } else {
        isFirstLoop = false;
        mid = low + (high - low) / 2;
        continue;
      }
    }
    if (possibleAnswer === null) {
      high = mid;
    } else {
      answer = possibleAnswer;
      low = mid;
    }
    mid = low + (high - low) / 2;
  }
  if (answer === null) {
    return argbFromLstar(tone);
  }
  return answer.viewed(viewingConditions);
}
function findCamByJ(hue, chroma, tone) {
  let low = 0;
  let high = 100;
  let mid = 0;
  let bestdL = 1e3;
  let bestdE = 1e3;
  let bestCam = null;
  while (Math.abs(low - high) > LIGHTNESS_SEARCH_ENDPOINT) {
    mid = low + (high - low) / 2;
    const camBeforeClip = Cam16.fromJch(mid, chroma, hue);
    const clipped = camBeforeClip.toInt();
    const clippedLstar = lstarFromArgb(clipped);
    const dL = Math.abs(tone - clippedLstar);
    if (dL < DL_MAX) {
      const camClipped = Cam16.fromInt(clipped);
      const dE = camClipped.distance(Cam16.fromJch(camClipped.j, camClipped.chroma, hue));
      if (dE <= DE_MAX && dE <= bestdE) {
        bestdL = dL;
        bestdE = dE;
        bestCam = camClipped;
      }
    }
    if (bestdL === 0 && bestdE === 0) {
      break;
    }
    if (clippedLstar < tone) {
      low = mid;
    } else {
      high = mid;
    }
  }
  return bestCam;
}

// node_modules/@material/material-color-utilities/dist/score/score.js
var Score = class _Score {
  constructor() {
  }
  /**
   * Given a map with keys of colors and values of how often the color appears,
   * rank the colors based on suitability for being used for a UI theme.
   *
   * @param colorsToPopulation map with keys of colors and values of how often
   *     the color appears, usually from a source image.
   * @return Colors sorted by suitability for a UI theme. The most suitable
   *     color is the first item, the least suitable is the last. There will
   *     always be at least one color returned. If all the input colors
   *     were not suitable for a theme, a default fallback color will be
   *     provided, Google Blue.
   */
  static score(colorsToPopulation) {
    let populationSum = 0;
    for (const population of colorsToPopulation.values()) {
      populationSum += population;
    }
    const colorsToProportion = /* @__PURE__ */ new Map();
    const colorsToCam = /* @__PURE__ */ new Map();
    const hueProportions = new Array(360).fill(0);
    for (const [color, population] of colorsToPopulation.entries()) {
      const proportion = population / populationSum;
      colorsToProportion.set(color, proportion);
      const cam = Cam16.fromInt(color);
      colorsToCam.set(color, cam);
      const hue = Math.round(cam.hue);
      hueProportions[hue] += proportion;
    }
    const colorsToExcitedProportion = /* @__PURE__ */ new Map();
    for (const [color, cam] of colorsToCam.entries()) {
      const hue = Math.round(cam.hue);
      let excitedProportion = 0;
      for (let i2 = hue - 15; i2 < hue + 15; i2++) {
        const neighborHue = sanitizeDegreesInt(i2);
        excitedProportion += hueProportions[neighborHue];
      }
      colorsToExcitedProportion.set(color, excitedProportion);
    }
    const colorsToScore = /* @__PURE__ */ new Map();
    for (const [color, cam] of colorsToCam.entries()) {
      const proportion = colorsToExcitedProportion.get(color);
      const proportionScore = proportion * 100 * _Score.WEIGHT_PROPORTION;
      const chromaWeight = cam.chroma < _Score.TARGET_CHROMA ? _Score.WEIGHT_CHROMA_BELOW : _Score.WEIGHT_CHROMA_ABOVE;
      const chromaScore = (cam.chroma - _Score.TARGET_CHROMA) * chromaWeight;
      const score = proportionScore + chromaScore;
      colorsToScore.set(color, score);
    }
    const filteredColors = _Score.filter(colorsToExcitedProportion, colorsToCam);
    const dedupedColorsToScore = /* @__PURE__ */ new Map();
    for (const color of filteredColors) {
      let duplicateHue = false;
      const hue = colorsToCam.get(color).hue;
      for (const [alreadyChosenColor] of dedupedColorsToScore) {
        const alreadyChosenHue = colorsToCam.get(alreadyChosenColor).hue;
        if (differenceDegrees(hue, alreadyChosenHue) < 15) {
          duplicateHue = true;
          break;
        }
      }
      if (duplicateHue) {
        continue;
      }
      dedupedColorsToScore.set(color, colorsToScore.get(color));
    }
    const colorsByScoreDescending = Array.from(dedupedColorsToScore.entries());
    colorsByScoreDescending.sort((first, second) => {
      return second[1] - first[1];
    });
    const answer = colorsByScoreDescending.map((entry) => {
      return entry[0];
    });
    if (answer.length === 0) {
      answer.push(4282549748);
    }
    return answer;
  }
  static filter(colorsToExcitedProportion, colorsToCam) {
    const filtered = new Array();
    for (const [color, cam] of colorsToCam.entries()) {
      const proportion = colorsToExcitedProportion.get(color);
      if (cam.chroma >= _Score.CUTOFF_CHROMA && lstarFromArgb(color) >= _Score.CUTOFF_TONE && proportion >= _Score.CUTOFF_EXCITED_PROPORTION) {
        filtered.push(color);
      }
    }
    return filtered;
  }
};
Score.TARGET_CHROMA = 48;
Score.WEIGHT_PROPORTION = 0.7;
Score.WEIGHT_CHROMA_ABOVE = 0.3;
Score.WEIGHT_CHROMA_BELOW = 0.1;
Score.CUTOFF_CHROMA = 15;
Score.CUTOFF_TONE = 10;
Score.CUTOFF_EXCITED_PROPORTION = 0.01;

// node_modules/@material/material-color-utilities/dist/utils/string_utils.js
var hexFromArgb = (argb) => {
  const r = redFromArgb(argb);
  const g2 = greenFromArgb(argb);
  const b2 = blueFromArgb(argb);
  const outParts = [r.toString(16), g2.toString(16), b2.toString(16)];
  for (const [i2, part] of outParts.entries()) {
    if (part.length === 1) {
      outParts[i2] = "0" + part;
    }
  }
  return "#" + outParts.join("");
};
var argbFromHex = (hex) => {
  hex = hex.replace("#", "");
  const isThree = hex.length === 3;
  const isSix = hex.length === 6;
  const isEight = hex.length === 8;
  if (!isThree && !isSix && !isEight) {
    throw new Error("unexpected hex " + hex);
  }
  let r = 0;
  let g2 = 0;
  let b2 = 0;
  if (isThree) {
    r = parseIntHex(hex.slice(0, 1).repeat(2));
    g2 = parseIntHex(hex.slice(1, 2).repeat(2));
    b2 = parseIntHex(hex.slice(2, 3).repeat(2));
  } else if (isSix) {
    r = parseIntHex(hex.slice(0, 2));
    g2 = parseIntHex(hex.slice(2, 4));
    b2 = parseIntHex(hex.slice(4, 6));
  } else if (isEight) {
    r = parseIntHex(hex.slice(2, 4));
    g2 = parseIntHex(hex.slice(4, 6));
    b2 = parseIntHex(hex.slice(6, 8));
  }
  return (255 << 24 | (r & 255) << 16 | (g2 & 255) << 8 | b2 & 255) >>> 0;
};
function parseIntHex(value) {
  return parseInt(value, 16);
}

// node_modules/tvision-color/dist/esm/index.js
var import_find = __toESM(require_find4());
var import_bezier_easing = __toESM(require_src());
var S = function(r, e) {
  var t = function(r2) {
    return 0.017453292519943295 * r2;
  }, n = function(r2) {
    return 57.29577951308232 * r2;
  }, o2 = r[0], a2 = r[1], l2 = r[2], i2 = e[0], c2 = e[1], u2 = e[2], s2 = (o2 + i2) / 2, f = (Math.sqrt(Math.pow(a2, 2) + Math.pow(l2, 2)) + Math.sqrt(Math.pow(c2, 2) + Math.pow(u2, 2))) / 2, h2 = (1 - Math.sqrt(Math.pow(f, 7) / (Math.pow(f, 7) + Math.pow(25, 7)))) / 2, p2 = a2 * (1 + h2), m2 = c2 * (1 + h2), v2 = Math.sqrt(Math.pow(p2, 2) + Math.pow(l2, 2)), b2 = Math.sqrt(Math.pow(m2, 2) + Math.pow(u2, 2)), y2 = (v2 + b2) / 2, g2 = n(Math.atan2(l2, p2));
  g2 < 0 && (g2 += 360);
  var d2 = n(Math.atan2(u2, m2));
  d2 < 0 && (d2 += 360);
  var x2 = Math.abs(g2 - d2) > 180 ? (g2 + d2 + 360) / 2 : (g2 + d2) / 2, M2 = 1 - 0.17 * Math.cos(t(x2 - 30)) + 0.24 * Math.cos(t(2 * x2)) + 0.32 * Math.cos(t(3 * x2 + 6)) - 0.2 * Math.cos(t(4 * x2 - 63)), w = d2 - g2;
  Math.abs(w) > 180 && (d2 <= g2 ? w += 360 : w -= 360);
  var j = i2 - o2, A = b2 - v2;
  w = 2 * Math.sqrt(v2 * b2) * Math.sin(t(w) / 2);
  var C = 1 + 0.015 * Math.pow(s2 - 50, 2) / Math.sqrt(20 + Math.pow(s2 - 50, 2)), k2 = 1 + 0.045 * y2, R2 = 1 + 0.015 * y2 * M2, S2 = 30 * Math.exp(-Math.pow((x2 - 275) / 25, 2)), B2 = -(2 * Math.sqrt(Math.pow(y2, 7) / (Math.pow(y2, 7) + Math.pow(25, 7)))) * Math.sin(2 * t(S2));
  return Math.sqrt(Math.pow(j / (1 * C), 2) + Math.pow(A / (1 * k2), 2) + Math.pow(w / (1 * R2), 2) + B2 * (A / (1 * k2)) * (w / (1 * R2)));
};
function B(r, e) {
  var t = !(arguments.length > 2 && void 0 !== arguments[2]) || arguments[2], n = T(r), o2 = T(e), a2 = (n + 0.05) / (o2 + 0.05);
  return t && a2 < 1 && (a2 = 1 / a2), a2;
}
var T = function(r) {
  var e = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1], t = (0, import_map.default)(r).call(r, function(r2) {
    return r2 / 255;
  });
  return e && (t = E(t, false)), 0.2126 * t[0] + 0.7152 * t[1] + 0.0722 * t[2];
};
var E = function(r) {
  var e = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1];
  return (0, import_map.default)(r).call(r, function(r2) {
    var t, n = e ? r2 / 255 : r2;
    return t = n <= 0.03928 ? n / 12.92 : Math.pow((n + 0.055) / 1.055, 2.4), e ? 255 * t : t;
  });
};
function q(r, e) {
  var t = void 0 !== import_symbol4.default && (0, import_get_iterator_method3.default)(r) || r["@@iterator"];
  if (!t) {
    if (Array.isArray(r) || (t = function(r2, e2) {
      var t2;
      if (!r2)
        return;
      if ("string" == typeof r2)
        return P(r2, e2);
      var n2 = (0, import_slice2.default)(t2 = Object.prototype.toString.call(r2)).call(t2, 8, -1);
      "Object" === n2 && r2.constructor && (n2 = r2.constructor.name);
      if ("Map" === n2 || "Set" === n2)
        return (0, import_from3.default)(r2);
      if ("Arguments" === n2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2))
        return P(r2, e2);
    }(r)) || e && r && "number" == typeof r.length) {
      t && (r = t);
      var n = 0, o2 = function() {
      };
      return { s: o2, n: function() {
        return n >= r.length ? { done: true } : { done: false, value: r[n++] };
      }, e: function(r2) {
        throw r2;
      }, f: o2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var a2, l2 = true, i2 = false;
  return { s: function() {
    t = t.call(r);
  }, n: function() {
    var r2 = t.next();
    return l2 = r2.done, r2;
  }, e: function(r2) {
    i2 = true, a2 = r2;
  }, f: function() {
    try {
      l2 || null == t.return || t.return();
    } finally {
      if (i2)
        throw a2;
    }
  } };
}
function P(r, e) {
  (null == e || e > r.length) && (e = r.length);
  for (var t = 0, n = new Array(e); t < e; t++)
    n[t] = r[t];
  return n;
}
var N = 0.95047;
var z = 1;
var I = 1.08883;
var G = 0.137931034;
var L = 0.206896552;
var F = 0.12841855;
var D = 8856452e-9;
function H(r) {
  return Math.round(255 * (r <= 304e-5 ? 12.92 * r : 1.055 * Math.pow(r, 1 / 2.4) - 0.055));
}
function O(r) {
  return (r /= 255) <= 0.04045 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, 2.4);
}
function _(r) {
  return r > D ? Math.pow(r, 1 / 3) : r / F + G;
}
function Y(r) {
  return r > L ? r * r * r : F * (r - G);
}
function U(r) {
  var e = r[0], t = r[1], n = r[2], o2 = (e + 16) / 116, a2 = isNaN(t) ? o2 : o2 + t / 500, l2 = isNaN(n) ? o2 : o2 - n / 200;
  return o2 = z * Y(o2), [H(3.2404542 * (a2 = N * Y(a2)) - 1.5371385 * o2 - 0.4985314 * (l2 = I * Y(l2))), H(-0.969266 * a2 + 1.8760108 * o2 + 0.041556 * l2), n = H(0.0556434 * a2 - 0.2040259 * o2 + 1.0572252 * l2)];
}
function W(r) {
  var e;
  return (0, import_slice2.default)(e = "0".concat(r.toString(16))).call(e, -2);
}
var $ = 180 / Math.PI;
function J(r, e) {
  return S(r, e);
}
var K = { validateRgb: function(r) {
  var e = r[0], t = r[1], n = r[2];
  return e >= 0 && e <= 255 && t >= 0 && t <= 255 && n >= 0 && n <= 255;
}, labToRgb: U, labToRgbHex: function(r) {
  var e, t, n = U(r);
  return (0, import_concat.default)(e = (0, import_concat.default)(t = "#".concat(W(n[0]))).call(t, W(n[1]))).call(e, W(n[2]));
}, rgbToLab: function(r) {
  var e = function(r2) {
    var e2 = r2[0], t2 = r2[1], n2 = r2[2];
    return [_((0.4124564 * (e2 = O(e2)) + 0.3575761 * (t2 = O(t2)) + 0.1804375 * (n2 = O(n2))) / N), _((0.2126729 * e2 + 0.7151522 * t2 + 0.072175 * n2) / z), _((0.0193339 * e2 + 0.119192 * t2 + 0.9503041 * n2) / I)];
  }(r), t = e[0], n = e[1], o2 = 116 * n - 16;
  return [o2 < 0 ? 0 : o2, 500 * (t - n), 200 * (n - e[2])];
}, labToHcl: function(r) {
  var e = r[0], t = r[1], n = r[2], o2 = Math.sqrt(t * t + n * n), a2 = (Math.atan2(n, t) * $ + 360) % 360;
  return 0 === Math.round(1e4 * o2) && (a2 = NaN), [a2, o2, e];
}, diffSort: function(r, e) {
  for (var t, n, o2, a2, l2, i2, c2 = [(e = (0, import_slice2.default)(e).call(e)).shift()]; e.length > 0; ) {
    for (t = -1, n = -1 / 0, o2 = 0; o2 < e.length; o2++)
      for (a2 = e[o2], i2 = 0; i2 < c2.length; i2++)
        (l2 = r(a2, c2[i2])) > n && (n = l2, t = o2);
    c2.push(e[t]), (0, import_splice.default)(e).call(e, t, 1);
  }
  return c2;
}, distance: J, contrast: function(r, e) {
  return B(r, e, true);
}, sortColorsByComplementary: function(e, t) {
  var n, o2 = (0, import_chroma_js.default)(t).hsv(), a2 = _toConsumableArray(o2);
  a2[0] = (o2[0] + 180) % 360;
  var i2 = _toConsumableArray(o2);
  i2[0] = (o2[0] + 330) % 360;
  var c2 = function(r) {
    return (0, import_chroma_js.default)(r).lab();
  }, u2 = function(r) {
    return (0, import_chroma_js.default)(r, "hsv").lab();
  }, s2 = u2(a2), f = e[0], h2 = J(s2, c2(e[0]));
  e.forEach(function(r) {
    var e2 = J(s2, c2(r));
    e2 < h2 && (h2 = e2, f = r);
  });
  var p2 = u2(i2), m2 = (0, import_filter.default)(n = (0, import_sort.default)(e).call(e, function(r, e2) {
    return J(p2, c2(r)) - J(p2, c2(e2));
  })).call(n, function(r) {
    return r !== f;
  });
  return m2.unshift((0, import_chroma_js.default)(t).hex()), m2.push(f), m2;
}, sortColorsByDEThreshold: function(r, e) {
  var t, n, a2 = (0, import_slice2.default)(r).call(r);
  a2.unshift();
  for (var i2 = (0, import_map.default)(a2).call(a2, function(r2) {
    return import_chroma_js.default.hex(r2);
  }), c2 = (0, import_chroma_js.default)(e), u2 = (0, import_sort.default)(t = (0, import_slice2.default)(i2).call(i2)).call(t, function(r2, e2) {
    var t2 = r2.lab(), n2 = e2.lab(), o2 = c2.lab();
    return S(t2, o2) - S(n2, o2);
  }), s2 = c2, f = s2.lab(), v2 = (0, import_slice2.default)(u2).call(u2), y2 = [], g2 = [], d2 = []; d2.length < 2 || d2[d2.length - 1] !== d2[d2.length - 2]; ) {
    for (var x2 = 0, M2 = 0; M2 < v2.length; M2++) {
      var w = v2[M2], j = w.lab();
      S(j, f) >= 30 ? (g2.push(w), f = (s2 = w).lab(), x2 += 1) : y2.push(w);
    }
    d2.push(x2), v2 = y2, y2 = [];
  }
  for (var A = (0, import_slice2.default)(g2).call(g2), C = 0, k2 = A.length - 1; k2 > 0; k2--) {
    var R2 = S(A[k2].lab(), c2.lab());
    if (!(R2 < 30 || R2 > 55))
      break;
    A[k2], v2.unshift(A[k2]), C += 1;
  }
  if (C && (0, import_splice.default)(g2).call(g2, g2.length - C, C), v2.length) {
    var B2, T2 = q(v2);
    try {
      for (T2.s(); !(B2 = T2.n()).done; ) {
        for (var E2, P2, N2 = B2.value, z2 = [], I2 = (0, import_concat.default)(E2 = (0, import_concat.default)(P2 = [c2]).call(P2, g2)).call(E2, [c2]), G2 = 1; G2 < I2.length - 1; G2++) {
          var L2, F2 = I2[G2 - 1], D2 = I2[G2], H2 = S(F2.lab(), N2.lab()), O2 = S(D2.lab(), N2.lab()), _2 = (0, import_map.default)(L2 = [H2, O2]).call(L2, function(r2) {
            var e2 = r2 - 30;
            return e2 > 0 ? Math.pow(e2, 2) : Math.pow(Math.abs(e2), 4);
          }).reduce(function(r2, e2) {
            return r2 + e2;
          }, 0);
          z2.push({ pos: G2, deA: H2, deB: O2, deMul: _2 });
        }
        (0, import_sort.default)(z2).call(z2, function(r2, e2) {
          return r2.deMul - e2.deMul;
        }), (0, import_splice.default)(g2).call(g2, z2[0].pos - 1, 0, N2);
      }
    } catch (r2) {
      T2.e(r2);
    } finally {
      T2.f();
    }
  }
  var Y2 = (0, import_concat.default)(n = [c2]).call(n, g2);
  return (0, import_map.default)(Y2).call(Y2, function(r2) {
    return r2.hex();
  });
}, generateLockedColor: function(r) {
  return (0, import_chroma_js.default)(r).lab();
}, getW3RelativeLuminance: T, sRGBDecode: E };
var Q = function() {
  function r(t) {
    var n, o2;
    _classCallCheck(this, r), t || (n = 0, o2 = Math.pow(2, 31) - 1, t = n + Math.floor(Math.random() * (o2 - n + 1))), this.seed = t % 2147483647, this.seed <= 0 && (this.seed += 2147483646);
  }
  return _createClass(r, [{ key: "next", value: function() {
    return this.seed = 16807 * this.seed % 2147483647, this.seed;
  } }, { key: "nextFloat", value: function() {
    return (this.next() - 1) / 2147483646;
  } }]), r;
}();
var V = { random: [0, 360, 15, 98, 40, 98] };
var X = { bright: [[330, 360, 42, 88, 68, 92], [320, 330, 54, 85, 64, 91], [300, 320, 46, 75, 62, 89], [280, 300, 36, 84, 59, 92], [270, 280, 5, 88, 54, 87], [240, 270, 12, 74, 62, 95], [210, 240, 13, 82, 58, 93], [200, 210, 25, 88, 72, 82], [180, 200, 14, 70, 57, 90], [150, 180, 22, 76, 64, 90], [135, 150, 42, 85, 68, 82], [120, 135, 43, 95, 62, 93], [110, 120, 49, 89, 66, 92], [91, 110, 56, 79, 79, 93], [70, 91, 65, 95, 78, 99], [60, 70, 44, 84, 65, 98], [30, 60, 41, 79, 72, 92], [0, 30, 42, 88, 65, 88]], contrast: [[330, 360, 39, 69, 54, 74], [320, 330, 28, 86, 44, 65], [300, 320, 80, 100, 33, 53], [280, 300, 53, 90, 30, 60], [270, 280, 27, 47, 30, 55], [240, 270, 54, 82, 62, 80], [210, 240, 25, 88, 57, 90], [200, 210, 24, 47, 66, 86], [180, 200, 30, 62, 77, 88], [150, 180, 45, 86, 60, 87], [120, 150, 63, 92, 58, 87], [110, 120, 50, 88, 66, 90], [90, 110, 35, 83, 70, 95], [60, 90, 68, 89, 78, 94], [30, 60, 43, 87, 55, 85], [0, 30, 48, 69, 61, 71]], composed: [[330, 360, 55, 95, 35, 70], [320, 330, 25, 83, 44, 70], [300, 320, 55, 90, 40, 74], [280, 300, 38, 90, 42, 67], [270, 280, 38, 95, 28, 65], [240, 270, 20, 88, 34, 78], [210, 240, 15, 77, 40, 85], [200, 210, 10, 88, 33, 85], [180, 200, 25, 82, 43, 77], [150, 180, 35, 89, 44, 68], [135, 150, 15, 88, 42, 85], [120, 135, 15, 95, 46, 85], [110, 120, 45, 90, 44, 80], [90, 110, 35, 78, 67, 95], [70, 90, 65, 98, 80, 98], [60, 70, 42, 85, 45, 90], [30, 60, 53, 93, 45, 85], [0, 30, 43, 83, 38, 65]] };
var Z = { colorFilter: null, colorSpace: "default", quality: 100, ultraPrecision: false, seed: null };
var rr = new import_set.default((0, import_keys.default)(V));
function er(r, e) {
  var t;
  if ("number" != typeof r || r < 2)
    throw new Error("expecting a color count > 2.");
  e = function(r2) {
    var e2, t2 = (0, import_assign.default)({}, Z, r2);
    if ("number" != typeof t2.quality || isNaN(t2.quality) || t2.quality < 1)
      throw new Error("invalid `quality`. Expecting a number > 0.");
    if ("boolean" != typeof t2.ultraPrecision)
      throw new Error("invalid `ultraPrecision`. Expecting a boolean.");
    if (t2.seed && "number" != typeof t2.seed)
      throw new Error("invalid `seed`. Expecting an integer or a string.");
    if (t2.colorFilter || !t2.colorSpace)
      return t2;
    if ("string" == typeof t2.colorSpace) {
      if (!rr.has(t2.colorSpace))
        throw new Error('unknown `colorSpace` "'.concat(t2.colorSpace, '".'));
      e2 = V[t2.colorSpace];
    } else if (Array.isArray(t2.colorSpace)) {
      if (6 !== t2.colorSpace.length)
        throw new Error("expecting a `colorSpace` array of length 6 ([hmin, hmax, cmin, cmax, lmin, lmax]).");
      e2 = t2.colorSpace;
    } else
      e2 = [t2.colorSpace.hmin || 0, t2.colorSpace.hmax || 360, t2.colorSpace.cmin || 0, t2.colorSpace.cmax || 100, t2.colorSpace.lmin || 0, t2.colorSpace.lmax || 100];
    return t2.colorFilter = function(r3, t3) {
      var n2 = K.labToHcl(t3);
      return e2[0] < e2[1] ? n2[0] >= e2[0] && n2[0] <= e2[1] && n2[1] >= e2[2] && n2[1] <= e2[3] && n2[2] >= e2[4] && n2[2] <= e2[5] : (n2[0] >= e2[0] || n2[0] <= e2[1]) && n2[1] >= e2[2] && n2[1] <= e2[3] && n2[2] >= e2[4] && n2[2] <= e2[5];
    }, t2;
  }(e);
  var n = new Q(e.seed), a2 = K.distance, i2 = function(r2, t2) {
    return !!K.validateRgb(r2) && (!e.colorFilter || !!e.colorFilter(r2, t2));
  }, c2 = function(r2, e2, t2) {
    for (var n2, o2, a3 = new Array(e2), l2 = 0; l2 < e2; l2++) {
      do {
        n2 = [100 * r2(), 100 * (2 * r2() - 1), 100 * (2 * r2() - 1)], o2 = K.labToRgb(n2);
      } while (!t2(o2, n2));
      a3[l2] = n2;
    }
    return a3;
  }(function() {
    return n.nextFloat();
  }, r, i2);
  return null !== (t = e) && void 0 !== t && t.color && (c2[0] = (0, import_chroma_js.default)(e.color).lab()), function(r2, e2, t2, n2) {
    var o2, a3, l2, i3, c3 = [], u2 = [], s2 = 4, f = 10, p2 = 10;
    for (n2.ultraPrecision && (s2 = 1, f = 5, p2 = 5), o2 = 0; o2 <= 100; o2 += s2)
      for (a3 = -100; a3 <= 100; a3 += f)
        for (l2 = -100; l2 <= 100; l2 += p2)
          i3 = [o2, a3, l2], e2(K.labToRgb(i3), i3) && (c3.push(i3), u2.push(null));
    for (var m2, b2, y2, g2, d2, x2, M2, w, j, A = n2.quality || Z.quality, C = c3.length, k2 = t2.length; A-- > 0; ) {
      for (m2 = 0; m2 < C; m2++)
        for (y2 = c3[m2], d2 = 1 / 0, b2 = 0; b2 < k2; b2++)
          (g2 = r2(t2[b2], y2)) < d2 && (d2 = g2, u2[m2] = b2);
      for (x2 = (0, import_slice2.default)(c3).call(c3), b2 = 1; b2 < k2; b2++) {
        for (M2 = 0, w = [0, 0, 0], m2 = 0; m2 < C; m2++)
          u2[m2] === b2 && (M2 += 1, w[0] += c3[m2][0], w[1] += c3[m2][1], w[2] += c3[m2][2]);
        if (0 !== M2)
          if (w[0] /= M2, w[1] /= M2, w[2] /= M2, e2(K.labToRgb(w), w))
            t2[b2] = w;
          else {
            var R2 = x2.length > 0 ? x2 : c3;
            for (d2 = 1 / 0, j = -1, m2 = 0; m2 < R2.length; m2++)
              (g2 = r2(R2[m2], w)) < d2 && (d2 = g2, j = m2);
            t2[b2] = R2[j], x2 = (0, import_filter.default)(x2).call(x2, function(r3) {
              return r3[0] !== t2[b2][0] || r3[1] !== t2[b2][1] || r3[2] !== t2[b2][2];
            });
          }
      }
    }
  }(a2, i2, c2, e), c2 = K.diffSort(a2, c2), (0, import_map.default)(c2).call(c2, K.labToRgbHex);
}
function tr(r, e) {
  var t;
  return "string" == typeof e.colorSpace && X[e.colorSpace] ? function(r2, e2, t2) {
    var n = [];
    if (!Array.isArray(t2))
      return n;
    var o2 = function(r3) {
      return (0, import_filter.default)(t2).call(t2, function(e3) {
        return e3[0] <= r3 && r3 < e3[1];
      })[0];
    }, l2 = function(r3) {
      for (e2.colorSpace = r3; ; ) {
        var t3 = er(5, e2)[3];
        if (!(0, import_includes.default)(n).call(n, t3)) {
          n.push(t3);
          break;
        }
      }
    };
    if (t2.length === r2)
      t2.forEach(function(r3) {
        return l2(r3);
      });
    else
      for (var i2 = 360 / r2, c2 = 1; c2 <= r2; c2++) {
        var u2 = c2 * i2 - Math.random() * i2;
        l2(o2(u2));
      }
    return n;
  }(r - 1, e, X[e.colorSpace]) : (0, import_splice.default)(t = er(r, e)).call(t, 1, r);
}
function nr(r, e) {
  for (var t = (0, import_chroma_js.default)(r).lab(), n = 100 * (1 - 1 / e), a2 = n / (e - 1), i2 = 0.5 * (100 - n), c2 = function(r2, e2, t2) {
    for (var n2 = [], o2 = e2; o2 > r2; o2 -= t2)
      n2.push(o2);
    return n2;
  }(i2, i2 + e * a2, a2), u2 = 9999, s2 = 0; s2 < e; s2++) {
    var f = t[0] - c2[s2];
    Math.abs(f) < Math.abs(u2) && (u2 = f);
  }
  var h2 = function(r2, e2) {
    var t2 = { diff: 1 / 0, idx: -1 };
    e2.forEach(function(e3, n3) {
      Math.abs(r2 - e3) < t2.diff && (t2.diff = Math.abs(r2 - e3), t2.idx = n3);
    });
    var n2 = (0, import_concat.default)(e2).call(e2);
    return n2[t2.idx] = r2, n2;
  }(t[0], (0, import_map.default)(c2).call(c2, function(r2) {
    return r2 + u2;
  }));
  return (0, import_map.default)(h2).call(h2, function(r2) {
    return import_chroma_js.default.lab(r2, t[1], t[2]);
  });
}
var or = function(r, e) {
  var t, n, a2, i2 = "#ffffff" !== (0, import_chroma_js.default)(r).hex(), c2 = i2 ? e + 1 : e, u2 = (0, import_chroma_js.default)(r).set("lch.l", 17).hex(), s2 = (0, import_chroma_js.default)(r).set("lch.l", 9999).hex(), f = (0, import_map.default)(t = (0, import_fill.default)(n = Array(c2)).call(n, 1)).call(t, function(r2, e2) {
    return e2 / c2;
  }), h2 = import_chroma_js.default.scale((a2 = [s2, r, u2], (0, import_sort.default)(a2).call(a2, function(r2, e2) {
    return (0, import_chroma_js.default)(e2).get("lch.l") - (0, import_chroma_js.default)(r2).get("lch.l");
  }))).mode("hsl").correctLightness(), p2 = (0, import_map.default)(f).call(f, function(r2) {
    return h2(r2).hex();
  });
  return i2 && p2.shift(), p2;
};
var ar = function(e, t, n) {
  var a2 = or(e, t);
  "range" === n && (a2 = import_chroma_js.default.scale(nr(e, t)).correctLightness().colors(t));
  var c2 = (0, import_map.default)(a2).call(a2, function(r) {
    return (0, import_chroma_js.deltaE)(r, e);
  }), u2 = Math.min.apply(Math, _toConsumableArray(c2));
  return (0, import_map.default)(a2).call(a2, function(r) {
    return (0, import_chroma_js.deltaE)(r, e) === u2 ? e : r;
  });
};
function lr(r) {
  return "string" == typeof r ? Hct.fromInt(argbFromHex(r)) : (0, import_map.default)(r).call(r, function(r2) {
    return Hct.fromInt(argbFromHex(r2));
  });
}
function ir(r) {
  return Array.isArray(r) ? (0, import_map.default)(r).call(r, function(r2) {
    return hexFromArgb(Hct.from(r2.hue, r2.chroma, r2.tone).toInt());
  }) : hexFromArgb(Hct.from(r.hue, r.chroma, r.tone).toInt());
}
var cr = function(r) {
  var e = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3;
  return Math.round(r * Math.pow(10, e)) / Math.pow(10, e);
};
var ur = function(r, e, t, n) {
  var o2;
  if (!r)
    return (0, import_fill.default)(o2 = new Array(n)).call(o2, t);
  for (var a2 = [], l2 = function(r2, e2, t2) {
    var n2 = [];
    if (!r2 || r2.length < 2)
      return function(r3) {
        return e2;
      };
    for (var o3 = 1; o3 < r2.length; o3++) {
      var a3 = r2[o3 - 1], l3 = a3.x, i3 = a3.y, c2 = r2[o3], u2 = c2.x, s2 = (c2.y - i3) / (u2 - l3);
      n2.push({ k: s2, range: [l3, u2], base: i3, formula: function(r3, t3) {
        var n3 = this.base + (r3 - this.range[0]) * this.k, o4 = t3 / e2;
        return Math.min(100, Math.max(0, t3 + n3 * o4));
      } });
    }
    return function(r3, e3) {
      var o4, a4 = 10 * Math.min(r3, t2) / t2;
      return (null === (o4 = (0, import_find.default)(n2).call(n2, function(r4) {
        return r4.range[0] <= a4 && r4.range[1] >= a4;
      })) || void 0 === o4 ? void 0 : o4.formula(a4, e3)) || e3;
    };
  }(r, e || r[0].y || t, n), i2 = 1; i2 <= n; i2++)
    a2.unshift(cr(l2(i2, t)));
  return a2;
};
var sr = { Blue: { bezier: [0.28, 0.22, 0.86, 0.98], toneRange: [12, 96] }, Cyan: { bezier: [0.63, 0.58, 0.79, 0.92], toneRange: [13, 96] }, Green: { bezier: [0.85, 0.82, 0.84, 1], toneRange: [12, 96], piecewise: [{ x: 0, y: 0 }, { x: 5, y: 0 }, { x: 6, y: -5 }, { x: 7, y: -7 }, { x: 8, y: -18 }, { x: 9, y: -30 }, { x: 10, y: -40 }], piecewiseBase: 55 }, Lemon: { bezier: [0.62, 0.51, 0.39, 0.82], toneRange: [12, 98], piecewise: [{ x: 0, y: 0 }, { x: 7, y: 0 }, { x: 8, y: -10 }, { x: 9, y: -25 }, { x: 10, y: -50 }], piecewiseBase: 73 }, Yellow: { bezier: [0.3, 0.25, 0.5, 0.8], toneRange: [12, 97] }, Orange: { bezier: [0.31, 0.2, 0.7, 0.83], toneRange: [13, 96], piecewise: [{ x: 0, y: 0 }, { x: 6, y: 0 }, { x: 7, y: -11 }, { x: 8, y: -2 }, { x: 9, y: 0 }, { x: 10, y: 0 }], piecewiseBase: 59 }, Red: { bezier: [0.31, 0.26, 0.7, 0.83], toneRange: [12, 96], piecewise: [{ x: 0, y: -12 }, { x: 2, y: -12 }, { x: 3, y: -11 }, { x: 4, y: -7 }, { x: 5, y: -2 }, { x: 6, y: -6 }, { x: 7, y: 0 }, { x: 10, y: 0 }], piecewiseBase: 72 }, Pink: { bezier: [0.55, 0.45, 0.86, 0.99], toneRange: [12, 96], piecewise: [{ x: 0, y: 0 }, { x: 1, y: 0 }, { x: 2, y: -12 }, { x: 3, y: -11 }, { x: 4, y: -7 }, { x: 5, y: -2 }, { x: 6, y: 0 }, { x: 10, y: 0 }], piecewiseBase: 75 }, Purple: { bezier: [0.25, 0.13, 0.71, 0.88], toneRange: [10, 96] }, Lime: { bezier: [0.75, 0.68, 0.84, 0.99], toneRange: [12, 97], piecewise: [{ x: 0, y: 0 }, { x: 5, y: 0 }, { x: 6, y: -5 }, { x: 7, y: -7 }, { x: 8, y: -18 }, { x: 9, y: -30 }, { x: 10, y: -40 }], piecewiseBase: 55 }, Mint: { bezier: [0.46, 0.4, 0.84, 0.96], toneRange: [13, 97], piecewise: [{ x: 0, y: 0 }, { x: 5, y: 0 }, { x: 6, y: -5 }, { x: 7, y: -7 }, { x: 8, y: -18 }, { x: 9, y: -30 }, { x: 10, y: -40 }], piecewiseBase: 55 } };
var fr = { "#2ba471": "#00a870", "#d54941": "#d94941", "#43c0c6": "#00c8cF", "#8eba36": "#81b305", "#00c3c3": "#1fffff" };
function hr(r) {
  var e, t, n;
  if ("string" == typeof r) {
    e = r;
    for (var o2 = arguments.length, a2 = new Array(o2 > 1 ? o2 - 1 : 0), l2 = 1; l2 < o2; l2++)
      a2[l2 - 1] = arguments[l2];
    t = a2[0], n = false;
  } else
    e = r.color, t = r.step, n = !!r.remainInput;
  return pr({ color: e.toLowerCase(), step: t, remainInput: n });
}
function pr(r) {
  var e, t, n = r.color, a2 = r.step, i2 = r.remainInput, c2 = lr(n), u2 = fr[n] || n, s2 = lr(u2), f = sr[function(r2) {
    var e2 = r2 % 360;
    switch (true) {
      case (e2 >= 10 && e2 < 30):
        return "Red";
      case (e2 >= 30 && e2 < 60):
        return "Orange";
      case (e2 >= 60 && e2 < 102):
        return "Yellow";
      case (e2 >= 102 && e2 < 115):
        return "Lemon";
      case (e2 >= 115 && e2 < 130):
        return "Lime";
      case (e2 >= 130 && e2 < 180):
        return "Green";
      case (e2 >= 180 && e2 < 210):
        return "Mint";
      case (e2 >= 210 && e2 < 240):
        return "Cyan";
      case (e2 >= 240 && e2 < 285):
        return "Blue";
      case (e2 >= 285 && e2 < 325):
        return "Purple";
      default:
        return "Pink";
    }
  }(s2.hue)], h2 = function(r2) {
    var e2 = _slicedToArray(r2.range, 2), t2 = e2[0], n2 = e2[1], o2 = _slicedToArray(r2.bezierMeta, 4), a3 = o2[0], l2 = o2[1], i3 = o2[2], c3 = o2[3], u3 = r2.count, s3 = [];
    try {
      for (var f2 = (0, import_bezier_easing.default)(a3, l2, i3, c3), h3 = Math.abs(n2 - t2), p3 = 1 / (u3 - 1), m2 = 0; m2 < u3; m2++) {
        var v3 = f2(m2 * p3) * h3;
        if (t2 + v3 > n2)
          break;
        s3.unshift(cr(t2 + v3));
      }
    } catch (r3) {
    }
    return s3;
  }({ range: f.toneRange, bezierMeta: f.bezier, count: a2 });
  if (!h2.length)
    for (var p2 = _slicedToArray(f.toneRange, 2), v2 = p2[0], b2 = p2[1], y2 = (b2 - v2) / (a2 - 1), g2 = v2; g2 <= b2; g2 += y2)
      h2.push(g2);
  var d2 = function(r2) {
    var e2 = lr("#fff");
    return r2.hue === e2.hue && r2.chroma === e2.chroma && r2.tone === e2.tone;
  }(s2) ? (0, import_fill.default)(e = Array(a2)).call(e, 0) : ur(f.piecewise, f.piecewiseBase, s2.chroma, a2), x2 = function(r2) {
    var e2 = r2.hctColor, t2 = r2.originColor, n2 = r2.originHctColor, a3 = r2.chromas, i3 = r2.tones, c3 = r2.remainInput, u3 = (0, import_map.default)(i3).call(i3, function(r3, t3) {
      var n3 = null == a3 ? void 0 : a3[t3];
      return { hue: e2.hue, chroma: "number" == typeof n3 ? n3 : e2.chroma, tone: r3 };
    }), s3 = -1, f2 = 1 / 0, h3 = (0, import_chroma_js.default)(t2).lab();
    u3.forEach(function(r3, e3) {
      var t3 = ir(r3), n3 = K.distance((0, import_chroma_js.default)(t3).lab(), h3);
      n3 < f2 && (s3 = e3, f2 = n3);
    }), c3 && (0, import_splice.default)(u3).call(u3, s3, 1, n2);
    return { colors: u3, primary: s3 };
  }({ originColor: n, originHctColor: c2, color: u2, hctColor: s2, chromas: d2, tones: h2, remainInput: s2.tone > f.toneRange[1] || s2.tone < f.toneRange[0] || i2 });
  return { colors: (0, import_map.default)(t = x2.colors).call(t, function(r2) {
    return ir(r2);
  }), primary: x2.primary };
}
function mr(r) {
  var e = [96, 94, 92, 88, 80, 68, 58, 50, 40, 32, 24, 18, 14, 8], t = lr(r);
  return "#0052d9" === r.toLowerCase() && (e[8] = t.tone), (0, import_map.default)(e).call(e, function(r2) {
    return ir({ hue: t.hue, chroma: t.chroma, tone: r2 });
  });
}
var vr = function() {
  function r() {
    _classCallCheck(this, r);
  }
  return _createClass(r, null, [{ key: "getRandomPalette", value: function() {
    var r2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, e = r2.color, t = void 0 === e ? "#0052d9" : e, n = r2.colorGamut, o2 = r2.number, a2 = void 0 === o2 ? 8 : o2, l2 = r2.sortMethod, i2 = void 0 === l2 ? "DEThreshold" : l2, c2 = K.generateLockedColor(t), u2 = { color: c2, quality: 100, colorSpace: n || "bright" }, s2 = tr(a2, u2), f = ("DEThreshold" === i2 ? K.sortColorsByDEThreshold : K.sortColorsByComplementary)(s2, t);
    return f;
  } }, { key: "getPaletteByGradation", value: function() {
    var r2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : { colors: [] }, e = r2.colors, t = r2.step, n = void 0 === t ? 10 : t, a2 = r2.method, l2 = void 0 === a2 ? "darken" : a2;
    return (0, import_map.default)(e).call(e, function(r3) {
      return ar(r3, n, l2);
    });
  } }, { key: "getColorGradations", value: function() {
    var r2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : { colors: [] }, e = r2.colors, t = r2.step, n = void 0 === t ? 10 : t, a2 = r2.remainInput, l2 = void 0 !== a2 && a2;
    return (0, import_map.default)(e).call(e, function(r3) {
      return hr({ color: r3, step: n, remainInput: l2 });
    });
  } }, { key: "getNeutralColorGradation", value: function(r2) {
    return mr(r2);
  } }, { key: "getNeutralColor", value: function(r2) {
    return function(r3) {
      var e = mr(r3), t = mr("#000000");
      return (0, import_map.default)(e).call(e, function(r4, e2) {
        var n = lr(r4);
        return n.hue >= 102 && n.hue < 210 ? import_chroma_js.default.average([r4, t[e2]], "rgb", [0.08, 0.92]).hex() : import_chroma_js.default.average([r4, t[e2]], "rgb", [0.12, 0.88]).hex();
      });
    }(r2);
  } }]), r;
}();
var br = function() {
  function r() {
    _classCallCheck(this, r);
  }
  return _createClass(r, null, [{ key: "color2arr", value: function(r2) {
    var e;
    return (null === (e = r2.match(/(\d|\.)+%?/g)) || void 0 === e ? void 0 : (0, import_map.default)(e).call(e, function(r3) {
      return (0, import_includes.default)(r3).call(r3, "%") ? parseFloat(r3) / 100 : Number(r3);
    })) || [0, 0, 0];
  } }, { key: "similar", value: function(r2, e) {
    return K.distance((0, import_chroma_js.default)(r2).lab(), (0, import_chroma_js.default)(e).lab());
  } }, { key: "contrast", value: function(r2, e) {
    return K.contrast((0, import_chroma_js.default)(r2).rgb(), (0, import_chroma_js.default)(e).rgb());
  } }, { key: "scale", value: function(r2, e, t) {
    return import_chroma_js.default.scale([r2, e]).colors(t);
  } }, { key: "sortColorsByComplementary", value: function(r2, e) {
    return K.sortColorsByComplementary(r2, e);
  } }, { key: "sortColorsByDEThreshold", value: function(r2, e) {
    return K.sortColorsByDEThreshold(r2, e);
  } }, { key: "getRandomPalette", value: function(r2) {
    return vr.getRandomPalette(r2);
  } }, { key: "getPaletteByGradation", value: function(r2) {
    return vr.getPaletteByGradation(r2);
  } }, { key: "getRandomPaletteByGradation", value: function(r2) {
    return vr.getPaletteByGradation(r2);
  } }, { key: "getColorGradations", value: function(r2) {
    return vr.getColorGradations(r2);
  } }, { key: "getNeutralColorGradation", value: function(r2) {
    return vr.getNeutralColorGradation(r2);
  } }, { key: "getNeutralColor", value: function(r2) {
    return vr.getNeutralColor(r2);
  } }]), r;
}();
_defineProperty(br, "colorTransform", function(e) {
  var t, n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "hex", a2 = arguments.length > 2 ? arguments[2] : void 0;
  if ("name" === n)
    t = (0, import_chroma_js.default)(e)[a2]();
  else if ("hex" !== n && "string" == typeof e) {
    var i2 = br.color2arr(e);
    t = (0, import_chroma_js.default)(i2, n)[a2]();
  } else
    t = "hex" !== n && Array.isArray(e) ? (0, import_chroma_js.default)(_toConsumableArray(e), n)[a2]() : (0, import_chroma_js.default)(e)[a2]();
  return Array.isArray(t) ? (0, import_map.default)(t).call(t, function(r) {
    return parseFloat(r.toFixed(3));
  }) : t;
});
var yr = Object.freeze({ __proto__: null, wcag21: function(r, e) {
  var t = NaN;
  if ("number" == typeof r)
    r > 0 && r < 1 ? t = 1 / r : r > 1 && (t = r);
  else {
    var n = _slicedToArray(r, 2), o2 = n[0], a2 = n[1];
    import_chroma_js.default.valid(o2) && import_chroma_js.default.valid(o2) && (t = B((0, import_chroma_js.default)(o2).rgb(), (0, import_chroma_js.default)(a2).rgb()));
  }
  var i2 = { contrast: t, AA: false, AAA: false };
  switch (e) {
    case "normalText":
      t > 4.5 && (i2.AA = true), t > 7 && (i2.AAA = true);
      break;
    case "largeText":
      t > 3.1 && (i2.AA = true), t > 4.5 && (i2.AAA = true);
      break;
    case "nonText":
      t > 3.1 && (i2.AA = true), i2.AAA = null;
  }
  return i2;
} });
export {
  br as Color,
  yr as Inspect
};
/*! Bundled license information:

chroma-js/chroma.js:
  (**
   * chroma.js - JavaScript library for color conversions
   *
   * Copyright (c) 2011-2019, Gregor Aisch
   * All rights reserved.
   *
   * Redistribution and use in source and binary forms, with or without
   * modification, are permitted provided that the following conditions are met:
   *
   * 1. Redistributions of source code must retain the above copyright notice, this
   * list of conditions and the following disclaimer.
   *
   * 2. Redistributions in binary form must reproduce the above copyright notice,
   * this list of conditions and the following disclaimer in the documentation
   * and/or other materials provided with the distribution.
   *
   * 3. The name Gregor Aisch may not be used to endorse or promote products
   * derived from this software without specific prior written permission.
   *
   * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
   * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
   * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
   * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
   * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
   * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
   * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
   * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
   * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
   *
   * -------------------------------------------------------
   *
   * chroma.js includes colors from colorbrewer2.org, which are released under
   * the following license:
   *
   * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
   * and The Pennsylvania State University.
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   * http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing,
   * software distributed under the License is distributed on an
   * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
   * either express or implied. See the License for the specific
   * language governing permissions and limitations under the License.
   *
   * ------------------------------------------------------
   *
   * Named colors are taken from X11 Color Names.
   * http://www.w3.org/TR/css3-color/#svg-color
   *
   * @preserve
   *)

@material/material-color-utilities/dist/utils/math_utils.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/utils/color_utils.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/hct/viewing_conditions.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/hct/cam16.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/hct/hct.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/blend/blend.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/palettes/tonal_palette.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/palettes/core_palette.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/point_provider.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/lab_point_provider.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/quantizer_wsmeans.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/quantizer_map.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/quantizer_wu.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/quantize/quantizer_celebi.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/scheme/scheme.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/scheme/scheme_android.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/score/score.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/utils/string_utils.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/utils/image_utils.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/utils/theme_utils.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)

@material/material-color-utilities/dist/index.js:
  (**
   * @license
   * Copyright 2021 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   *)
*/
//# sourceMappingURL=tvision-color.js.map
