{"name": "frontend", "version": "0.3.1", "private": true, "main": "static-server.js", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vite build --mode release", "build:site": "vite build --mode site", "site:preview": "npm run build && cp -r dist _site", "preview": "vite preview --mode test", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test specified,work in process\"", "lint": "eslint ./src --ext ts,tsx", "lint:fix": "eslint ./src --ext ts,tsx --fix", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install"}, "devDependencies": {"@honkhonk/vite-plugin-svgr": "^1.1.0", "@types/echarts": "^4.9.13", "@types/lodash": "^4.14.178", "@types/mockjs": "^1.0.6", "@types/node": "^20.2.5", "@types/qrcode.react": "^1.0.2", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.3.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "husky": "^8.0.1", "less": "^4.1.3", "prettier": "^2.5.1", "typescript": "^4.8.4", "vite": "^4.5.10", "vite-plugin-mock": "^2.9.6"}, "dependencies": {"@reduxjs/toolkit": "^1.8.5", "@tencent/trpc-rpc-client": "^0.7.9", "@tencent/trpc-rpc-protocol": "^0.7.9", "@tencent/trpc-tools-codec": "^0.8.4", "@types/spark-md5": "^3.0.5", "assert": "^2.1.0", "axios": "^1.2.0", "axios-jsonp": "^1.0.4", "buffer": "^6.0.3", "classnames": "^2.3.1", "dayjs": "^1.10.7", "echarts": "^5.3.0", "echarts-for-react": "^3.0.2", "events": "^3.3.0", "koa": "^3.0.1", "koa-static": "^5.0.0", "lodash": "^4.17.21", "mockjs": "^1.1.0", "os-browserify": "^0.3.0", "path": "^0.12.7", "path-browserify": "^1.0.1", "process": "^0.11.10", "protobufjs": "^7.5.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^7.2.4", "react-router-dom": "^6.3.0", "spark-md5": "^3.0.2", "stream-browserify": "^3.0.0", "string_decoder": "^1.3.0", "tdesign-icons-react": "^0.2.0", "tdesign-react": "^1.1.10", "timers-browserify": "^2.0.12", "tty-browserify": "^0.0.1", "tvision-color": "^1.5.0", "url": "^0.11.4", "util": "^0.12.5", "vite-plugin-node-polyfills": "^0.24.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "预演工具CMS 前台"}